import React from 'react';
import styled from 'styled-components';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>a<PERSON><PERSON>, 
  <PERSON>aLock, 
  FaBell, 
  FaPalette, 
  FaQuestionCircle, 
  FaSignOutAlt,
  FaUsers,
  FaArchive,
  FaStar,
  FaCloudDownloadAlt,
  FaDesktop,
  FaInfoCircle,
  FaTimes
} from 'react-icons/fa';
import Logo from '../../assets/logo';

const DrawerContainer = styled.div`
  position: fixed;
  top: 0;
  left: ${props => props.$show ? '0' : '-320px'};
  width: 320px;
  height: 100vh;
  background-color: ${props => props.theme.colors.background};
  box-shadow: ${props => props.$show ? '0 0 15px rgba(0, 0, 0, 0.2)' : 'none'};
  z-index: 1000;
  transition: left 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
`;

const DrawerHeader = styled.div`
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.secondaryText};
  cursor: pointer;
  font-size: 20px;
  
  &:hover {
    color: ${props => props.theme.colors.text};
  }
`;

const DrawerContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
`;

const DrawerFooter = styled.div`
  padding: 15px 20px;
  border-top: 1px solid ${props => props.theme.colors.border};
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const AppVersion = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 12px;
`;

const LogoutButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: ${props => props.theme.colors.danger || '#e74c3c'};
  cursor: pointer;
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 4px;
  
  &:hover {
    background-color: ${props => props.theme.colors.danger || '#e74c3c'}10;
  }
`;

const MenuSection = styled.div`
  margin-bottom: 15px;
`;

const SectionTitle = styled.div`
  padding: 10px 20px;
  color: ${props => props.theme.colors.secondaryText};
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const MenuItem = styled.div`
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
  
  ${props => props.$active && `
    background-color: ${props.theme.colors.primary}10;
    border-left: 3px solid ${props.theme.colors.primary};
  `}
`;

const MenuIcon = styled.div`
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.icon};
`;

const MenuText = styled.div`
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.text};
  font-size: 14px;
  font-weight: ${props => props.$active ? '500' : 'normal'};
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 15px;
`;

const UserAvatar = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  
  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }
`;

const UserDetails = styled.div`
  display: flex;
  flex-direction: column;
`;

const UserName = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 16px;
`;

const UserStatus = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 12px;
  margin-top: 2px;
`;

const NavigationDrawer = ({ 
  show, 
  onClose, 
  theme, 
  activeSection, 
  onNavigate,
  user = { name: 'User Name', status: 'Online', avatar: null }
}) => {
  const handleNavigation = (section) => {
    onNavigate(section);
    onClose();
  };
  
  return (
    <DrawerContainer $show={show} theme={theme}>
      <DrawerHeader theme={theme}>
        <UserInfo>
          <UserAvatar theme={theme}>
            {user.avatar ? (
              <img src={user.avatar} alt={user.name} />
            ) : (
              <FaUser />
            )}
          </UserAvatar>
          <UserDetails>
            <UserName theme={theme}>{user.name}</UserName>
            <UserStatus theme={theme}>{user.status}</UserStatus>
          </UserDetails>
        </UserInfo>
        <CloseButton onClick={onClose} theme={theme}>
          <FaTimes />
        </CloseButton>
      </DrawerHeader>
      
      <DrawerContent>
        <MenuSection>
          <MenuItem 
            $active={activeSection === 'profile'} 
            onClick={() => handleNavigation('profile')}
            theme={theme}
          >
            <MenuIcon $active={activeSection === 'profile'} theme={theme}>
              <FaUser />
            </MenuIcon>
            <MenuText $active={activeSection === 'profile'} theme={theme}>
              Profile
            </MenuText>
          </MenuItem>
          
          <MenuItem 
            $active={activeSection === 'groups'} 
            onClick={() => handleNavigation('groups')}
            theme={theme}
          >
            <MenuIcon $active={activeSection === 'groups'} theme={theme}>
              <FaUsers />
            </MenuIcon>
            <MenuText $active={activeSection === 'groups'} theme={theme}>
              Groups
            </MenuText>
          </MenuItem>
          
          <MenuItem 
            $active={activeSection === 'archived'} 
            onClick={() => handleNavigation('archived')}
            theme={theme}
          >
            <MenuIcon $active={activeSection === 'archived'} theme={theme}>
              <FaArchive />
            </MenuIcon>
            <MenuText $active={activeSection === 'archived'} theme={theme}>
              Archived
            </MenuText>
          </MenuItem>
          
          <MenuItem 
            $active={activeSection === 'starred'} 
            onClick={() => handleNavigation('starred')}
            theme={theme}
          >
            <MenuIcon $active={activeSection === 'starred'} theme={theme}>
              <FaStar />
            </MenuIcon>
            <MenuText $active={activeSection === 'starred'} theme={theme}>
              Starred Messages
            </MenuText>
          </MenuItem>
        </MenuSection>
        
        <MenuSection>
          <SectionTitle theme={theme}>Settings</SectionTitle>
          
          <MenuItem 
            $active={activeSection === 'theme'} 
            onClick={() => handleNavigation('theme')}
            theme={theme}
          >
            <MenuIcon $active={activeSection === 'theme'} theme={theme}>
              <FaPalette />
            </MenuIcon>
            <MenuText $active={activeSection === 'theme'} theme={theme}>
              Theme & Appearance
            </MenuText>
          </MenuItem>
          
          <MenuItem 
            $active={activeSection === 'notifications'} 
            onClick={() => handleNavigation('notifications')}
            theme={theme}
          >
            <MenuIcon $active={activeSection === 'notifications'} theme={theme}>
              <FaBell />
            </MenuIcon>
            <MenuText $active={activeSection === 'notifications'} theme={theme}>
              Notifications
            </MenuText>
          </MenuItem>
          
          <MenuItem 
            $active={activeSection === 'privacy'} 
            onClick={() => handleNavigation('privacy')}
            theme={theme}
          >
            <MenuIcon $active={activeSection === 'privacy'} theme={theme}>
              <FaLock />
            </MenuIcon>
            <MenuText $active={activeSection === 'privacy'} theme={theme}>
              Privacy
            </MenuText>
          </MenuItem>
          
          <MenuItem 
            $active={activeSection === 'general'} 
            onClick={() => handleNavigation('general')}
            theme={theme}
          >
            <MenuIcon $active={activeSection === 'general'} theme={theme}>
              <FaCog />
            </MenuIcon>
            <MenuText $active={activeSection === 'general'} theme={theme}>
              General Settings
            </MenuText>
          </MenuItem>
        </MenuSection>
        
        <MenuSection>
          <SectionTitle theme={theme}>Other</SectionTitle>
          
          <MenuItem 
            onClick={() => handleNavigation('data')}
            theme={theme}
          >
            <MenuIcon theme={theme}>
              <FaCloudDownloadAlt />
            </MenuIcon>
            <MenuText theme={theme}>
              Data and Storage
            </MenuText>
          </MenuItem>
          
          <MenuItem 
            onClick={() => handleNavigation('web')}
            theme={theme}
          >
            <MenuIcon theme={theme}>
              <FaDesktop />
            </MenuIcon>
            <MenuText theme={theme}>
              Web Version
            </MenuText>
          </MenuItem>
          
          <MenuItem 
            onClick={() => handleNavigation('help')}
            theme={theme}
          >
            <MenuIcon theme={theme}>
              <FaQuestionCircle />
            </MenuIcon>
            <MenuText theme={theme}>
              Help
            </MenuText>
          </MenuItem>
          
          <MenuItem 
            onClick={() => handleNavigation('about')}
            theme={theme}
          >
            <MenuIcon theme={theme}>
              <FaInfoCircle />
            </MenuIcon>
            <MenuText theme={theme}>
              About
            </MenuText>
          </MenuItem>
        </MenuSection>
      </DrawerContent>
      
      <DrawerFooter theme={theme}>
        <AppVersion theme={theme}>GBChat v1.0.0</AppVersion>
        <LogoutButton theme={theme}>
          <FaSignOutAlt />
          Logout
        </LogoutButton>
      </DrawerFooter>
    </DrawerContainer>
  );
};

export default NavigationDrawer;
