import React, { useState } from 'react';
import styled from 'styled-components';
import {
  FaDesktop,
  FaQrcode,
  FaToggleOn,
  FaToggleOff,
  FaLock,
  FaInfoCircle,
  FaExclamationTriangle,
  FaLink,
  FaUnlink,
  FaSync,
  FaSignOutAlt
} from 'react-icons/fa';

const WebVersionContainer = styled.div`
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const SettingsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const SettingItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const SettingInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const IconWrapper = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary}20;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    color: ${props => props.color || props.theme.colors.primary};
    font-size: 18px;
  }
`;

const SettingText = styled.div`
  display: flex;
  flex-direction: column;
`;

const SettingName = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 0.95rem;
`;

const SettingDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 0.8rem;
  margin-top: 4px;
`;

const ToggleButton = styled.div`
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
  font-size: 1.5rem;
  cursor: pointer;
`;

const QRCodeContainer = styled.div`
  margin-top: 24px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const QRCodeTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  margin-bottom: 16px;

  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const QRCodeImage = styled.div`
  width: 200px;
  height: 200px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  position: relative;

  svg {
    font-size: 80px;
    color: ${props => props.theme.colors.primary};
  }

  &::before {
    content: '';
    position: absolute;
    width: 40px;
    height: 40px;
    border-top: 4px solid ${props => props.theme.colors.primary};
    border-left: 4px solid ${props => props.theme.colors.primary};
    top: 10px;
    left: 10px;
    border-top-left-radius: 8px;
  }

  &::after {
    content: '';
    position: absolute;
    width: 40px;
    height: 40px;
    border-bottom: 4px solid ${props => props.theme.colors.primary};
    border-right: 4px solid ${props => props.theme.colors.primary};
    bottom: 10px;
    right: 10px;
    border-bottom-right-radius: 8px;
  }
`;

const QRCodeInstructions = styled.div`
  text-align: center;
  color: ${props => props.theme.colors.secondaryText};
  font-size: 14px;
  line-height: 1.5;
  max-width: 300px;
`;

const InfoBox = styled.div`
  margin-top: 20px;
  padding: 15px;
  background-color: ${props => props.theme.colors.primary}10;
  border-left: 4px solid ${props => props.theme.colors.primary};
  border-radius: 4px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
`;

const InfoIcon = styled.div`
  color: ${props => props.theme.colors.primary};
  font-size: 18px;
  margin-top: 2px;
`;

const InfoText = styled.div`
  color: ${props => props.theme.colors.text};
  font-size: 14px;
  line-height: 1.5;
`;

const WarningBox = styled.div`
  margin-top: 20px;
  padding: 15px;
  background-color: #FFF3CD;
  border-left: 4px solid #FFC107;
  border-radius: 4px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
`;

const WarningIcon = styled.div`
  color: #FFC107;
  font-size: 18px;
  margin-top: 2px;
`;

const WarningText = styled.div`
  color: #856404;
  font-size: 14px;
  line-height: 1.5;
`;

const DevicesList = styled.div`
  margin-top: 24px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const DevicesTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  margin-bottom: 12px;

  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const DeviceItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid ${props => props.theme.colors.border};

  &:last-child {
    border-bottom: none;
  }
`;

const DeviceInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const DeviceName = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 14px;
`;

const DeviceStatus = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 12px;
  margin-top: 4px;
`;

const DeviceActions = styled.div`
  display: flex;
  gap: 8px;
`;

const DeviceButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.primary};
  cursor: pointer;
  font-size: 16px;

  &:hover {
    color: ${props => props.theme.colors.text};
  }
`;

const WebVersionSettings = ({ theme }) => {
  const [settings, setSettings] = useState({
    stayLoggedIn: true,
    notificationsEnabled: true,
    darkMode: false,
    soundEnabled: true
  });

  const [connectedDevices] = useState([
    { id: 1, name: 'Chrome on Windows', lastActive: 'Active now', current: true },
    { id: 2, name: 'Firefox on MacBook', lastActive: 'Last active 2 hours ago', current: false },
    { id: 3, name: 'Safari on iPhone', lastActive: 'Last active yesterday', current: false }
  ]);

  const toggleSetting = (setting) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  const handleLogoutDevice = (deviceId) => {
    if (window.confirm('Are you sure you want to log out this device?')) {
      console.log(`Logging out device ${deviceId}`);
      // Add logic to log out device here
    }
  };

  const handleRefreshQR = () => {
    console.log('Refreshing QR code...');
    // Add logic to refresh QR code here
  };

  return (
    <WebVersionContainer theme={theme}>
      <SectionTitle theme={theme}>Web Version</SectionTitle>

      <SettingsList>
        <SettingItem theme={theme} onClick={() => toggleSetting('stayLoggedIn')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#4CAF50">
              <FaDesktop />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Stay logged in</SettingName>
              <SettingDescription theme={theme}>
                Keep me logged in on this computer
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton
            $active={settings.stayLoggedIn}
            theme={theme}
          >
            {settings.stayLoggedIn ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>

        <SettingItem theme={theme} onClick={() => toggleSetting('notificationsEnabled')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#2196F3">
              <FaDesktop />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Desktop notifications</SettingName>
              <SettingDescription theme={theme}>
                Show notifications on this computer
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton
            $active={settings.notificationsEnabled}
            theme={theme}
          >
            {settings.notificationsEnabled ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>

        <SettingItem theme={theme} onClick={() => toggleSetting('darkMode')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#9C27B0">
              <FaDesktop />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Dark mode</SettingName>
              <SettingDescription theme={theme}>
                Use dark theme on web version
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton
            $active={settings.darkMode}
            theme={theme}
          >
            {settings.darkMode ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>

        <SettingItem theme={theme} onClick={() => toggleSetting('soundEnabled')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#FF9800">
              <FaDesktop />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Sound effects</SettingName>
              <SettingDescription theme={theme}>
                Play sounds for messages and calls
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton
            $active={settings.soundEnabled}
            theme={theme}
          >
            {settings.soundEnabled ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
      </SettingsList>

      <QRCodeContainer theme={theme}>
        <QRCodeTitle theme={theme}>
          <FaQrcode />
          Scan to connect
        </QRCodeTitle>

        <QRCodeImage theme={theme}>
          <FaQrcode />
        </QRCodeImage>

        <QRCodeInstructions theme={theme}>
          To use GBChat on your computer, scan this QR code with your phone to log in
        </QRCodeInstructions>

        <button
          style={{
            marginTop: '16px',
            padding: '8px 16px',
            backgroundColor: theme.colors.primary,
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
          onClick={handleRefreshQR}
        >
          <FaSync /> Refresh QR Code
        </button>
      </QRCodeContainer>

      <DevicesList theme={theme}>
        <DevicesTitle theme={theme}>
          <FaDesktop />
          Connected Devices
        </DevicesTitle>

        {connectedDevices.map(device => (
          <DeviceItem key={device.id} theme={theme}>
            <DeviceInfo>
              <DeviceName theme={theme}>
                {device.name} {device.current && '(This device)'}
              </DeviceName>
              <DeviceStatus theme={theme}>{device.lastActive}</DeviceStatus>
            </DeviceInfo>
            <DeviceActions>
              {!device.current && (
                <DeviceButton
                  theme={theme}
                  onClick={() => handleLogoutDevice(device.id)}
                  title="Log out this device"
                >
                  <FaSignOutAlt />
                </DeviceButton>
              )}
            </DeviceActions>
          </DeviceItem>
        ))}
      </DevicesList>

      <InfoBox theme={theme}>
        <InfoIcon theme={theme}>
          <FaInfoCircle />
        </InfoIcon>
        <InfoText theme={theme}>
          GBChat Web works by mirroring the content from your phone. All your messages stay on your phone.
        </InfoText>
      </InfoBox>

      <WarningBox>
        <WarningIcon>
          <FaExclamationTriangle />
        </WarningIcon>
        <WarningText>
          Make sure you're connecting to the official GBChat Web at web.gbchat.com to avoid security risks.
        </WarningText>
      </WarningBox>
    </WebVersionContainer>
  );
};

export default WebVersionSettings;
