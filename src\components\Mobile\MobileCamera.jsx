import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import {
  FaTimes,
  FaCamera,
  FaVideo,
  FaVideoSlash,
  FaFlash,
  FaBolt,
  FaSyncAlt,
  FaImage,
  FaCircle,
  FaStop,
  FaCheck,
  FaRedo,
  FaAdjust
} from 'react-icons/fa';
import MobileHaptics from './MobileHaptics';
import CameraEffects from './CameraEffects';

const CameraContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000;
  z-index: 9999;
  display: flex;
  flex-direction: column;
`;

const CameraHeader = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: linear-gradient(to bottom, rgba(0,0,0,0.7), transparent);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40px 20px 20px;
  z-index: 10;
`;

const HeaderButton = styled.button`
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }
`;

const CameraViewport = styled.div`
  flex: 1;
  position: relative;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
`;

const CameraPreview = styled.div`
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 48px;
`;

const CapturedImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const CameraControls = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 140px;
  background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 30px;
  z-index: 10;
`;

const SideControls = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
`;

const ControlButton = styled.button`
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;

  ${props => props.$active && `
    background: rgba(255, 255, 255, 0.4);
  `}

  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }
`;

const CaptureButton = styled.button`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid white;
  background: ${props => props.$recording ? '#ff4757' : 'rgba(255, 255, 255, 0.3)'};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }

  ${props => props.$recording && `
    animation: pulse 1s infinite;

    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(255, 71, 87, 0); }
      100% { box-shadow: 0 0 0 0 rgba(255, 71, 87, 0); }
    }
  `}
`;

const ModeSelector = styled.div`
  position: absolute;
  bottom: 160px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 25px;
  padding: 5px;
`;

const ModeButton = styled.button`
  background: ${props => props.$active ? 'rgba(255, 255, 255, 0.3)' : 'transparent'};
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const PreviewActions = styled.div`
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
`;

const ActionButton = styled.button`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  background: ${props => props.$variant === 'send' ? '#25d366' : 'rgba(255, 255, 255, 0.2)'};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
`;

const MobileCamera = ({ onClose, onCapture, theme }) => {
  const [mode, setMode] = useState('photo'); // 'photo' or 'video'
  const [isRecording, setIsRecording] = useState(false);
  const [flashEnabled, setFlashEnabled] = useState(false);
  const [frontCamera, setFrontCamera] = useState(false);
  const [capturedMedia, setCapturedMedia] = useState(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const [showEffects, setShowEffects] = useState(false);
  const [currentEffect, setCurrentEffect] = useState(null);
  const fileInputRef = useRef(null);

  const handleClose = () => {
    MobileHaptics.light();
    if (onClose) onClose();
  };

  const handleCapture = () => {
    MobileHaptics.medium();

    if (mode === 'photo') {
      // Simulate photo capture
      const mockPhoto = {
        type: 'photo',
        url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5QaG90bzwvdGV4dD48L3N2Zz4=',
        timestamp: Date.now()
      };
      setCapturedMedia(mockPhoto);
    } else {
      // Handle video recording
      if (!isRecording) {
        setIsRecording(true);
        // Start recording timer
        const timer = setInterval(() => {
          setRecordingTime(prev => prev + 1);
        }, 1000);

        // Auto stop after 30 seconds (demo)
        setTimeout(() => {
          setIsRecording(false);
          clearInterval(timer);
          const mockVideo = {
            type: 'video',
            url: 'mock-video-url',
            duration: recordingTime,
            timestamp: Date.now()
          };
          setCapturedMedia(mockVideo);
          setRecordingTime(0);
        }, 5000);
      } else {
        setIsRecording(false);
        setRecordingTime(0);
      }
    }
  };

  const handleFlashToggle = () => {
    MobileHaptics.light();
    setFlashEnabled(!flashEnabled);
  };

  const handleCameraFlip = () => {
    MobileHaptics.light();
    setFrontCamera(!frontCamera);
  };

  const handleGallery = () => {
    MobileHaptics.light();
    fileInputRef.current?.click();
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setCapturedMedia({
          type: file.type.startsWith('video/') ? 'video' : 'photo',
          url: e.target.result,
          timestamp: Date.now()
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSend = () => {
    MobileHaptics.success();
    if (onCapture && capturedMedia) {
      onCapture(capturedMedia);
    }
    handleClose();
  };

  const handleRetake = () => {
    MobileHaptics.light();
    setCapturedMedia(null);
    setRecordingTime(0);
  };

  const handleEffectsToggle = () => {
    MobileHaptics.light();
    setShowEffects(!showEffects);
  };

  const handleEffectApply = (effect) => {
    setCurrentEffect(effect);
  };

  const getEffectStyle = () => {
    if (!currentEffect) return {};

    let filter = currentEffect.filter || 'none';

    if (currentEffect.brightness !== 100) {
      filter += ` brightness(${currentEffect.brightness}%)`;
    }
    if (currentEffect.contrast !== 100) {
      filter += ` contrast(${currentEffect.contrast}%)`;
    }
    if (currentEffect.saturation !== 100) {
      filter += ` saturate(${currentEffect.saturation}%)`;
    }

    return { filter };
  };

  return (
    <CameraContainer>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*,video/*"
        style={{ display: 'none' }}
        onChange={handleFileSelect}
      />

      <CameraHeader>
        <HeaderButton onClick={handleClose}>
          <FaTimes />
        </HeaderButton>

        <div style={{ display: 'flex', gap: '8px' }}>
          <HeaderButton onClick={handleEffectsToggle}>
            <FaAdjust />
          </HeaderButton>

          <HeaderButton onClick={handleFlashToggle}>
            {flashEnabled ? <FaBolt /> : <FaBolt style={{ opacity: 0.5 }} />}
          </HeaderButton>
        </div>
      </CameraHeader>

      <CameraViewport>
        {capturedMedia ? (
          capturedMedia.type === 'photo' ? (
            <CapturedImage src={capturedMedia.url} alt="Captured" />
          ) : (
            <CameraPreview>
              <FaVideo size={64} />
            </CameraPreview>
          )
        ) : (
          <CameraPreview style={getEffectStyle()}>
            <FaCamera size={64} />
          </CameraPreview>
        )}
      </CameraViewport>

      {!capturedMedia && (
        <>
          <ModeSelector>
            <ModeButton
              $active={mode === 'photo'}
              onClick={() => setMode('photo')}
            >
              PHOTO
            </ModeButton>
            <ModeButton
              $active={mode === 'video'}
              onClick={() => setMode('video')}
            >
              VIDEO
            </ModeButton>
          </ModeSelector>

          <CameraControls>
            <SideControls>
              <ControlButton onClick={handleGallery}>
                <FaImage />
              </ControlButton>
            </SideControls>

            <CaptureButton
              $recording={isRecording}
              onClick={handleCapture}
            >
              {mode === 'photo' ? (
                <FaCircle />
              ) : isRecording ? (
                <FaStop />
              ) : (
                <FaCircle />
              )}
            </CaptureButton>

            <SideControls>
              <ControlButton onClick={handleCameraFlip}>
                <FaSyncAlt />
              </ControlButton>
            </SideControls>
          </CameraControls>
        </>
      )}

      {capturedMedia && (
        <PreviewActions>
          <ActionButton onClick={handleRetake}>
            <FaRedo />
          </ActionButton>

          <ActionButton $variant="send" onClick={handleSend}>
            <FaCheck />
          </ActionButton>
        </PreviewActions>
      )}

      <CameraEffects
        visible={showEffects}
        onClose={() => setShowEffects(false)}
        onEffectApply={handleEffectApply}
      />
    </CameraContainer>
  );
};

export default MobileCamera;
