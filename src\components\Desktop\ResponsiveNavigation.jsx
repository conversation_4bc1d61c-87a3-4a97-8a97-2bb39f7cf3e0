import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Fa<PERSON>ilter,
  FaSort,
  FaEllipsisV,
  FaPlus,
  FaArchive,
  FaStar,
  FaUsers,
  FaUserPlus,
  FaComments,
  FaPhone,
  FaVideo,
  FaCog,
  FaBell,
  FaUser,
  FaChevronDown,
  FaChevronUp,
  FaTimes,
  FaCheck
} from 'react-icons/fa';
import { useTheme } from '../../context/ThemeContext';

const NavigationContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: ${props => props.theme.colors.background};
`;

const SearchSection = styled.div`
  padding: 16px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const SearchBar = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 25px;
  padding: 12px 16px;
  gap: 12px;
  transition: all 0.2s ease;

  &:focus-within {
    background-color: ${props => props.theme.colors.background};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}40;
  }
`;

const SearchIcon = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 16px;
`;

const SearchInput = styled.input`
  flex: 1;
  border: none;
  background: none;
  outline: none;
  color: ${props => props.theme.colors.text};
  font-size: 14px;
  
  &::placeholder {
    color: ${props => props.theme.colors.secondaryText};
  }
`;

const SearchActions = styled.div`
  display: flex;
  gap: 8px;
`;

const SearchButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.secondaryText};
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    color: ${props => props.theme.colors.primary};
    background-color: ${props => props.theme.colors.primary}20;
  }
`;

const FilterSection = styled.div`
  padding: 12px 16px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const FilterTabs = styled.div`
  display: flex;
  gap: 4px;
  flex: 1;
`;

const FilterTab = styled.button`
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  background-color: ${props => props.$active ? props.theme.colors.primary : 'transparent'};
  color: ${props => props.$active ? 'white' : props.theme.colors.text};
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    background-color: ${props => props.$active ? props.theme.colors.primaryDark || props.theme.colors.primary : props.theme.colors.secondary};
  }
`;

const FilterActions = styled.div`
  display: flex;
  gap: 8px;
  margin-left: 12px;
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.secondaryText};
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    color: ${props => props.theme.colors.primary};
    background-color: ${props => props.theme.colors.primary}20;
  }
`;

const ContentArea = styled.div`
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
`;

const QuickActions = styled.div`
  padding: 16px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const QuickActionGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
`;

const QuickActionItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: ${props => props.theme.colors.secondary};

  &:hover {
    background-color: ${props => props.theme.colors.primary}20;
    transform: translateY(-2px);
  }
`;

const QuickActionIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
`;

const QuickActionLabel = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  text-align: center;
`;

const SectionHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.secondary};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${props => props.theme.colors.primary}10;
  }
`;

const SectionTitle = styled.div`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  display: flex;
  align-items: center;
  gap: 8px;
`;

const SectionToggle = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  transition: transform 0.2s ease;
  transform: ${props => props.$collapsed ? 'rotate(0deg)' : 'rotate(180deg)'};
`;

const SectionContent = styled.div`
  max-height: ${props => props.$collapsed ? '0' : '300px'};
  overflow: hidden;
  transition: max-height 0.3s ease;
`;

const ResponsiveNavigation = ({
  children,
  onSearch,
  onFilter,
  onSort,
  onNewChat,
  onNewGroup,
  onArchived,
  onStarred,
  activeFilter = 'all',
  searchQuery = '',
  showQuickActions = true
}) => {
  const { currentTheme } = useTheme();
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [sectionsCollapsed, setSectionsCollapsed] = useState({
    quickActions: false,
    recent: false,
    groups: false,
    archived: false
  });

  const filterTabs = [
    { id: 'all', label: 'All' },
    { id: 'unread', label: 'Unread' },
    { id: 'groups', label: 'Groups' },
    { id: 'contacts', label: 'Contacts' }
  ];

  const quickActions = [
    { id: 'newChat', icon: <FaComments />, label: 'New Chat', color: '#25d366', action: onNewChat },
    { id: 'newGroup', icon: <FaUsers />, label: 'New Group', color: '#2196f3', action: onNewGroup },
    { id: 'addContact', icon: <FaUserPlus />, label: 'Add Contact', color: '#ff9800' },
    { id: 'starred', icon: <FaStar />, label: 'Starred', color: '#ffc107', action: onStarred },
    { id: 'archived', icon: <FaArchive />, label: 'Archived', color: '#607d8b', action: onArchived },
    { id: 'calls', icon: <FaPhone />, label: 'Calls', color: '#4caf50' }
  ];

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setLocalSearchQuery(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  const handleFilterChange = (filterId) => {
    if (onFilter) {
      onFilter(filterId);
    }
  };

  const toggleSection = (sectionId) => {
    setSectionsCollapsed(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  const handleQuickAction = (action) => {
    if (action && typeof action === 'function') {
      action();
    }
  };

  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  return (
    <NavigationContainer theme={currentTheme}>
      <SearchSection theme={currentTheme}>
        <SearchBar theme={currentTheme}>
          <SearchIcon theme={currentTheme}>
            <FaSearch />
          </SearchIcon>
          <SearchInput
            type="text"
            placeholder="Search chats, messages..."
            value={localSearchQuery}
            onChange={handleSearchChange}
            theme={currentTheme}
          />
          <SearchActions theme={currentTheme}>
            <SearchButton onClick={() => onFilter && onFilter('unread')} title="Filter" theme={currentTheme}>
              <FaFilter />
            </SearchButton>
            <SearchButton onClick={() => onSort && onSort()} title="Sort" theme={currentTheme}>
              <FaSort />
            </SearchButton>
          </SearchActions>
        </SearchBar>
      </SearchSection>

      <FilterSection theme={currentTheme}>
        <FilterTabs theme={currentTheme}>
          {filterTabs.map(tab => (
            <FilterTab
              key={tab.id}
              $active={activeFilter === tab.id}
              onClick={() => handleFilterChange(tab.id)}
              theme={currentTheme}
            >
              {tab.label}
            </FilterTab>
          ))}
        </FilterTabs>
        <FilterActions theme={currentTheme}>
          <ActionButton onClick={() => onNewChat && onNewChat()} title="New Chat" theme={currentTheme}>
            <FaPlus />
          </ActionButton>
          <ActionButton title="More Options" theme={currentTheme}>
            <FaEllipsisV />
          </ActionButton>
        </FilterActions>
      </FilterSection>

      <ContentArea theme={currentTheme}>
        {showQuickActions && (
          <>
            <SectionHeader onClick={() => toggleSection('quickActions')} theme={currentTheme}>
              <SectionTitle theme={currentTheme}>
                Quick Actions
              </SectionTitle>
              <SectionToggle $collapsed={sectionsCollapsed.quickActions} theme={currentTheme}>
                <FaChevronDown />
              </SectionToggle>
            </SectionHeader>
            <SectionContent $collapsed={sectionsCollapsed.quickActions} theme={currentTheme}>
              <QuickActions theme={currentTheme}>
                <QuickActionGrid theme={currentTheme}>
                  {quickActions.map(action => (
                    <QuickActionItem
                      key={action.id}
                      onClick={() => handleQuickAction(action.action)}
                      theme={currentTheme}
                    >
                      <QuickActionIcon color={action.color} theme={currentTheme}>
                        {action.icon}
                      </QuickActionIcon>
                      <QuickActionLabel theme={currentTheme}>{action.label}</QuickActionLabel>
                    </QuickActionItem>
                  ))}
                </QuickActionGrid>
              </QuickActions>
            </SectionContent>
          </>
        )}

        {children}
      </ContentArea>
    </NavigationContainer>
  );
};

export default ResponsiveNavigation;
