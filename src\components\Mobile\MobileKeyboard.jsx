import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

const KeyboardContainer = styled.div`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: ${props => props.theme.colors.secondary};
  border-top: 1px solid ${props => props.theme.colors.border};
  transform: translateY(${props => props.$visible ? '0' : '100%'});
  transition: transform 0.3s ease-in-out;
  z-index: 1000;
  display: none;
  
  @media (max-width: 768px) {
    display: block;
  }
`;

const KeyboardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: ${props => props.theme.colors.background};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const KeyboardTitle = styled.div`
  font-size: 14px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
`;

const KeyboardActions = styled.div`
  display: flex;
  gap: 12px;
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.primary};
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const KeyboardContent = styled.div`
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
`;

const QuickActions = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
`;

const QuickActionButton = styled.button`
  padding: 8px 12px;
  background-color: ${props => props.theme.colors.background};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 20px;
  color: ${props => props.theme.colors.text};
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: ${props => props.theme.colors.primary};
    color: white;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const SuggestionsList = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  overflow-x: auto;
  padding-bottom: 4px;
`;

const SuggestionItem = styled.button`
  padding: 6px 12px;
  background-color: ${props => props.theme.colors.primary}20;
  border: 1px solid ${props => props.theme.colors.primary}40;
  border-radius: 16px;
  color: ${props => props.theme.colors.primary};
  font-size: 12px;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: ${props => props.theme.colors.primary};
    color: white;
  }
`;

const MobileKeyboard = ({ 
  theme, 
  visible, 
  onClose, 
  onTextInsert, 
  currentText = '',
  placeholder = 'Type a message...' 
}) => {
  const [suggestions, setSuggestions] = useState([]);
  const [quickActions] = useState([
    'Hello!', 'How are you?', 'Thanks!', 'OK', 'Yes', 'No', 
    'See you later', 'Good morning', 'Good night', 'Call me'
  ]);

  useEffect(() => {
    if (currentText.length > 2) {
      // Generate suggestions based on current text
      const textSuggestions = [
        `${currentText} 👍`,
        `${currentText}!`,
        `${currentText}?`,
        `${currentText} 😊`
      ];
      setSuggestions(textSuggestions);
    } else {
      setSuggestions([]);
    }
  }, [currentText]);

  const handleQuickAction = (text) => {
    if (onTextInsert) {
      onTextInsert(text);
    }
  };

  const handleSuggestion = (suggestion) => {
    if (onTextInsert) {
      onTextInsert(suggestion);
    }
  };

  const handleDone = () => {
    if (onClose) {
      onClose();
    }
  };

  if (!visible) return null;

  return (
    <KeyboardContainer theme={theme} $visible={visible}>
      <KeyboardHeader theme={theme}>
        <KeyboardTitle theme={theme}>Quick Actions</KeyboardTitle>
        <KeyboardActions>
          <ActionButton theme={theme} onClick={handleDone}>
            Done
          </ActionButton>
        </KeyboardActions>
      </KeyboardHeader>
      
      <KeyboardContent>
        {suggestions.length > 0 && (
          <>
            <div style={{ 
              fontSize: '12px', 
              color: theme.colors.secondaryText, 
              marginBottom: '8px' 
            }}>
              Suggestions:
            </div>
            <SuggestionsList>
              {suggestions.map((suggestion, index) => (
                <SuggestionItem
                  key={index}
                  theme={theme}
                  onClick={() => handleSuggestion(suggestion)}
                >
                  {suggestion}
                </SuggestionItem>
              ))}
            </SuggestionsList>
          </>
        )}
        
        <div style={{ 
          fontSize: '12px', 
          color: theme.colors.secondaryText, 
          marginBottom: '8px' 
        }}>
          Quick Messages:
        </div>
        <QuickActions>
          {quickActions.map((action, index) => (
            <QuickActionButton
              key={index}
              theme={theme}
              onClick={() => handleQuickAction(action)}
            >
              {action}
            </QuickActionButton>
          ))}
        </QuickActions>
      </KeyboardContent>
    </KeyboardContainer>
  );
};

export default MobileKeyboard;
