import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../context/ThemeContext';
import { usePrivacy } from '../../context/PrivacyContext';
import { FaCheckDouble, FaTrash, FaStar, FaReply, FaRegSmile, FaShare } from 'react-icons/fa';
import MessageReactions from './MessageReactions';
import VoiceMessage from './VoiceMessage';
import SelectableText from './SelectableText';

const MessageContainer = styled.div`
  display: flex;
  margin-bottom: 8px;
  justify-content: ${props => props.$isSent ? 'flex-end' : 'flex-start'};
  position: relative;
  align-items: flex-end;
`;

const SenderAvatar = styled.div`
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  margin-right: 8px;
  margin-left: 0;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 12px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const MessageBubble = styled.div`
  max-width: 65%;
  padding: 8px 12px;
  border-radius: 7.5px;
  position: relative;
  background-color: ${props => props.$isSent
    ? props.theme.colors.sentMessage
    : props.theme.colors.receivedMessage};
  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
  border-top-${props => props.$isSent ? 'right' : 'left'}-radius: 0;

  ${props => props.$isDeleted && `
    background-color: ${props.theme.colors.secondary};
    font-style: italic;
  `}
`;

const MessageText = styled.div`
  font-size: 14px;
  line-height: 19px;
  color: ${props => props.theme.colors.text};
  word-break: break-word;
`;

const HighlightedText = styled.span`
  background-color: #ffeb3b;
  color: #000;
  border-radius: 2px;
  padding: 0 2px;
  font-weight: 500;
`;

const MessageFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 4px;
`;

const MessageTime = styled.div`
  font-size: 11px;
  color: ${props => props.theme.colors.secondaryText};
  margin-right: ${props => props.$isSent ? '4px' : '0'};
`;

const MessageStatus = styled.div`
  display: ${props => props.$isSent ? 'flex' : 'none'};
  color: ${props => props.$read ? props.theme.colors.primary : props.theme.colors.secondaryText};
  font-size: 12px;
`;

const MessageActions = styled.div`
  position: absolute;
  top: -30px;
  ${props => props.$isSent ? 'right: 0;' : 'left: 0;'}
  background-color: ${props => props.theme.colors.background};
  border-radius: 20px;
  padding: 5px 10px;
  display: flex;
  gap: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  z-index: 10;
  opacity: ${props => props.$show ? 1 : 0};
  pointer-events: ${props => props.$show ? 'auto' : 'none'};
  transition: opacity 0.2s;
`;

const ActionIcon = styled.div`
  cursor: pointer;
  color: ${props => props.theme.colors.icon};

  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const SenderName = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: ${props => props.theme.colors.primary};
  margin-bottom: 2px;
`;

const Message = ({ message, isGroupChat, searchQuery }) => {
  const [showActions, setShowActions] = useState(false);
  const [isDeleted, setIsDeleted] = useState(message.isDeleted || false);
  const [showReactionSelector, setShowReactionSelector] = useState(false);
  const [reactions, setReactions] = useState(message.reactions || {
    like: 0,
    love: 0,
    laugh: 0,
    wow: 0,
    sad: 0,
    angry: 0
  });
  const [isStarred, setIsStarred] = useState(message.isStarred || false);
  const { currentTheme } = useTheme();
  const { privacySettings } = usePrivacy();

  const getInitials = (name) => {
    if (!name) return '';
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  // Function to highlight search terms in message text
  const highlightSearchTerm = (text, query) => {
    if (!query || isDeleted) return text;

    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, i) =>
      regex.test(part) ? <HighlightedText key={i}>{part}</HighlightedText> : part
    );
  };

  const handleDelete = () => {
    setIsDeleted(true);
    setShowActions(false);
  };

  const handleAddReaction = (type) => {
    setReactions(prev => {
      // Toggle reaction: if already added by user, remove it, otherwise add it
      const newCount = prev[type] > 0 ? prev[type] - 1 : prev[type] + 1;
      return { ...prev, [type]: newCount };
    });
  };

  const handleEmojiClick = () => {
    setShowReactionSelector(true);
    setShowActions(false);
  };

  const handleStar = () => {
    setIsStarred(!isStarred);
    setShowActions(false);
  };

  const handleCopyText = (text) => {
    // Show a toast or notification that text was copied
    console.log('Text copied:', text);
  };

  const handleForwardText = (text) => {
    // Show forward dialog
    console.log('Forward text:', text);
  };

  return (
    <MessageContainer
      $isSent={message.isSent}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {!message.isSent && message.sender && isGroupChat && (
        <SenderAvatar theme={currentTheme}>
          {message.senderAvatar ? (
            <img src={message.senderAvatar} alt={message.sender} />
          ) : (
            getInitials(message.sender)
          )}
        </SenderAvatar>
      )}
      <MessageActions
        $show={showActions && !isDeleted}
        $isSent={message.isSent}
        theme={currentTheme}
      >
        <ActionIcon theme={currentTheme} onClick={handleStar}>
          <FaStar style={{ color: isStarred ? '#FFD700' : undefined }} />
        </ActionIcon>
        <ActionIcon theme={currentTheme}>
          <FaReply />
        </ActionIcon>
        <ActionIcon theme={currentTheme} onClick={handleEmojiClick}>
          <FaRegSmile />
        </ActionIcon>
        <ActionIcon theme={currentTheme}>
          <FaShare />
        </ActionIcon>
        <ActionIcon theme={currentTheme} onClick={handleDelete}>
          <FaTrash />
        </ActionIcon>
      </MessageActions>

      <MessageBubble
        $isSent={message.isSent}
        $isDeleted={isDeleted}
        theme={currentTheme}
      >
        {!message.isSent && message.sender && (
          <SenderName theme={currentTheme}>{message.sender}</SenderName>
        )}

        {message.type === 'voice' ? (
          <VoiceMessage duration={message.duration} theme={currentTheme} />
        ) : (
          <MessageText theme={currentTheme}>
            {isDeleted && !privacySettings.showDeletedMessages ? (
              'This message was deleted'
            ) : (
              <SelectableText
                theme={currentTheme}
                onCopy={handleCopyText}
                onDelete={handleDelete}
                onStar={handleStar}
                onForward={handleForwardText}
              >
                {highlightSearchTerm(message.text, searchQuery)}
              </SelectableText>
            )}
          </MessageText>
        )}

        <MessageFooter>
          <MessageTime
            theme={currentTheme}
            $isSent={message.isSent}
          >
            {message.time}
          </MessageTime>

          <MessageStatus
            $isSent={message.isSent}
            $read={true}
            theme={currentTheme}
          >
            <FaCheckDouble />
          </MessageStatus>
        </MessageFooter>

        <MessageReactions
          reactions={reactions}
          onAddReaction={handleAddReaction}
          isSent={message.isSent}
          theme={currentTheme}
          showSelector={showReactionSelector}
          setShowSelector={setShowReactionSelector}
        />
      </MessageBubble>
    </MessageContainer>
  );
};

export default Message;
