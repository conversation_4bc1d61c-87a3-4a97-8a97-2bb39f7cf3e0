import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../context/ThemeContext';
import { usePrivacy } from '../../context/PrivacyContext';
import {
  FaCheckDouble,
  FaTrash,
  FaStar,
  FaReply,
  FaRegSmile,
  FaShare,
  FaCopy,
  FaForward,
  FaEdit,
  FaCheck,
  FaImage,
  FaVideo,
  FaFileAlt,
  FaDownload
} from 'react-icons/fa';
import MessageReactions from './MessageReactions';
import VoiceMessage from './VoiceMessage';
import SelectableText from './SelectableText';
import storage from '../../utils/storage';

const MessageContainer = styled.div`
  display: flex;
  margin-bottom: 8px;
  justify-content: ${props => props.$isSent ? 'flex-end' : 'flex-start'};
  position: relative;
  align-items: flex-end;
`;

const SenderAvatar = styled.div`
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  margin-right: 8px;
  margin-left: 0;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 12px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const MessageBubble = styled.div`
  max-width: 65%;
  padding: 8px 12px;
  border-radius: 7.5px;
  position: relative;
  background-color: ${props => props.$isSent
    ? props.theme.colors.sentMessage
    : props.theme.colors.receivedMessage};
  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
  border-top-${props => props.$isSent ? 'right' : 'left'}-radius: 0;

  ${props => props.$isDeleted && `
    background-color: ${props.theme.colors.secondary};
    font-style: italic;
  `}
`;

const MessageText = styled.div`
  font-size: 14px;
  line-height: 19px;
  color: ${props => props.theme.colors.text};
  word-break: break-word;
`;

const HighlightedText = styled.span`
  background-color: #ffeb3b;
  color: #000;
  border-radius: 2px;
  padding: 0 2px;
  font-weight: 500;
`;

const MessageFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 4px;
`;

const MessageTime = styled.div`
  font-size: 11px;
  color: ${props => props.theme.colors.secondaryText};
  margin-right: ${props => props.$isSent ? '4px' : '0'};
`;

const MessageStatus = styled.div`
  display: ${props => props.$isSent ? 'flex' : 'none'};
  color: ${props => props.$read ? props.theme.colors.primary : props.theme.colors.secondaryText};
  font-size: 12px;
`;

const MessageActions = styled.div`
  position: absolute;
  top: -30px;
  ${props => props.$isSent ? 'right: 0;' : 'left: 0;'}
  background-color: ${props => props.theme.colors.background};
  border-radius: 20px;
  padding: 5px 10px;
  display: flex;
  gap: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  z-index: 10;
  opacity: ${props => props.$show ? 1 : 0};
  pointer-events: ${props => props.$show ? 'auto' : 'none'};
  transition: opacity 0.2s;
`;

const ActionIcon = styled.div`
  cursor: pointer;
  color: ${props => props.theme.colors.icon};

  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const MediaContainer = styled.div`
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
  max-width: 300px;
  position: relative;
`;

const MediaImage = styled.img`
  width: 100%;
  height: auto;
  max-height: 300px;
  object-fit: cover;
  cursor: pointer;
`;

const MediaVideo = styled.video`
  width: 100%;
  height: auto;
  max-height: 300px;
  object-fit: cover;
`;

const FileContainer = styled.div`
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;

  &:hover {
    background-color: ${props => props.theme.colors.border};
  }
`;

const FileIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 12px;
  font-size: 18px;
`;

const FileInfo = styled.div`
  flex: 1;
`;

const FileName = styled.div`
  font-size: 14px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 2px;
`;

const FileSize = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.secondaryText};
`;

const MediaOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;

  &:hover {
    opacity: 1;
  }
`;

const PlayButton = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #333;
  cursor: pointer;
`;

const TickContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 2px;
`;

const Tick = styled.div`
  font-size: 12px;
  color: ${props => {
    if (props.$status === 'sent') return props.theme.colors.secondaryText;
    if (props.$status === 'delivered') return props.theme.colors.secondaryText;
    if (props.$status === 'read') return props.theme.colors.primary;
    return props.theme.colors.secondaryText;
  }};
`;

const SenderName = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: ${props => props.theme.colors.primary};
  margin-bottom: 2px;
`;

const Message = ({ message, isGroupChat, searchQuery, chatId, onDelete, onEdit }) => {
  const [showActions, setShowActions] = useState(false);
  const [isDeleted, setIsDeleted] = useState(message.isDeleted || false);
  const [showReactionSelector, setShowReactionSelector] = useState(false);
  const [reactions, setReactions] = useState(message.reactions || {
    like: 0,
    love: 0,
    laugh: 0,
    wow: 0,
    sad: 0,
    angry: 0
  });
  const [isStarred, setIsStarred] = useState(message.isStarred || false);
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(message.text || '');
  const { currentTheme } = useTheme();
  const { privacySettings } = usePrivacy();

  const getInitials = (name) => {
    if (!name) return '';
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type) => {
    if (type?.startsWith('image/')) return <FaImage />;
    if (type?.startsWith('video/')) return <FaVideo />;
    return <FaFileAlt />;
  };

  // Function to highlight search terms in message text
  const highlightSearchTerm = (text, query) => {
    if (!query || isDeleted) return text;

    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, i) =>
      regex.test(part) ? <HighlightedText key={i}>{part}</HighlightedText> : part
    );
  };

  const handleDelete = () => {
    setIsDeleted(true);
    setShowActions(false);
    if (onDelete) onDelete(message.id);
    // Save to storage
    storage.updateMessage(chatId, message.id, { isDeleted: true });
  };

  const handleAddReaction = (type) => {
    setReactions(prev => {
      const newCount = prev[type] > 0 ? prev[type] - 1 : prev[type] + 1;
      const newReactions = { ...prev, [type]: newCount };
      // Save to storage
      storage.updateMessage(chatId, message.id, { reactions: newReactions });
      return newReactions;
    });
  };

  const handleEmojiClick = () => {
    setShowReactionSelector(true);
    setShowActions(false);
  };

  const handleStar = () => {
    const newStarred = !isStarred;
    setIsStarred(newStarred);
    setShowActions(false);

    if (newStarred) {
      storage.starMessage(chatId, message.id);
    } else {
      storage.unstarMessage(chatId, message.id);
    }

    storage.updateMessage(chatId, message.id, { isStarred: newStarred });
  };

  const handleEdit = () => {
    setIsEditing(true);
    setShowActions(false);
  };

  const handleSaveEdit = () => {
    if (editText.trim() !== message.text) {
      storage.updateMessage(chatId, message.id, {
        text: editText.trim(),
        isEdited: true,
        editedAt: Date.now()
      });
      if (onEdit) onEdit(message.id, editText.trim());
    }
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditText(message.text || '');
    setIsEditing(false);
  };

  const handleCopyText = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      // Show success feedback
      console.log('Text copied to clipboard');
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
    setShowActions(false);
  };

  const handleForwardText = (text) => {
    // Show forward dialog
    console.log('Forward text:', text);
    setShowActions(false);
  };

  const handleDownload = (media) => {
    if (media?.url) {
      const link = document.createElement('a');
      link.href = media.url;
      link.download = media.name || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    setShowActions(false);
  };

  const renderTicks = () => {
    if (!message.isSent) return null;

    const status = message.status || 'sent';

    return (
      <TickContainer>
        {status === 'sent' && (
          <Tick $status="sent" theme={currentTheme}>
            <FaCheck />
          </Tick>
        )}
        {(status === 'delivered' || status === 'read') && (
          <Tick $status={status} theme={currentTheme}>
            <FaCheckDouble />
          </Tick>
        )}
      </TickContainer>
    );
  };

  const renderMedia = () => {
    if (!message.media) return null;

    const { type, url, name, size } = message.media;

    if (type?.startsWith('image/')) {
      return (
        <MediaContainer>
          <MediaImage
            src={url}
            alt={name || 'Image'}
            onClick={() => window.open(url, '_blank')}
          />
          <MediaOverlay>
            <PlayButton onClick={() => handleDownload(message.media)}>
              <FaDownload />
            </PlayButton>
          </MediaOverlay>
        </MediaContainer>
      );
    }

    if (type?.startsWith('video/')) {
      return (
        <MediaContainer>
          <MediaVideo controls>
            <source src={url} type={type} />
            Your browser does not support the video tag.
          </MediaVideo>
        </MediaContainer>
      );
    }

    // File attachment
    return (
      <FileContainer theme={currentTheme} onClick={() => handleDownload(message.media)}>
        <FileIcon theme={currentTheme}>
          {getFileIcon(type)}
        </FileIcon>
        <FileInfo>
          <FileName theme={currentTheme}>{name || 'Unknown file'}</FileName>
          <FileSize theme={currentTheme}>{formatFileSize(size || 0)}</FileSize>
        </FileInfo>
        <FaDownload style={{ color: currentTheme.colors.icon }} />
      </FileContainer>
    );
  };

  return (
    <MessageContainer
      $isSent={message.isSent}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {!message.isSent && message.sender && isGroupChat && (
        <SenderAvatar theme={currentTheme}>
          {message.senderAvatar ? (
            <img src={message.senderAvatar} alt={message.sender} />
          ) : (
            getInitials(message.sender)
          )}
        </SenderAvatar>
      )}
      <MessageActions
        $show={showActions && !isDeleted}
        $isSent={message.isSent}
        theme={currentTheme}
      >
        <ActionIcon theme={currentTheme} onClick={handleStar}>
          <FaStar style={{ color: isStarred ? '#FFD700' : undefined }} />
        </ActionIcon>
        <ActionIcon theme={currentTheme}>
          <FaReply />
        </ActionIcon>
        <ActionIcon theme={currentTheme} onClick={() => handleCopyText(message.text)}>
          <FaCopy />
        </ActionIcon>
        <ActionIcon theme={currentTheme} onClick={() => handleForwardText(message.text)}>
          <FaForward />
        </ActionIcon>
        {message.isSent && (
          <ActionIcon theme={currentTheme} onClick={handleEdit}>
            <FaEdit />
          </ActionIcon>
        )}
        <ActionIcon theme={currentTheme} onClick={handleEmojiClick}>
          <FaRegSmile />
        </ActionIcon>
        <ActionIcon theme={currentTheme} onClick={handleDelete}>
          <FaTrash />
        </ActionIcon>
      </MessageActions>

      <MessageBubble
        $isSent={message.isSent}
        $isDeleted={isDeleted}
        theme={currentTheme}
      >
        {!message.isSent && message.sender && (
          <SenderName theme={currentTheme}>{message.sender}</SenderName>
        )}

        {/* Render media if present */}
        {renderMedia()}

        {message.type === 'voice' ? (
          <VoiceMessage duration={message.duration} theme={currentTheme} />
        ) : isEditing ? (
          <div>
            <input
              type="text"
              value={editText}
              onChange={(e) => setEditText(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') handleSaveEdit();
                if (e.key === 'Escape') handleCancelEdit();
              }}
              style={{
                width: '100%',
                padding: '8px',
                border: 'none',
                borderRadius: '4px',
                backgroundColor: currentTheme.colors.background,
                color: currentTheme.colors.text,
                fontSize: '14px'
              }}
              autoFocus
            />
            <div style={{ marginTop: '8px', display: 'flex', gap: '8px' }}>
              <button onClick={handleSaveEdit} style={{
                padding: '4px 8px',
                backgroundColor: currentTheme.colors.primary,
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                fontSize: '12px'
              }}>
                Save
              </button>
              <button onClick={handleCancelEdit} style={{
                padding: '4px 8px',
                backgroundColor: currentTheme.colors.secondary,
                color: currentTheme.colors.text,
                border: 'none',
                borderRadius: '4px',
                fontSize: '12px'
              }}>
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <MessageText theme={currentTheme}>
            {isDeleted && !privacySettings.showDeletedMessages ? (
              'This message was deleted'
            ) : (
              <SelectableText
                theme={currentTheme}
                onCopy={handleCopyText}
                onDelete={handleDelete}
                onStar={handleStar}
                onForward={handleForwardText}
              >
                {highlightSearchTerm(message.text, searchQuery)}
                {message.isEdited && (
                  <span style={{
                    fontSize: '11px',
                    color: currentTheme.colors.secondaryText,
                    fontStyle: 'italic',
                    marginLeft: '8px'
                  }}>
                    (edited)
                  </span>
                )}
              </SelectableText>
            )}
          </MessageText>
        )}

        <MessageFooter>
          <MessageTime
            theme={currentTheme}
            $isSent={message.isSent}
          >
            {message.time}
          </MessageTime>

          {renderTicks()}
        </MessageFooter>

        <MessageReactions
          reactions={reactions}
          onAddReaction={handleAddReaction}
          isSent={message.isSent}
          theme={currentTheme}
          showSelector={showReactionSelector}
          setShowSelector={setShowReactionSelector}
        />
      </MessageBubble>
    </MessageContainer>
  );
};

export default Message;
