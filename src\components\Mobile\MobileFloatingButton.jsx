import React, { useState } from 'react';
import styled from 'styled-components';
import {
  FaPlus,
  FaEdit,
  FaCamera,
  FaPhone,
  FaVideo,
  FaUsers,
  FaTimes,
  FaAddressBook,
  FaFolder
} from 'react-icons/fa';
import MobileHaptics from './MobileHaptics';

const FABContainer = styled.div`
  position: fixed;
  bottom: 80px;
  right: 20px;
  z-index: 999;
  display: none;

  @media (max-width: 768px) {
    display: block;
  }
`;

const MainFAB = styled.button`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform: ${props => props.$expanded ? 'rotate(45deg)' : 'rotate(0deg)'};

  &:active {
    transform: ${props => props.$expanded ? 'rotate(45deg) scale(0.95)' : 'scale(0.95)'};
  }

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
`;

const SubFABsContainer = styled.div`
  position: absolute;
  bottom: 70px;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
  opacity: ${props => props.$visible ? 1 : 0};
  transform: ${props => props.$visible ? 'translateY(0)' : 'translateY(20px)'};
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  pointer-events: ${props => props.$visible ? 'auto' : 'none'};
`;

const SubFAB = styled.button`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.secondary};
  color: ${props => props.color ? 'white' : props.theme.colors.text};
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s ease;
  position: relative;

  &:active {
    transform: scale(0.9);
  }

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const SubFABLabel = styled.div`
  position: absolute;
  right: 60px;
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  opacity: ${props => props.$show ? 1 : 0};
  transform: ${props => props.$show ? 'translateX(0)' : 'translateX(10px)'};
  transition: all 0.2s ease;
  pointer-events: none;
`;

const Backdrop = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  opacity: ${props => props.$visible ? 1 : 0};
  visibility: ${props => props.$visible ? 'visible' : 'hidden'};
  transition: all 0.3s ease;
  z-index: 998;
`;

const MobileFloatingButton = ({
  theme,
  onNewChat,
  onNewGroup,
  onCamera,
  onContacts,
  onFiles,
  onCall,
  onVideoCall
}) => {
  const [expanded, setExpanded] = useState(false);
  const [hoveredAction, setHoveredAction] = useState(null);

  const actions = [
    {
      id: 'newChat',
      icon: <FaEdit />,
      label: 'New Chat',
      color: '#4CAF50',
      action: onNewChat
    },
    {
      id: 'newGroup',
      icon: <FaUsers />,
      label: 'New Group',
      color: '#2196F3',
      action: onNewGroup
    },
    {
      id: 'contacts',
      icon: <FaAddressBook />,
      label: 'Contacts',
      color: '#607D8B',
      action: onContacts
    },
    {
      id: 'files',
      icon: <FaFolder />,
      label: 'Files',
      color: '#795548',
      action: onFiles
    },
    {
      id: 'camera',
      icon: <FaCamera />,
      label: 'Camera',
      color: '#FF9800',
      action: onCamera
    },
    {
      id: 'call',
      icon: <FaPhone />,
      label: 'Call',
      color: '#F44336',
      action: onCall
    },
    {
      id: 'videoCall',
      icon: <FaVideo />,
      label: 'Video Call',
      color: '#9C27B0',
      action: onVideoCall
    }
  ];

  const handleMainFABClick = () => {
    MobileHaptics.light();
    setExpanded(!expanded);
  };

  const handleActionClick = (action) => {
    MobileHaptics.medium();
    setExpanded(false);
    if (action.action) {
      action.action();
    }
  };

  const handleBackdropClick = () => {
    setExpanded(false);
  };

  return (
    <>
      <Backdrop $visible={expanded} onClick={handleBackdropClick} />
      <FABContainer>
        <SubFABsContainer $visible={expanded}>
          {actions.map((action, index) => (
            <SubFAB
              key={action.id}
              theme={theme}
              color={action.color}
              onClick={() => handleActionClick(action)}
              onMouseEnter={() => setHoveredAction(action.id)}
              onMouseLeave={() => setHoveredAction(null)}
              style={{
                transitionDelay: expanded ? `${index * 50}ms` : '0ms'
              }}
            >
              {action.icon}
              <SubFABLabel
                theme={theme}
                $show={hoveredAction === action.id}
              >
                {action.label}
              </SubFABLabel>
            </SubFAB>
          ))}
        </SubFABsContainer>

        <MainFAB
          theme={theme}
          $expanded={expanded}
          onClick={handleMainFABClick}
        >
          {expanded ? <FaTimes /> : <FaPlus />}
        </MainFAB>
      </FABContainer>
    </>
  );
};

export default MobileFloatingButton;
