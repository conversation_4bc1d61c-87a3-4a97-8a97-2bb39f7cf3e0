import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  FaTimes, 
  FaAdjust, 
  FaPalette, 
  FaFilter,
  FaRegSmile,
  FaRegStar,
  FaRegHeart,
  FaRegSun,
  FaRegMoon,
  FaRegSnowflake
} from 'react-icons/fa';

const EffectsContainer = styled.div`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.9), rgba(0,0,0,0.7));
  backdrop-filter: blur(10px);
  padding: 20px;
  z-index: 1000;
  transform: translateY(${props => props.$visible ? '0' : '100%'});
  transition: transform 0.3s ease-in-out;
`;

const EffectsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const EffectsTitle = styled.h3`
  color: white;
  font-size: 18px;
  font-weight: 600;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const EffectsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
`;

const EffectItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 10px;
  border-radius: 12px;
  background: ${props => props.$active ? 'rgba(255, 255, 255, 0.2)' : 'transparent'};
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
  }
`;

const EffectIcon = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: ${props => props.color || 'rgba(255, 255, 255, 0.2)'};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  margin-bottom: 8px;
`;

const EffectLabel = styled.div`
  color: white;
  font-size: 12px;
  text-align: center;
`;

const FilterSlider = styled.div`
  margin-bottom: 15px;
`;

const SliderLabel = styled.div`
  color: white;
  font-size: 14px;
  margin-bottom: 8px;
`;

const Slider = styled.input`
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.3);
  outline: none;
  
  &::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    cursor: pointer;
  }
  
  &::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    border: none;
  }
`;

const CameraEffects = ({ visible, onClose, onEffectApply }) => {
  const [activeEffect, setActiveEffect] = useState(null);
  const [brightness, setBrightness] = useState(100);
  const [contrast, setContrast] = useState(100);
  const [saturation, setSaturation] = useState(100);

  const effects = [
    { 
      id: 'none', 
      name: 'None', 
      icon: <FaTimes />, 
      color: '#666',
      filter: 'none'
    },
    { 
      id: 'vintage', 
      name: 'Vintage', 
      icon: <FaRegSun />, 
      color: '#D4A574',
      filter: 'sepia(50%) contrast(120%) brightness(110%)'
    },
    { 
      id: 'bw', 
      name: 'B&W', 
      icon: <FaAdjust />, 
      color: '#888',
      filter: 'grayscale(100%) contrast(120%)'
    },
    { 
      id: 'cool', 
      name: 'Cool', 
      icon: <FaRegSnowflake />, 
      color: '#4A90E2',
      filter: 'hue-rotate(180deg) saturate(120%)'
    },
    { 
      id: 'warm', 
      name: 'Warm', 
      icon: <FaRegHeart />, 
      color: '#E94B3C',
      filter: 'hue-rotate(30deg) saturate(130%) brightness(110%)'
    },
    { 
      id: 'dramatic', 
      name: 'Drama', 
      icon: <FaRegMoon />, 
      color: '#2C3E50',
      filter: 'contrast(150%) brightness(90%) saturate(80%)'
    },
    { 
      id: 'vivid', 
      name: 'Vivid', 
      icon: <FaPalette />, 
      color: '#9B59B6',
      filter: 'saturate(200%) contrast(120%)'
    },
    { 
      id: 'soft', 
      name: 'Soft', 
      icon: <FaRegSmile />, 
      color: '#F39C12',
      filter: 'brightness(110%) contrast(90%) blur(0.5px)'
    }
  ];

  const handleEffectSelect = (effect) => {
    setActiveEffect(effect.id);
    if (onEffectApply) {
      onEffectApply({
        ...effect,
        brightness,
        contrast,
        saturation
      });
    }
  };

  const handleSliderChange = (type, value) => {
    switch (type) {
      case 'brightness':
        setBrightness(value);
        break;
      case 'contrast':
        setContrast(value);
        break;
      case 'saturation':
        setSaturation(value);
        break;
    }
    
    // Apply changes in real-time
    if (onEffectApply && activeEffect) {
      const effect = effects.find(e => e.id === activeEffect);
      onEffectApply({
        ...effect,
        brightness: type === 'brightness' ? value : brightness,
        contrast: type === 'contrast' ? value : contrast,
        saturation: type === 'saturation' ? value : saturation
      });
    }
  };

  if (!visible) return null;

  return (
    <EffectsContainer $visible={visible}>
      <EffectsHeader>
        <EffectsTitle>Camera Effects</EffectsTitle>
        <CloseButton onClick={onClose}>
          <FaTimes />
        </CloseButton>
      </EffectsHeader>

      <EffectsGrid>
        {effects.map((effect) => (
          <EffectItem
            key={effect.id}
            $active={activeEffect === effect.id}
            onClick={() => handleEffectSelect(effect)}
          >
            <EffectIcon color={effect.color}>
              {effect.icon}
            </EffectIcon>
            <EffectLabel>{effect.name}</EffectLabel>
          </EffectItem>
        ))}
      </EffectsGrid>

      <FilterSlider>
        <SliderLabel>Brightness: {brightness}%</SliderLabel>
        <Slider
          type="range"
          min="50"
          max="150"
          value={brightness}
          onChange={(e) => handleSliderChange('brightness', e.target.value)}
        />
      </FilterSlider>

      <FilterSlider>
        <SliderLabel>Contrast: {contrast}%</SliderLabel>
        <Slider
          type="range"
          min="50"
          max="150"
          value={contrast}
          onChange={(e) => handleSliderChange('contrast', e.target.value)}
        />
      </FilterSlider>

      <FilterSlider>
        <SliderLabel>Saturation: {saturation}%</SliderLabel>
        <Slider
          type="range"
          min="0"
          max="200"
          value={saturation}
          onChange={(e) => handleSliderChange('saturation', e.target.value)}
        />
      </FilterSlider>
    </EffectsContainer>
  );
};

export default CameraEffects;
