// Media Access Utility for WhatsApp Clone
import storage from './storage';

class MediaAccessManager {
  constructor() {
    this.stream = null;
    this.mediaRecorder = null;
    this.chunks = [];
  }

  // Camera access
  async requestCameraAccess(constraints = { video: true, audio: false }) {
    try {
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      this.stream = stream;
      return stream;
    } catch (error) {
      console.error('Error accessing camera:', error);
      throw new Error('Camera access denied or not available');
    }
  }

  // Microphone access
  async requestMicrophoneAccess() {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      return stream;
    } catch (error) {
      console.error('Error accessing microphone:', error);
      throw new Error('Microphone access denied or not available');
    }
  }

  // Get available devices
  async getAvailableDevices() {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return {
        cameras: devices.filter(device => device.kind === 'videoinput'),
        microphones: devices.filter(device => device.kind === 'audioinput'),
        speakers: devices.filter(device => device.kind === 'audiooutput')
      };
    } catch (error) {
      console.error('Error getting devices:', error);
      return { cameras: [], microphones: [], speakers: [] };
    }
  }

  // Capture photo from video stream
  capturePhoto(videoElement, quality = 0.9) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    
    canvas.width = videoElement.videoWidth;
    canvas.height = videoElement.videoHeight;
    
    context.drawImage(videoElement, 0, 0);
    
    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        const file = new File([blob], `photo_${Date.now()}.jpg`, {
          type: 'image/jpeg',
          lastModified: Date.now()
        });
        resolve(file);
      }, 'image/jpeg', quality);
    });
  }

  // Start video recording
  async startVideoRecording(stream, options = {}) {
    try {
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: options.mimeType || 'video/webm',
        videoBitsPerSecond: options.videoBitsPerSecond || 2500000
      });

      this.mediaRecorder = mediaRecorder;
      this.chunks = [];

      return new Promise((resolve, reject) => {
        mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            this.chunks.push(event.data);
          }
        };

        mediaRecorder.onstop = () => {
          const blob = new Blob(this.chunks, { type: 'video/webm' });
          const file = new File([blob], `video_${Date.now()}.webm`, {
            type: 'video/webm',
            lastModified: Date.now()
          });
          resolve(file);
        };

        mediaRecorder.onerror = reject;
        mediaRecorder.start();
      });
    } catch (error) {
      console.error('Error starting video recording:', error);
      throw error;
    }
  }

  // Stop video recording
  stopVideoRecording() {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
    }
  }

  // Record audio
  async recordAudio(duration = 60000) {
    try {
      const stream = await this.requestMicrophoneAccess();
      const mediaRecorder = new MediaRecorder(stream);
      const chunks = [];

      return new Promise((resolve, reject) => {
        mediaRecorder.ondataavailable = (event) => {
          chunks.push(event.data);
        };

        mediaRecorder.onstop = () => {
          const blob = new Blob(chunks, { type: 'audio/webm' });
          const file = new File([blob], `audio_${Date.now()}.webm`, {
            type: 'audio/webm',
            lastModified: Date.now()
          });
          stream.getTracks().forEach(track => track.stop());
          resolve(file);
        };

        mediaRecorder.onerror = reject;
        mediaRecorder.start();

        // Auto stop after duration
        setTimeout(() => {
          if (mediaRecorder.state === 'recording') {
            mediaRecorder.stop();
          }
        }, duration);
      });
    } catch (error) {
      console.error('Error recording audio:', error);
      throw error;
    }
  }

  // File picker for images
  async pickImages(multiple = false) {
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.multiple = multiple;
      
      input.onchange = (event) => {
        const files = Array.from(event.target.files);
        resolve(files);
      };
      
      input.click();
    });
  }

  // File picker for videos
  async pickVideos(multiple = false) {
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'video/*';
      input.multiple = multiple;
      
      input.onchange = (event) => {
        const files = Array.from(event.target.files);
        resolve(files);
      };
      
      input.click();
    });
  }

  // File picker for documents
  async pickDocuments(multiple = false) {
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx';
      input.multiple = multiple;
      
      input.onchange = (event) => {
        const files = Array.from(event.target.files);
        resolve(files);
      };
      
      input.click();
    });
  }

  // Get location
  async getCurrentLocation() {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp
          });
        },
        (error) => {
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      );
    });
  }

  // Process and compress image
  async compressImage(file, maxWidth = 1920, maxHeight = 1080, quality = 0.8) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
        
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob((blob) => {
          const compressedFile = new File([blob], file.name, {
            type: 'image/jpeg',
            lastModified: Date.now()
          });
          resolve(compressedFile);
        }, 'image/jpeg', quality);
      };

      img.src = URL.createObjectURL(file);
    });
  }

  // Save media to storage
  async saveMediaToStorage(file, type = 'file') {
    try {
      const mediaId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      
      // Compress image if needed
      let processedFile = file;
      if (type === 'image' && file.size > 1024 * 1024) { // 1MB
        processedFile = await this.compressImage(file);
      }

      const reader = new FileReader();
      
      return new Promise((resolve, reject) => {
        reader.onload = (e) => {
          const mediaData = {
            id: mediaId,
            name: processedFile.name,
            type: processedFile.type,
            size: processedFile.size,
            url: e.target.result,
            originalSize: file.size,
            uploadedAt: Date.now(),
            mediaType: type
          };

          // Save to storage
          const saved = storage.saveMedia(mediaId, mediaData);
          if (saved) {
            resolve(mediaData);
          } else {
            reject(new Error('Failed to save media'));
          }
        };

        reader.onerror = reject;
        reader.readAsDataURL(processedFile);
      });
    } catch (error) {
      console.error('Error saving media:', error);
      throw error;
    }
  }

  // Clean up streams
  stopAllStreams() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
  }

  // Check permissions
  async checkPermissions() {
    const permissions = {};
    
    try {
      const cameraPermission = await navigator.permissions.query({ name: 'camera' });
      permissions.camera = cameraPermission.state;
    } catch (e) {
      permissions.camera = 'unknown';
    }

    try {
      const micPermission = await navigator.permissions.query({ name: 'microphone' });
      permissions.microphone = micPermission.state;
    } catch (e) {
      permissions.microphone = 'unknown';
    }

    try {
      const geoPermission = await navigator.permissions.query({ name: 'geolocation' });
      permissions.geolocation = geoPermission.state;
    } catch (e) {
      permissions.geolocation = 'unknown';
    }

    return permissions;
  }
}

// Create singleton instance
const mediaAccess = new MediaAccessManager();

export default mediaAccess;
