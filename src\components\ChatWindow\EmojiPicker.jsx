import React, { useState } from 'react';
import styled from 'styled-components';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FaSadTear,
  FaAngry,
  FaHeart,
  FaThumbsUp,
  FaThumbsDown,
  FaHandPeace,
  FaHandRock,
  FaFire,
  FaGift,
  FaBirthdayCake,
  FaStar,
  FaMoon,
  FaSun
} from 'react-icons/fa';

const EmojiPickerContainer = styled.div`
  position: absolute;
  bottom: 70px;
  left: 20px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 10;
  display: ${props => props.$show ? 'block' : 'none'};
  width: 300px;
`;

const EmojiGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
`;

const EmojiCategory = styled.div`
  margin-bottom: 15px;
`;

const CategoryTitle = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.secondaryText};
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const EmojiButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const TabsContainer = styled.div`
  display: flex;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  margin-bottom: 10px;
`;

const Tab = styled.button`
  background: none;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
  border-bottom: 2px solid ${props => props.$active ? props.theme.colors.primary : 'transparent'};

  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const EmojiPicker = ({ show, onEmojiSelect, theme }) => {
  const emojis = {
    smileys: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳'],
    gestures: ['👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '👇', '☝️', '👋', '🤚', '🖐️', '✋', '🖖', '👏', '🙌', '👐', '🤲', '🤝', '🙏'],
    love: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '♥️'],
    activities: ['🎮', '🎯', '🎲', '🧩', '🎭', '🎨', '🎬', '🎤', '🎧', '🎼', '🎹', '🥁', '🎷', '🎺', '🎸', '🎻', '🎪', '🎟️', '🎫'],
    food: ['🍏', '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🌽', '🥕', '🧄', '🧅', '🥔', '🍠', '🥐', '🥯', '🍞', '🥖', '🥨', '🧀', '🥚', '🍳', '🧈', '🥞', '🧇', '🥓', '🥩', '🍗', '🍖', '🌭', '🍔', '🍟', '🍕', '🥪', '🥙', '🧆', '🌮', '🌯', '🥗', '🥘', '🥫', '🍝', '🍜', '🍲', '🍛', '🍣', '🍱', '🥟', '🦪', '🍤', '🍙', '🍚', '🍘', '🍥', '🥠', '🥮', '🍢', '🍡', '🍧', '🍨', '🍦', '🥧', '🧁', '🍰', '🎂', '🍮', '🍭', '🍬', '🍫', '🍿', '🍩', '🍪', '🌰', '🥜', '🍯', '🥛', '🍼', '☕', '🍵', '🧃', '🥤', '🍶', '🍺', '🍻', '🥂', '🍷', '🥃', '🍸', '🍹', '🧉', '🍾', '🧊'],
  };

  const [activeTab, setActiveTab] = useState('smileys');

  const handleEmojiClick = (emoji) => {
    onEmojiSelect(emoji);
  };

  return (
    <EmojiPickerContainer $show={show} theme={theme}>
      <TabsContainer theme={theme}>
        <Tab
          $active={activeTab === 'smileys'}
          onClick={() => setActiveTab('smileys')}
          theme={theme}
        >
          <FaSmile />
        </Tab>
        <Tab
          $active={activeTab === 'gestures'}
          onClick={() => setActiveTab('gestures')}
          theme={theme}
        >
          <FaHandPeace />
        </Tab>
        <Tab
          $active={activeTab === 'love'}
          onClick={() => setActiveTab('love')}
          theme={theme}
        >
          <FaHeart />
        </Tab>
        <Tab
          $active={activeTab === 'activities'}
          onClick={() => setActiveTab('activities')}
          theme={theme}
        >
          <FaFire />
        </Tab>
        <Tab
          $active={activeTab === 'food'}
          onClick={() => setActiveTab('food')}
          theme={theme}
        >
          <FaBirthdayCake />
        </Tab>
      </TabsContainer>

      <EmojiCategory>
        <CategoryTitle theme={theme}>
          {activeTab === 'smileys' && 'Smileys & Emotions'}
          {activeTab === 'gestures' && 'Hand Gestures'}
          {activeTab === 'love' && 'Love & Hearts'}
          {activeTab === 'activities' && 'Activities'}
          {activeTab === 'food' && 'Food & Drink'}
        </CategoryTitle>
        <EmojiGrid>
          {emojis[activeTab].map((emoji, index) => (
            <EmojiButton
              key={index}
              onClick={() => handleEmojiClick(emoji)}
              theme={theme}
            >
              {emoji}
            </EmojiButton>
          ))}
        </EmojiGrid>
      </EmojiCategory>
    </EmojiPickerContainer>
  );
};

export default EmojiPicker;
