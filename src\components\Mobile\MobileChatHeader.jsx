import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  FaArrowLeft, 
  FaPhone, 
  FaVideo, 
  FaEllipsisV,
  FaSearch,
  FaStar,
  FaVolumeUp,
  FaVolumeMute,
  FaUserPlus,
  FaTrash,
  FaExclamationTriangle
} from 'react-icons/fa';
import MobileHaptics from './MobileHaptics';

const HeaderContainer = styled.div`
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  position: relative;
  min-height: 60px;
  
  @media (max-width: 768px) {
    padding-top: 32px; /* Account for status bar */
  }
`;

const BackButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  margin-right: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  &:active {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;

const ChatInfo = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const Avatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const OnlineIndicator = styled.div`
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #4CAF50;
  border: 2px solid white;
`;

const ChatDetails = styled.div`
  flex: 1;
  min-width: 0;
`;

const ChatName = styled.div`
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const ChatStatus = styled.div`
  font-size: 12px;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const ActionButtons = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  &:active {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;

const DropdownMenu = styled.div`
  position: absolute;
  top: 100%;
  right: 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  z-index: 1000;
  opacity: ${props => props.$visible ? 1 : 0};
  transform: ${props => props.$visible ? 'translateY(0)' : 'translateY(-10px)'};
  visibility: ${props => props.$visible ? 'visible' : 'hidden'};
  transition: all 0.2s ease;
`;

const MenuItem = styled.div`
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: ${props => props.theme.colors.text};
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
  
  &:first-child {
    border-radius: 8px 8px 0 0;
  }
  
  &:last-child {
    border-radius: 0 0 8px 8px;
  }
  
  svg {
    margin-right: 12px;
    font-size: 16px;
    color: ${props => props.theme.colors.icon};
  }
`;

const MobileChatHeader = ({ 
  chat, 
  theme, 
  onBack, 
  onCall, 
  onVideoCall, 
  onSearch,
  onChatInfo 
}) => {
  const [showMenu, setShowMenu] = useState(false);

  const getInitials = (name) => {
    return name?.split(' ').map(n => n[0]).join('').toUpperCase() || '';
  };

  const getStatusText = () => {
    if (!chat) return '';
    
    if (chat.isGroup) {
      return `${chat.members?.length || 0} members`;
    }
    
    if (chat.isOnline) {
      return 'online';
    }
    
    return `last seen ${chat.lastSeen}`;
  };

  const handleBackClick = () => {
    MobileHaptics.light();
    if (onBack) onBack();
  };

  const handleCallClick = () => {
    MobileHaptics.medium();
    if (onCall) onCall();
  };

  const handleVideoCallClick = () => {
    MobileHaptics.medium();
    if (onVideoCall) onVideoCall();
  };

  const handleSearchClick = () => {
    MobileHaptics.light();
    if (onSearch) onSearch();
  };

  const handleMenuClick = () => {
    MobileHaptics.light();
    setShowMenu(!showMenu);
  };

  const handleChatInfoClick = () => {
    MobileHaptics.light();
    if (onChatInfo) onChatInfo();
    setShowMenu(false);
  };

  const menuItems = [
    { icon: <FaSearch />, label: 'Search', action: onSearch },
    { icon: <FaStar />, label: 'Starred Messages', action: () => {} },
    { icon: <FaVolumeUp />, label: 'Mute Notifications', action: () => {} },
    { icon: <FaUserPlus />, label: 'Add to Group', action: () => {} },
    { icon: <FaExclamationTriangle />, label: 'Report', action: () => {} },
    { icon: <FaTrash />, label: 'Delete Chat', action: () => {} }
  ];

  if (!chat) return null;

  return (
    <HeaderContainer theme={theme}>
      <BackButton onClick={handleBackClick}>
        <FaArrowLeft />
      </BackButton>
      
      <ChatInfo onClick={handleChatInfoClick}>
        <Avatar>
          {chat.avatar ? (
            <img src={chat.avatar} alt={chat.name} />
          ) : (
            getInitials(chat.name)
          )}
          {chat.isOnline && !chat.isGroup && <OnlineIndicator />}
        </Avatar>
        
        <ChatDetails>
          <ChatName>{chat.name}</ChatName>
          <ChatStatus>{getStatusText()}</ChatStatus>
        </ChatDetails>
      </ChatInfo>
      
      <ActionButtons>
        <ActionButton onClick={handleCallClick}>
          <FaPhone />
        </ActionButton>
        
        <ActionButton onClick={handleVideoCallClick}>
          <FaVideo />
        </ActionButton>
        
        <ActionButton onClick={handleMenuClick}>
          <FaEllipsisV />
        </ActionButton>
      </ActionButtons>
      
      <DropdownMenu theme={theme} $visible={showMenu}>
        {menuItems.map((item, index) => (
          <MenuItem
            key={index}
            theme={theme}
            onClick={() => {
              if (item.action) item.action();
              setShowMenu(false);
            }}
          >
            {item.icon}
            {item.label}
          </MenuItem>
        ))}
      </DropdownMenu>
    </HeaderContainer>
  );
};

export default MobileChatHeader;
