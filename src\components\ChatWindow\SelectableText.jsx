import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { FaCopy, FaShare, FaTrash, FaStar } from 'react-icons/fa';

const SelectableContainer = styled.div`
  position: relative;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text;
  word-break: break-word;

  /* Apply the selectable-text class to ensure text selection works */
  &.selectable-text {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
  }
`;

const SelectionToolbar = styled.div`
  position: absolute;
  background-color: ${props => props.theme.colors.primary};
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  display: ${props => props.$show ? 'flex' : 'none'};
  align-items: center;
  padding: 6px;
  z-index: 100;
  top: ${props => props.$position.top}px;
  left: ${props => props.$position.left}px;
  transform: translateY(-100%);

  &::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid ${props => props.theme.colors.primary};
  }
`;

const ToolbarButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 14px;
  padding: 6px;
  margin: 0 2px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;

const SelectableText = ({ children, theme, onCopy, onDelete, onStar, onForward }) => {
  const [showToolbar, setShowToolbar] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [toolbarPosition, setToolbarPosition] = useState({ top: 0, left: 0 });
  const containerRef = useRef(null);

  const handleSelection = () => {
    const selection = window.getSelection();

    if (selection.toString().trim().length > 0) {
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      const containerRect = containerRef.current.getBoundingClientRect();

      setSelectedText(selection.toString());
      setToolbarPosition({
        top: rect.top - containerRect.top,
        left: rect.left + (rect.width / 2) - containerRect.left
      });
      setShowToolbar(true);
    } else {
      setShowToolbar(false);
    }
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(selectedText);
    if (onCopy) onCopy(selectedText);
    setShowToolbar(false);
  };

  const handleDelete = () => {
    if (onDelete) onDelete(selectedText);
    setShowToolbar(false);
  };

  const handleStar = () => {
    if (onStar) onStar(selectedText);
    setShowToolbar(false);
  };

  const handleForward = () => {
    if (onForward) onForward(selectedText);
    setShowToolbar(false);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setShowToolbar(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <SelectableContainer
      ref={containerRef}
      onMouseUp={handleSelection}
      onTouchEnd={handleSelection}
      className="selectable-text message-text"
    >
      {children}

      <SelectionToolbar
        $show={showToolbar}
        $position={toolbarPosition}
        theme={theme}
      >
        <ToolbarButton onClick={handleCopy}>
          <FaCopy />
        </ToolbarButton>
        <ToolbarButton onClick={handleForward}>
          <FaShare />
        </ToolbarButton>
        <ToolbarButton onClick={handleStar}>
          <FaStar />
        </ToolbarButton>
        <ToolbarButton onClick={handleDelete}>
          <FaTrash />
        </ToolbarButton>
      </SelectionToolbar>
    </SelectableContainer>
  );
};

export default SelectableText;
