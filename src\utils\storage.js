// JSON Storage Utility for WhatsApp Clone
class StorageManager {
  constructor() {
    this.storageKey = 'whatsapp_clone_data';
    this.init();
  }

  init() {
    // Initialize storage if it doesn't exist
    if (!localStorage.getItem(this.storageKey)) {
      const initialData = {
        chats: [],
        messages: {},
        media: {},
        contacts: [],
        files: {},
        callHistory: [],
        settings: {
          theme: 'default',
          notifications: true,
          privacy: {
            lastSeen: true,
            profilePhoto: true,
            about: true,
            status: true
          },
          camera: {
            quality: 'high',
            flashMode: 'auto',
            effects: true,
            location: false
          },
          storage: {
            autoDownload: true,
            mediaQuality: 'high',
            cacheSize: '100MB'
          }
        },
        user: null,
        drafts: {},
        starredMessages: [],
        archivedChats: [],
        blockedContacts: [],
        version: '1.0.0',
        lastUpdated: Date.now()
      };
      this.saveData(initialData);
    }
  }

  // Get all data
  getData() {
    try {
      const data = localStorage.getItem(this.storageKey);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Error reading storage:', error);
      return null;
    }
  }

  // Save all data
  saveData(data) {
    try {
      data.lastUpdated = Date.now();
      localStorage.setItem(this.storageKey, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('Error saving storage:', error);
      return false;
    }
  }

  // Get specific section
  getSection(section) {
    const data = this.getData();
    return data ? data[section] : null;
  }

  // Update specific section
  updateSection(section, value) {
    const data = this.getData();
    if (data) {
      data[section] = value;
      return this.saveData(data);
    }
    return false;
  }

  // Chat operations
  saveChat(chat) {
    const data = this.getData();
    if (data) {
      const existingIndex = data.chats.findIndex(c => c.id === chat.id);
      if (existingIndex >= 0) {
        data.chats[existingIndex] = { ...data.chats[existingIndex], ...chat };
      } else {
        data.chats.push(chat);
      }
      return this.saveData(data);
    }
    return false;
  }

  getChats() {
    return this.getSection('chats') || [];
  }

  deleteChat(chatId) {
    const data = this.getData();
    if (data) {
      data.chats = data.chats.filter(c => c.id !== chatId);
      delete data.messages[chatId];
      return this.saveData(data);
    }
    return false;
  }

  // Message operations
  saveMessage(chatId, message) {
    const data = this.getData();
    if (data) {
      if (!data.messages[chatId]) {
        data.messages[chatId] = [];
      }
      data.messages[chatId].push({
        ...message,
        id: message.id || Date.now() + Math.random(),
        timestamp: message.timestamp || Date.now()
      });
      return this.saveData(data);
    }
    return false;
  }

  getMessages(chatId) {
    const data = this.getData();
    return data && data.messages[chatId] ? data.messages[chatId] : [];
  }

  deleteMessage(chatId, messageId) {
    const data = this.getData();
    if (data && data.messages[chatId]) {
      data.messages[chatId] = data.messages[chatId].filter(m => m.id !== messageId);
      return this.saveData(data);
    }
    return false;
  }

  updateMessage(chatId, messageId, updates) {
    const data = this.getData();
    if (data && data.messages[chatId]) {
      const messageIndex = data.messages[chatId].findIndex(m => m.id === messageId);
      if (messageIndex >= 0) {
        data.messages[chatId][messageIndex] = {
          ...data.messages[chatId][messageIndex],
          ...updates
        };
        return this.saveData(data);
      }
    }
    return false;
  }

  // Media operations
  saveMedia(mediaId, mediaData) {
    const data = this.getData();
    if (data) {
      if (!data.media) data.media = {};
      data.media[mediaId] = {
        ...mediaData,
        id: mediaId,
        uploadedAt: Date.now()
      };
      return this.saveData(data);
    }
    return false;
  }

  getMedia(mediaId) {
    const data = this.getData();
    return data && data.media ? data.media[mediaId] : null;
  }

  // Starred messages
  starMessage(chatId, messageId) {
    const data = this.getData();
    if (data) {
      if (!data.starredMessages) data.starredMessages = [];
      const starredMessage = { chatId, messageId, starredAt: Date.now() };
      if (!data.starredMessages.find(s => s.chatId === chatId && s.messageId === messageId)) {
        data.starredMessages.push(starredMessage);
      }
      return this.saveData(data);
    }
    return false;
  }

  unstarMessage(chatId, messageId) {
    const data = this.getData();
    if (data && data.starredMessages) {
      data.starredMessages = data.starredMessages.filter(
        s => !(s.chatId === chatId && s.messageId === messageId)
      );
      return this.saveData(data);
    }
    return false;
  }

  getStarredMessages() {
    return this.getSection('starredMessages') || [];
  }

  // Draft messages
  saveDraft(chatId, draft) {
    const data = this.getData();
    if (data) {
      if (!data.drafts) data.drafts = {};
      data.drafts[chatId] = {
        text: draft,
        savedAt: Date.now()
      };
      return this.saveData(data);
    }
    return false;
  }

  getDraft(chatId) {
    const data = this.getData();
    return data && data.drafts ? data.drafts[chatId] : null;
  }

  clearDraft(chatId) {
    const data = this.getData();
    if (data && data.drafts) {
      delete data.drafts[chatId];
      return this.saveData(data);
    }
    return false;
  }

  // Settings
  updateSettings(settings) {
    const data = this.getData();
    if (data) {
      data.settings = { ...data.settings, ...settings };
      return this.saveData(data);
    }
    return false;
  }

  getSettings() {
    return this.getSection('settings') || {};
  }

  // Export/Import
  exportData() {
    return this.getData();
  }

  importData(importedData) {
    try {
      return this.saveData(importedData);
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }

  // Clear all data
  clearAll() {
    localStorage.removeItem(this.storageKey);
    this.init();
    return true;
  }

  // Contacts operations
  saveContact(contact) {
    const data = this.getData();
    if (data) {
      if (!data.contacts) data.contacts = [];
      const existingIndex = data.contacts.findIndex(c => c.id === contact.id);
      if (existingIndex >= 0) {
        data.contacts[existingIndex] = { ...data.contacts[existingIndex], ...contact };
      } else {
        data.contacts.push({
          ...contact,
          id: contact.id || Date.now() + Math.random(),
          addedAt: Date.now()
        });
      }
      return this.saveData(data);
    }
    return false;
  }

  getContacts() {
    return this.getSection('contacts') || [];
  }

  deleteContact(contactId) {
    const data = this.getData();
    if (data && data.contacts) {
      data.contacts = data.contacts.filter(c => c.id !== contactId);
      return this.saveData(data);
    }
    return false;
  }

  // File operations
  saveFile(fileId, fileData) {
    const data = this.getData();
    if (data) {
      if (!data.files) data.files = {};
      data.files[fileId] = {
        ...fileData,
        id: fileId,
        savedAt: Date.now()
      };
      return this.saveData(data);
    }
    return false;
  }

  getFile(fileId) {
    const data = this.getData();
    return data && data.files ? data.files[fileId] : null;
  }

  getAllFiles() {
    const data = this.getData();
    return data && data.files ? Object.values(data.files) : [];
  }

  deleteFile(fileId) {
    const data = this.getData();
    if (data && data.files) {
      delete data.files[fileId];
      return this.saveData(data);
    }
    return false;
  }

  // Call history operations
  saveCall(callData) {
    const data = this.getData();
    if (data) {
      if (!data.callHistory) data.callHistory = [];
      data.callHistory.unshift({
        ...callData,
        id: callData.id || Date.now() + Math.random(),
        timestamp: callData.timestamp || Date.now()
      });
      // Keep only last 100 calls
      if (data.callHistory.length > 100) {
        data.callHistory = data.callHistory.slice(0, 100);
      }
      return this.saveData(data);
    }
    return false;
  }

  getCallHistory() {
    return this.getSection('callHistory') || [];
  }

  clearCallHistory() {
    return this.updateSection('callHistory', []);
  }

  // Cache management
  clearCache() {
    const data = this.getData();
    if (data) {
      data.media = {};
      data.files = {};
      return this.saveData(data);
    }
    return false;
  }

  // Get storage size
  getStorageSize() {
    const data = localStorage.getItem(this.storageKey);
    return data ? new Blob([data]).size : 0;
  }

  // Get storage usage breakdown
  getStorageBreakdown() {
    const data = this.getData();
    if (!data) return {};

    const breakdown = {
      total: this.getStorageSize(),
      chats: new Blob([JSON.stringify(data.chats || [])]).size,
      messages: new Blob([JSON.stringify(data.messages || {})]).size,
      media: new Blob([JSON.stringify(data.media || {})]).size,
      files: new Blob([JSON.stringify(data.files || {})]).size,
      contacts: new Blob([JSON.stringify(data.contacts || [])]).size,
      settings: new Blob([JSON.stringify(data.settings || {})]).size
    };

    return breakdown;
  }

  // Backup and restore
  createBackup() {
    const data = this.getData();
    if (data) {
      const backup = {
        ...data,
        backupDate: Date.now(),
        version: data.version || '1.0.0'
      };
      return JSON.stringify(backup);
    }
    return null;
  }

  restoreBackup(backupString) {
    try {
      const backup = JSON.parse(backupString);
      if (backup && backup.chats !== undefined) {
        return this.saveData(backup);
      }
      return false;
    } catch (error) {
      console.error('Error restoring backup:', error);
      return false;
    }
  }
}

// Create singleton instance
const storage = new StorageManager();

export default storage;
