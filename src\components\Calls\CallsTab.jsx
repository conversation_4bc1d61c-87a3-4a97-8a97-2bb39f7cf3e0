import React, { useState } from 'react';
import styled from 'styled-components';
import { FaPhone, FaVideo, FaPhoneAlt, FaVideoSlash, FaInfoCircle } from 'react-icons/fa';

const CallsContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: ${props => props.theme.colors.background};
`;

const CallsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: ${props => props.theme.colors.secondary};
`;

const HeaderTitle = styled.h2`
  margin: 0;
  font-size: 1.2rem;
  color: ${props => props.theme.colors.text};
`;

const NewCallButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.primary};
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  
  span {
    font-size: 14px;
    font-weight: 500;
  }
`;

const TabsContainer = styled.div`
  display: flex;
  background-color: ${props => props.theme.colors.secondary};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const Tab = styled.div`
  flex: 1;
  padding: 12px;
  text-align: center;
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
  font-weight: ${props => props.$active ? '600' : '400'};
  border-bottom: 2px solid ${props => props.$active ? props.theme.colors.primary : 'transparent'};
  cursor: pointer;
`;

const CallsList = styled.div`
  flex: 1;
  overflow-y: auto;
`;

const CallItem = styled.div`
  display: flex;
  padding: 16px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  cursor: pointer;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const CallAvatar = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
  
  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }
`;

const CallInfo = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const CallName = styled.div`
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 4px;
`;

const CallDetails = styled.div`
  display: flex;
  align-items: center;
  color: ${props => props.$missed ? '#e74c3c' : props.theme.colors.secondaryText};
  font-size: 14px;
  
  svg {
    margin-right: 5px;
    transform: ${props => props.$incoming ? 'rotate(135deg)' : 'rotate(-45deg)'};
  }
`;

const CallTime = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 14px;
  margin-left: auto;
  display: flex;
  align-items: center;
`;

const CallButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.primary};
  font-size: 20px;
  cursor: pointer;
  margin-left: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    opacity: 0.8;
  }
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
  text-align: center;
  color: ${props => props.theme.colors.secondaryText};
`;

const EmptyIcon = styled.div`
  font-size: 50px;
  margin-bottom: 20px;
  color: ${props => props.theme.colors.border};
`;

const EmptyTitle = styled.div`
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 10px;
  color: ${props => props.theme.colors.text};
`;

const EmptyText = styled.div`
  font-size: 14px;
  max-width: 300px;
  line-height: 1.5;
`;

// Mock data for calls
const mockCalls = [
  {
    id: 1,
    name: 'John Doe',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
    type: 'video',
    direction: 'outgoing',
    missed: false,
    time: '10:30 AM',
    date: 'Today'
  },
  {
    id: 2,
    name: 'Jane Smith',
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
    type: 'audio',
    direction: 'incoming',
    missed: true,
    time: 'Yesterday',
    date: 'Yesterday'
  },
  {
    id: 3,
    name: 'Mike Johnson',
    image: 'https://randomuser.me/api/portraits/men/67.jpg',
    type: 'audio',
    direction: 'outgoing',
    missed: false,
    time: '3:45 PM',
    date: 'Yesterday'
  },
  {
    id: 4,
    name: 'Sarah Williams',
    image: 'https://randomuser.me/api/portraits/women/63.jpg',
    type: 'video',
    direction: 'incoming',
    missed: false,
    time: '5:20 PM',
    date: '06/15/2023'
  }
];

const CallsTab = ({ theme }) => {
  const [activeTab, setActiveTab] = useState('all');
  const [calls, setCalls] = useState(mockCalls);
  
  const filteredCalls = activeTab === 'all' 
    ? calls 
    : activeTab === 'missed' 
      ? calls.filter(call => call.missed) 
      : [];
  
  const getInitials = (name) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };
  
  return (
    <CallsContainer theme={theme}>
      <CallsHeader theme={theme}>
        <HeaderTitle theme={theme}>Calls</HeaderTitle>
        <NewCallButton theme={theme}>
          <FaPhone />
          <span>New Call</span>
        </NewCallButton>
      </CallsHeader>
      
      <TabsContainer theme={theme}>
        <Tab 
          $active={activeTab === 'all'} 
          onClick={() => setActiveTab('all')}
          theme={theme}
        >
          All
        </Tab>
        <Tab 
          $active={activeTab === 'missed'} 
          onClick={() => setActiveTab('missed')}
          theme={theme}
        >
          Missed
        </Tab>
      </TabsContainer>
      
      <CallsList>
        {filteredCalls.length > 0 ? (
          filteredCalls.map(call => (
            <CallItem key={call.id} theme={theme}>
              <CallAvatar theme={theme}>
                {call.image ? (
                  <img src={call.image} alt={call.name} />
                ) : (
                  getInitials(call.name)
                )}
              </CallAvatar>
              <CallInfo>
                <CallName theme={theme}>{call.name}</CallName>
                <CallDetails 
                  $missed={call.missed} 
                  $incoming={call.direction === 'incoming'}
                  theme={theme}
                >
                  <FaPhone />
                  {call.direction === 'incoming' ? 'Incoming' : 'Outgoing'} {call.missed ? 'missed' : ''} {call.type} call
                </CallDetails>
              </CallInfo>
              <CallTime theme={theme}>
                {call.time}
                <CallButton theme={theme}>
                  {call.type === 'video' ? <FaVideo /> : <FaPhoneAlt />}
                </CallButton>
              </CallTime>
            </CallItem>
          ))
        ) : (
          <EmptyState theme={theme}>
            <EmptyIcon theme={theme}>
              {activeTab === 'all' ? <FaPhone /> : <FaVideoSlash />}
            </EmptyIcon>
            <EmptyTitle theme={theme}>No {activeTab === 'all' ? 'calls' : 'missed calls'}</EmptyTitle>
            <EmptyText theme={theme}>
              {activeTab === 'all' 
                ? 'Start making calls to your contacts' 
                : 'You don\'t have any missed calls'}
            </EmptyText>
          </EmptyState>
        )}
      </CallsList>
    </CallsContainer>
  );
};

export default CallsTab;
