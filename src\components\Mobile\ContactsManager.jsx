import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaUser, 
  FaPhone, 
  FaVideo, 
  FaComment, 
  FaPlus,
  FaSearch,
  FaEdit,
  FaTrash,
  FaStar,
  FaRegStar,
  FaTimes
} from 'react-icons/fa';
import storage from '../../utils/storage';
import MobileHaptics from './MobileHaptics';

const ContactsContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${props => props.theme.colors.background};
  z-index: 1000;
  display: flex;
  flex-direction: column;
`;

const ContactsHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  min-height: 60px;
`;

const HeaderTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
`;

const HeaderButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const SearchContainer = styled.div`
  padding: 16px;
  background-color: ${props => props.theme.colors.secondary};
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 25px;
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 16px;
  outline: none;
  
  &::placeholder {
    color: ${props => props.theme.colors.secondaryText};
  }
`;

const ContactsList = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
`;

const ContactItem = styled.div`
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const ContactAvatar = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  font-weight: 600;
  margin-right: 16px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const ContactInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const ContactName = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const ContactPhone = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.secondaryText};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const ContactActions = styled.div`
  display: flex;
  gap: 8px;
`;

const ActionButton = styled.button`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background-color: ${props => props.color || props.theme.colors.secondary};
  color: ${props => props.color ? 'white' : props.theme.colors.icon};
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    transform: scale(1.1);
  }
  
  &:active {
    transform: scale(0.95);
  }
`;

const AddContactFAB = styled.button`
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
  
  &:active {
    transform: scale(0.95);
  }
`;

const ContactModal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  padding: 20px;
`;

const ModalContent = styled.div`
  background-color: ${props => props.theme.colors.background};
  border-radius: 12px;
  padding: 24px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow-y: auto;
`;

const ModalTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: 20px;
  text-align: center;
`;

const FormGroup = styled.div`
  margin-bottom: 16px;
`;

const FormLabel = styled.label`
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 8px;
`;

const FormInput = styled.input`
  width: 100%;
  padding: 12px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 8px;
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 16px;
  outline: none;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
  }
  
  &::placeholder {
    color: ${props => props.theme.colors.secondaryText};
  }
`;

const ModalActions = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 24px;
`;

const ModalButton = styled.button`
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  
  ${props => props.$primary ? `
    background-color: ${props.theme.colors.primary};
    color: white;
    
    &:hover {
      opacity: 0.9;
    }
  ` : `
    background-color: ${props.theme.colors.secondary};
    color: ${props.theme.colors.text};
    
    &:hover {
      background-color: ${props.theme.colors.border};
    }
  `}
`;

const ContactsManager = ({ theme, onClose, onSelectContact }) => {
  const [contacts, setContacts] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingContact, setEditingContact] = useState(null);
  const [newContact, setNewContact] = useState({
    name: '',
    phone: '',
    email: '',
    avatar: ''
  });

  useEffect(() => {
    loadContacts();
  }, []);

  const loadContacts = () => {
    const savedContacts = storage.getContacts();
    setContacts(savedContacts);
  };

  const getInitials = (name) => {
    return name?.split(' ').map(n => n[0]).join('').toUpperCase() || '?';
  };

  const filteredContacts = contacts.filter(contact =>
    contact.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.phone?.includes(searchQuery)
  );

  const handleAddContact = () => {
    MobileHaptics.light();
    setEditingContact(null);
    setNewContact({ name: '', phone: '', email: '', avatar: '' });
    setShowAddModal(true);
  };

  const handleEditContact = (contact) => {
    MobileHaptics.light();
    setEditingContact(contact);
    setNewContact({ ...contact });
    setShowAddModal(true);
  };

  const handleSaveContact = () => {
    if (!newContact.name.trim() || !newContact.phone.trim()) {
      return;
    }

    MobileHaptics.success();
    
    const contactData = {
      ...newContact,
      id: editingContact?.id || Date.now() + Math.random(),
      name: newContact.name.trim(),
      phone: newContact.phone.trim(),
      email: newContact.email.trim()
    };

    storage.saveContact(contactData);
    loadContacts();
    setShowAddModal(false);
    setNewContact({ name: '', phone: '', email: '', avatar: '' });
  };

  const handleDeleteContact = (contactId) => {
    MobileHaptics.heavy();
    storage.deleteContact(contactId);
    loadContacts();
  };

  const handleCall = (contact) => {
    MobileHaptics.medium();
    // Handle voice call
    console.log('Calling:', contact.name);
  };

  const handleVideoCall = (contact) => {
    MobileHaptics.medium();
    // Handle video call
    console.log('Video calling:', contact.name);
  };

  const handleMessage = (contact) => {
    MobileHaptics.light();
    if (onSelectContact) {
      onSelectContact(contact);
    }
  };

  return (
    <ContactsContainer theme={theme}>
      <ContactsHeader theme={theme}>
        <HeaderButton onClick={onClose}>
          <FaTimes />
        </HeaderButton>
        <HeaderTitle>Contacts</HeaderTitle>
        <HeaderButton onClick={handleAddContact}>
          <FaPlus />
        </HeaderButton>
      </ContactsHeader>

      <SearchContainer theme={theme}>
        <SearchInput
          type="text"
          placeholder="Search contacts..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          theme={theme}
        />
      </SearchContainer>

      <ContactsList>
        {filteredContacts.map((contact) => (
          <ContactItem key={contact.id} theme={theme}>
            <ContactAvatar theme={theme}>
              {contact.avatar ? (
                <img src={contact.avatar} alt={contact.name} />
              ) : (
                getInitials(contact.name)
              )}
            </ContactAvatar>
            
            <ContactInfo>
              <ContactName theme={theme}>{contact.name}</ContactName>
              <ContactPhone theme={theme}>{contact.phone}</ContactPhone>
            </ContactInfo>
            
            <ContactActions>
              <ActionButton
                color="#25d366"
                onClick={() => handleMessage(contact)}
                theme={theme}
              >
                <FaComment />
              </ActionButton>
              
              <ActionButton
                color="#007bff"
                onClick={() => handleCall(contact)}
                theme={theme}
              >
                <FaPhone />
              </ActionButton>
              
              <ActionButton
                color="#ff6b6b"
                onClick={() => handleVideoCall(contact)}
                theme={theme}
              >
                <FaVideo />
              </ActionButton>
              
              <ActionButton
                onClick={() => handleEditContact(contact)}
                theme={theme}
              >
                <FaEdit />
              </ActionButton>
            </ContactActions>
          </ContactItem>
        ))}
        
        {filteredContacts.length === 0 && (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px 20px',
            color: theme.colors.secondaryText 
          }}>
            {searchQuery ? 'No contacts found' : 'No contacts yet'}
          </div>
        )}
      </ContactsList>

      <AddContactFAB theme={theme} onClick={handleAddContact}>
        <FaPlus />
      </AddContactFAB>

      {showAddModal && (
        <ContactModal>
          <ModalContent theme={theme}>
            <ModalTitle theme={theme}>
              {editingContact ? 'Edit Contact' : 'Add Contact'}
            </ModalTitle>
            
            <FormGroup>
              <FormLabel theme={theme}>Name *</FormLabel>
              <FormInput
                type="text"
                placeholder="Enter name"
                value={newContact.name}
                onChange={(e) => setNewContact({ ...newContact, name: e.target.value })}
                theme={theme}
              />
            </FormGroup>
            
            <FormGroup>
              <FormLabel theme={theme}>Phone *</FormLabel>
              <FormInput
                type="tel"
                placeholder="Enter phone number"
                value={newContact.phone}
                onChange={(e) => setNewContact({ ...newContact, phone: e.target.value })}
                theme={theme}
              />
            </FormGroup>
            
            <FormGroup>
              <FormLabel theme={theme}>Email</FormLabel>
              <FormInput
                type="email"
                placeholder="Enter email"
                value={newContact.email}
                onChange={(e) => setNewContact({ ...newContact, email: e.target.value })}
                theme={theme}
              />
            </FormGroup>
            
            <ModalActions>
              <ModalButton
                onClick={() => setShowAddModal(false)}
                theme={theme}
              >
                Cancel
              </ModalButton>
              <ModalButton
                $primary
                onClick={handleSaveContact}
                theme={theme}
              >
                {editingContact ? 'Update' : 'Add'}
              </ModalButton>
            </ModalActions>
          </ModalContent>
        </ContactModal>
      )}
    </ContactsContainer>
  );
};

export default ContactsManager;
