import React, { useState } from 'react';
import styled from 'styled-components';
import ChatHeader from './ChatHeader';
import MessageList from './MessageList';
import ChatInput from './ChatInput';
import { useTheme } from '../../context/ThemeContext';
import { FaArrowLeft } from 'react-icons/fa';

const ChatWindowContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 70%;
  height: 100%;
  background-color: ${props => props.theme.colors.chatBackground};
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='600' viewBox='0 0 600 600'%3E%3Cpath fill='%23ffffff10' d='M600 325.1v-170.2l-50-29.1-50 29.1v170.2l50 29.1 50-29.1zm-150 0v-170.2l-50-29.1-50 29.1v170.2l50 29.1 50-29.1zm-150-141.1v-170.2l-50-29.1-50 29.1v170.2l50 29.1 50-29.1zm-150 0v-170.2l-50-29.1-50 29.1v170.2l50 29.1 50-29.1zm-150 141.1v-170.2l-50-29.1-50 29.1v170.2l50 29.1 50-29.1z'/%3E%3C/svg%3E");
  background-repeat: repeat;
  background-size: 200px;

  /* Mobile view */
  @media (max-width: 768px) {
    width: 100%;
    display: ${props => props.$isMobileView && !props.$activeChat ? 'none' : 'flex'};
  }

  /* Tablet and small desktop */
  @media (min-width: 769px) and (max-width: 1199px) {
    width: 65%;
  }

  /* Medium desktop */
  @media (min-width: 1200px) and (max-width: 1599px) {
    width: 70%;
  }

  /* Large desktop */
  @media (min-width: 1600px) and (max-width: 1999px) {
    width: 75%;
  }

  /* Ultra wide */
  @media (min-width: 2000px) {
    width: 80%;
  }
`;

const EmptyStateContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: ${props => props.theme.colors.secondaryText};
  background-color: ${props => props.theme.colors.background};
`;

const EmptyStateTitle = styled.h2`
  font-size: 32px;
  font-weight: 300;
  margin-bottom: 20px;
  color: ${props => props.theme.colors.text};
`;

const EmptyStateText = styled.p`
  font-size: 14px;
  text-align: center;
  max-width: 500px;
  line-height: 20px;
  color: ${props => props.theme.colors.secondaryText};
`;

const BackButton = styled.button`
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  margin-right: 10px;

  @media (max-width: 768px) {
    display: block;
  }
`;

const ChatWindow = ({ activeChat, messages, sendMessage, isMobileView, onBackClick }) => {
  const { currentTheme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');

  if (!activeChat) {
    return (
      <EmptyStateContainer theme={currentTheme}>
        <EmptyStateTitle theme={currentTheme}>WhatsApp Clone</EmptyStateTitle>
        <EmptyStateText theme={currentTheme}>
          Select a chat to start messaging
        </EmptyStateText>
      </EmptyStateContainer>
    );
  }

  return (
    <ChatWindowContainer
      theme={currentTheme}
      $isMobileView={isMobileView}
      $activeChat={activeChat}
    >
      <ChatHeader
        chat={activeChat}
        showBackButton={isMobileView}
        onBackClick={onBackClick}
        onSearch={setSearchQuery}
      />
      <MessageList
        messages={messages}
        isGroupChat={activeChat?.isGroup}
        searchQuery={searchQuery}
      />
      <ChatInput onSendMessage={(text, voiceMessage) => {
        if (voiceMessage) {
          sendMessage(text, { type: 'voice', duration: voiceMessage.duration });
        } else {
          sendMessage(text);
        }
      }} />
    </ChatWindowContainer>
  );
};

export default ChatWindow;
