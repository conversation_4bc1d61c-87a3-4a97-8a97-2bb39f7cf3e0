import React, { useState } from 'react';
import styled from 'styled-components';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON>gle<PERSON>n,
  FaToggleOff,
  FaImage,
  FaCommentAlt,
  FaRegCircle,
  FaRegSquare,
  FaRegDotCircle,
  FaEllipsisH,
  FaArrowLeft
} from 'react-icons/fa';

const ChatStyleContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${props => props.theme.colors.background};
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
`;

const SettingsHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  min-height: 60px;
  position: sticky;
  top: 0;
  z-index: 10;
`;

const HeaderTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
`;

const HeaderButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const SettingsContent = styled.div`
  flex: 1;
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const SettingsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const SettingItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const SettingInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const IconWrapper = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary}20;
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    color: ${props => props.color || props.theme.colors.primary};
    font-size: 18px;
  }
`;

const SettingText = styled.div`
  display: flex;
  flex-direction: column;
`;

const SettingName = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 0.95rem;
`;

const SettingDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 0.8rem;
  margin-top: 4px;
`;

const ToggleButton = styled.div`
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
  font-size: 1.5rem;
  cursor: pointer;
`;

const ColorPalette = styled.div`
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
  margin-top: 10px;
`;

const ColorOption = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.color};
  cursor: pointer;
  position: relative;
  border: 2px solid ${props => props.$selected ? props.theme.colors.primary : 'transparent'};
  transition: transform 0.2s;
  
  &:hover {
    transform: scale(1.1);
  }
  
  svg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    display: ${props => props.$selected ? 'block' : 'none'};
  }
`;

const BubbleShapeOptions = styled.div`
  display: flex;
  gap: 15px;
  margin-top: 10px;
`;

const BubbleOption = styled.div`
  width: 60px;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  cursor: pointer;
`;

const BubbleShape = styled.div`
  width: 50px;
  height: 30px;
  background-color: ${props => props.theme.colors.primary};
  border-radius: ${props => {
    switch(props.$shape) {
      case 'rounded': return '15px';
      case 'square': return '5px';
      case 'circle': return '20px';
      default: return '15px';
    }
  }};
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: ${props => props.$shape === 'circle' ? '5px' : '0'};
    left: ${props => props.$shape === 'circle' ? '-5px' : '-8px'};
    width: 0;
    height: 0;
    border-right: 10px solid ${props => props.theme.colors.primary};
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    display: ${props => props.$shape === 'square' ? 'none' : 'block'};
    border-radius: ${props => props.$shape === 'circle' ? '50%' : '0'};
  }
`;

const BubbleLabel = styled.div`
  font-size: 12px;
  color: ${props => props.$selected ? props.theme.colors.primary : props.theme.colors.text};
  font-weight: ${props => props.$selected ? '500' : 'normal'};
`;

const BackgroundOptions = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-top: 10px;
`;

const BackgroundOption = styled.div`
  height: 60px;
  border-radius: 8px;
  background-image: ${props => props.$image ? `url(${props.$image})` : 'none'};
  background-color: ${props => !props.$image ? props.theme.colors.secondary : 'transparent'};
  background-size: cover;
  background-position: center;
  cursor: pointer;
  position: relative;
  border: 2px solid ${props => props.$selected ? props.theme.colors.primary : 'transparent'};
  
  &:hover {
    opacity: 0.9;
  }
  
  svg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    display: ${props => props.$selected ? 'block' : 'none'};
    background-color: ${props => props.theme.colors.primary};
    border-radius: 50%;
    padding: 2px;
  }
`;

const PreviewContainer = styled.div`
  margin-top: 20px;
  padding: 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const PreviewTitle = styled.div`
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  
  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const ChatPreview = styled.div`
  background-color: ${props => props.theme.colors.chatBackground};
  background-image: ${props => props.$backgroundImage ? `url(${props.$backgroundImage})` : 'none'};
  background-size: cover;
  background-position: center;
  border-radius: 8px;
  padding: 15px;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  gap: 10px;
`;

const MessageBubble = styled.div`
  max-width: 70%;
  padding: 10px 15px;
  background-color: ${props => props.$isSent ? props.$bubbleColor : props.theme.colors.receivedMessage};
  color: ${props => props.$isSent && props.$bubbleColor !== '#25D366' ? 'white' : props.theme.colors.text};
  align-self: ${props => props.$isSent ? 'flex-end' : 'flex-start'};
  border-radius: ${props => {
    switch(props.$shape) {
      case 'rounded': return '15px';
      case 'square': return '5px';
      case 'circle': return '20px';
      default: return '15px';
    }
  }};
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: ${props => props.$shape === 'circle' ? '5px' : '10px'};
    ${props => props.$isSent ? 'right: -8px' : 'left: -8px'};
    width: 0;
    height: 0;
    border-${props => props.$isSent ? 'left' : 'right'}: 10px solid ${props => props.$isSent ? props.$bubbleColor : props.theme.colors.receivedMessage};
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    display: ${props => props.$shape === 'square' ? 'none' : 'block'};
    border-radius: ${props => props.$shape === 'circle' ? '50%' : '0'};
  }
`;

const ChatStyleSettings = ({ theme, onClose }) => {
  const [settings, setSettings] = useState({
    bubbleColor: '#25D366',
    bubbleShape: 'rounded',
    backgroundImage: '',
    enableCustomBackground: false,
    enableGradientBubbles: false
  });
  
  const colorOptions = [
    '#25D366', // Default WhatsApp green
    '#4CAF50', // Material Green
    '#2196F3', // Blue
    '#9C27B0', // Purple
    '#E91E63', // Pink
    '#F44336', // Red
    '#FF9800', // Orange
    '#607D8B', // Blue Grey
    '#795548', // Brown
    '#000000'  // Black
  ];
  
  const backgroundOptions = [
    '',
    'https://i.pinimg.com/736x/8c/98/99/8c98994518b575bfd8c949e91d20548b.jpg',
    'https://i.pinimg.com/736x/51/ed/c0/51edc046eb80046ee4755ee71d0f19ca.jpg',
    'https://i.pinimg.com/736x/b6/f2/36/b6f2362c9c8a5b86bffa84abee7a12a7.jpg',
    'https://i.pinimg.com/736x/e5/18/f9/e518f9563a84d45f2c4a378e0ebe7e28.jpg',
    'https://i.pinimg.com/736x/e0/26/5b/e0265b5d2d4894c7dc0de85a6f6f321f.jpg'
  ];
  
  const bubbleShapes = [
    { name: 'Rounded', value: 'rounded', icon: <FaRegCircle /> },
    { name: 'Square', value: 'square', icon: <FaRegSquare /> },
    { name: 'Circle', value: 'circle', icon: <FaRegDotCircle /> }
  ];
  
  const handleColorChange = (color) => {
    setSettings(prev => ({
      ...prev,
      bubbleColor: color
    }));
  };
  
  const handleShapeChange = (shape) => {
    setSettings(prev => ({
      ...prev,
      bubbleShape: shape
    }));
  };
  
  const handleBackgroundChange = (image) => {
    setSettings(prev => ({
      ...prev,
      backgroundImage: image
    }));
  };
  
  const toggleCustomBackground = () => {
    setSettings(prev => ({
      ...prev,
      enableCustomBackground: !prev.enableCustomBackground
    }));
  };
  
  const toggleGradientBubbles = () => {
    setSettings(prev => ({
      ...prev,
      enableGradientBubbles: !prev.enableGradientBubbles
    }));
  };
  
  return (
    <ChatStyleContainer theme={theme}>
      <SettingsHeader theme={theme}>
        <HeaderButton onClick={onClose}>
          <FaArrowLeft />
        </HeaderButton>
        <HeaderTitle>Chat Wallpaper & Style</HeaderTitle>
        <HeaderButton onClick={() => {}}>
          {/* Placeholder for future action */}
        </HeaderButton>
      </SettingsHeader>

      <SettingsContent>
        <SectionTitle theme={theme}>Chat Style Settings</SectionTitle>

        <SettingsList>
        <SettingItem theme={theme}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#4CAF50">
              <FaPalette />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Bubble Color</SettingName>
              <SettingDescription theme={theme}>
                Change the color of your message bubbles
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <div style={{ 
            width: 24, 
            height: 24, 
            borderRadius: '50%', 
            backgroundColor: settings.bubbleColor 
          }} />
        </SettingItem>
        
        <ColorPalette>
          {colorOptions.map(color => (
            <ColorOption 
              key={color} 
              color={color}
              $selected={settings.bubbleColor === color}
              onClick={() => handleColorChange(color)}
              theme={theme}
            >
              <FaCheck />
            </ColorOption>
          ))}
        </ColorPalette>
        
        <SettingItem theme={theme}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#2196F3">
              <FaCommentAlt />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Bubble Shape</SettingName>
              <SettingDescription theme={theme}>
                Change the shape of message bubbles
              </SettingDescription>
            </SettingText>
          </SettingInfo>
        </SettingItem>
        
        <BubbleShapeOptions>
          {bubbleShapes.map(shape => (
            <BubbleOption 
              key={shape.value} 
              onClick={() => handleShapeChange(shape.value)}
            >
              <BubbleShape 
                $shape={shape.value}
                theme={theme}
              />
              <BubbleLabel 
                $selected={settings.bubbleShape === shape.value}
                theme={theme}
              >
                {shape.name}
              </BubbleLabel>
            </BubbleOption>
          ))}
        </BubbleShapeOptions>
        
        <SettingItem theme={theme} onClick={toggleGradientBubbles}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#9C27B0">
              <FaPalette />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Gradient Bubbles</SettingName>
              <SettingDescription theme={theme}>
                Enable gradient effect for message bubbles
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.enableGradientBubbles} 
            theme={theme}
          >
            {settings.enableGradientBubbles ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={toggleCustomBackground}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#FF9800">
              <FaImage />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Custom Background</SettingName>
              <SettingDescription theme={theme}>
                Set a custom background image for chats
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.enableCustomBackground} 
            theme={theme}
          >
            {settings.enableCustomBackground ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        {settings.enableCustomBackground && (
          <>
            <SettingItem theme={theme}>
              <SettingInfo>
                <IconWrapper theme={theme} color="#E91E63">
                  <FaImage />
                </IconWrapper>
                <SettingText>
                  <SettingName theme={theme}>Background Image</SettingName>
                  <SettingDescription theme={theme}>
                    Select a background for your chats
                  </SettingDescription>
                </SettingText>
              </SettingInfo>
            </SettingItem>
            
            <BackgroundOptions>
              {backgroundOptions.map((image, index) => (
                <BackgroundOption 
                  key={index} 
                  $image={image}
                  $selected={settings.backgroundImage === image}
                  onClick={() => handleBackgroundChange(image)}
                  theme={theme}
                >
                  {index === 0 && <FaEllipsisH />}
                  {index !== 0 && <FaCheck />}
                </BackgroundOption>
              ))}
            </BackgroundOptions>
          </>
        )}
      </SettingsList>
      
      <PreviewContainer theme={theme}>
        <PreviewTitle theme={theme}>
          <FaCommentAlt />
          Preview
        </PreviewTitle>
        
        <ChatPreview 
          $backgroundImage={settings.enableCustomBackground ? settings.backgroundImage : ''}
          theme={theme}
        >
          <MessageBubble 
            $isSent={false}
            $shape={settings.bubbleShape}
            theme={theme}
          >
            Hello! How are you doing today?
          </MessageBubble>
          
          <MessageBubble 
            $isSent={true}
            $shape={settings.bubbleShape}
            $bubbleColor={settings.bubbleColor}
            theme={theme}
            style={{
              background: settings.enableGradientBubbles 
                ? `linear-gradient(135deg, ${settings.bubbleColor}, ${adjustColor(settings.bubbleColor, -30)})` 
                : settings.bubbleColor
            }}
          >
            I'm doing great, thanks for asking! How about you?
          </MessageBubble>
        </ChatPreview>
      </PreviewContainer>
      </SettingsContent>
    </ChatStyleContainer>
  );
};

// Helper function to darken/lighten a color
function adjustColor(color, amount) {
  return '#' + color.replace(/^#/, '').replace(/../g, color => 
    ('0' + Math.min(255, Math.max(0, parseInt(color, 16) + amount)).toString(16)).substr(-2)
  );
}

export default ChatStyleSettings;
