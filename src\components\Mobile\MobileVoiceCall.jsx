import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaPhoneSlash, 
  FaPhone, 
  FaMicrophone, 
  FaMicrophoneSlash,
  FaVolumeUp,
  FaVolumeMute,
  FaKeypad,
  FaPlus,
  FaVideo,
  FaEllipsisV
} from 'react-icons/fa';
import MobileHaptics from './MobileHaptics';

const VoiceCallContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  color: white;
  padding: 60px 20px 40px;
`;

const ContactSection = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
`;

const Avatar = styled.div`
  width: 160px;
  height: 160px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 64px;
  font-weight: 600;
  margin-bottom: 30px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const ContactName = styled.h1`
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`;

const CallStatus = styled.div`
  font-size: 18px;
  opacity: 0.9;
  margin-bottom: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
`;

const CallDuration = styled.div`
  font-size: 16px;
  opacity: 0.8;
  font-weight: 500;
`;

const ControlsContainer = styled.div`
  padding: 20px 0;
`;

const ControlsRow = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 25px;
  margin-bottom: 25px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const ControlButton = styled.button`
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  ${props => props.$variant === 'end' && `
    background: #ff4757;
    color: white;
    
    &:hover {
      background: #ff3742;
      transform: scale(1.1);
    }
  `}
  
  ${props => props.$variant === 'accept' && `
    background: #2ed573;
    color: white;
    
    &:hover {
      background: #26d467;
      transform: scale(1.1);
    }
  `}
  
  ${props => props.$variant === 'toggle' && `
    background: ${props.$active ? '#ff4757' : 'rgba(255, 255, 255, 0.2)'};
    color: white;
    
    &:hover {
      background: ${props.$active ? '#ff3742' : 'rgba(255, 255, 255, 0.3)'};
    }
  `}
  
  ${props => props.$variant === 'action' && `
    background: rgba(255, 255, 255, 0.2);
    color: white;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  `}
  
  &:active {
    transform: scale(0.95);
  }
`;

const SmallControlButton = styled.button`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }
  
  &:active {
    transform: scale(0.95);
  }
`;

const PulseAnimation = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    100% {
      transform: scale(1.4);
      opacity: 0;
    }
  }
`;

const MobileVoiceCall = ({ 
  contact, 
  isIncoming = false, 
  onEndCall, 
  onAccept, 
  onDecline 
}) => {
  const [callDuration, setCallDuration] = useState(0);
  const [isMicEnabled, setIsMicEnabled] = useState(true);
  const [isSpeakerOn, setIsSpeakerOn] = useState(false);
  const [callStatus, setCallStatus] = useState(isIncoming ? 'Incoming call...' : 'Calling...');
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    let interval;
    if (isConnected) {
      interval = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isConnected]);

  useEffect(() => {
    if (!isIncoming) {
      setTimeout(() => {
        setCallStatus('Connected');
        setIsConnected(true);
      }, 3000);
    }
  }, [isIncoming]);

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleEndCall = () => {
    MobileHaptics.heavy();
    if (onEndCall) onEndCall();
  };

  const handleAccept = () => {
    MobileHaptics.success();
    setCallStatus('Connected');
    setIsConnected(true);
    if (onAccept) onAccept();
  };

  const handleDecline = () => {
    MobileHaptics.heavy();
    if (onDecline) onDecline();
  };

  const toggleMic = () => {
    MobileHaptics.medium();
    setIsMicEnabled(!isMicEnabled);
  };

  const toggleSpeaker = () => {
    MobileHaptics.light();
    setIsSpeakerOn(!isSpeakerOn);
  };

  const getInitials = (name) => {
    return name?.split(' ').map(n => n[0]).join('').toUpperCase() || '';
  };

  return (
    <VoiceCallContainer>
      <ContactSection>
        <Avatar>
          {contact?.avatar ? (
            <img src={contact.avatar} alt={contact.name} />
          ) : (
            getInitials(contact?.name)
          )}
          {(isIncoming || !isConnected) && <PulseAnimation />}
        </Avatar>
        
        <ContactName>{contact?.name || 'Unknown'}</ContactName>
        <CallStatus>{callStatus}</CallStatus>
        {isConnected && callDuration > 0 && (
          <CallDuration>{formatDuration(callDuration)}</CallDuration>
        )}
      </ContactSection>

      <ControlsContainer>
        {isIncoming ? (
          <ControlsRow>
            <ControlButton $variant="end" onClick={handleDecline}>
              <FaPhoneSlash />
            </ControlButton>
            
            <ControlButton $variant="accept" onClick={handleAccept}>
              <FaPhone />
            </ControlButton>
          </ControlsRow>
        ) : (
          <>
            {isConnected && (
              <ControlsRow>
                <SmallControlButton onClick={toggleSpeaker}>
                  {isSpeakerOn ? <FaVolumeUp /> : <FaVolumeMute />}
                </SmallControlButton>
                
                <SmallControlButton>
                  <FaKeypad />
                </SmallControlButton>
                
                <SmallControlButton>
                  <FaPlus />
                </SmallControlButton>
                
                <SmallControlButton>
                  <FaVideo />
                </SmallControlButton>
              </ControlsRow>
            )}
            
            <ControlsRow>
              {isConnected && (
                <ControlButton 
                  $variant="toggle" 
                  $active={!isMicEnabled} 
                  onClick={toggleMic}
                >
                  {isMicEnabled ? <FaMicrophone /> : <FaMicrophoneSlash />}
                </ControlButton>
              )}
              
              <ControlButton $variant="end" onClick={handleEndCall}>
                <FaPhoneSlash />
              </ControlButton>
              
              {isConnected && (
                <ControlButton $variant="action">
                  <FaEllipsisV />
                </ControlButton>
              )}
            </ControlsRow>
          </>
        )}
      </ControlsContainer>
    </VoiceCallContainer>
  );
};

export default MobileVoiceCall;
