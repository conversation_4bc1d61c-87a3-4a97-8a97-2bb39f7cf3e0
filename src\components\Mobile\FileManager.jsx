import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaFile, 
  FaImage, 
  FaVideo, 
  FaMusic, 
  FaFileAlt,
  FaDownload,
  FaTrash,
  FaShare,
  FaEye,
  FaTimes,
  FaFolder,
  FaSearch,
  FaSort,
  FaFilter
} from 'react-icons/fa';
import storage from '../../utils/storage';
import MobileHaptics from './MobileHaptics';

const FileManagerContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${props => props.theme.colors.background};
  z-index: 1000;
  display: flex;
  flex-direction: column;
`;

const FileManagerHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  min-height: 60px;
`;

const HeaderTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
`;

const HeaderActions = styled.div`
  display: flex;
  gap: 8px;
`;

const HeaderButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const FilterTabs = styled.div`
  display: flex;
  background-color: ${props => props.theme.colors.secondary};
  padding: 8px 16px;
  overflow-x: auto;
`;

const FilterTab = styled.button`
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  background-color: ${props => props.$active ? props.theme.colors.primary : 'transparent'};
  color: ${props => props.$active ? 'white' : props.theme.colors.text};
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  margin-right: 8px;
  white-space: nowrap;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.border};
  }
`;

const SearchContainer = styled.div`
  padding: 16px;
  background-color: ${props => props.theme.colors.secondary};
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 25px;
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 16px;
  outline: none;
  
  &::placeholder {
    color: ${props => props.theme.colors.secondaryText};
  }
`;

const FilesList = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
`;

const FileItem = styled.div`
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const FileIcon = styled.div`
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background-color: ${props => props.color || props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  margin-right: 16px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const FileInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const FileName = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const FileDetails = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.secondaryText};
  display: flex;
  gap: 8px;
`;

const FileActions = styled.div`
  display: flex;
  gap: 8px;
`;

const ActionButton = styled.button`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background-color: ${props => props.color || props.theme.colors.secondary};
  color: ${props => props.color ? 'white' : props.theme.colors.icon};
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    transform: scale(1.1);
  }
  
  &:active {
    transform: scale(0.95);
  }
`;

const StorageInfo = styled.div`
  padding: 16px;
  background-color: ${props => props.theme.colors.secondary};
  border-top: 1px solid ${props => props.theme.colors.border};
`;

const StorageBar = styled.div`
  width: 100%;
  height: 8px;
  background-color: ${props => props.theme.colors.border};
  border-radius: 4px;
  overflow: hidden;
  margin: 8px 0;
`;

const StorageUsed = styled.div`
  height: 100%;
  background-color: ${props => props.theme.colors.primary};
  width: ${props => props.$percentage}%;
  transition: width 0.3s ease;
`;

const StorageText = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.secondaryText};
  text-align: center;
`;

const FileManager = ({ theme, onClose }) => {
  const [files, setFiles] = useState([]);
  const [activeFilter, setActiveFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('date');
  const [storageInfo, setStorageInfo] = useState({});

  const filters = [
    { id: 'all', label: 'All Files', icon: <FaFile /> },
    { id: 'images', label: 'Images', icon: <FaImage /> },
    { id: 'videos', label: 'Videos', icon: <FaVideo /> },
    { id: 'audio', label: 'Audio', icon: <FaMusic /> },
    { id: 'documents', label: 'Documents', icon: <FaFileAlt /> }
  ];

  useEffect(() => {
    loadFiles();
    loadStorageInfo();
  }, []);

  const loadFiles = () => {
    const allFiles = storage.getAllFiles();
    const allMedia = Object.values(storage.getSection('media') || {});
    
    const combinedFiles = [
      ...allFiles.map(file => ({ ...file, source: 'files' })),
      ...allMedia.map(media => ({ ...media, source: 'media' }))
    ];
    
    setFiles(combinedFiles);
  };

  const loadStorageInfo = () => {
    const breakdown = storage.getStorageBreakdown();
    setStorageInfo(breakdown);
  };

  const getFileIcon = (file) => {
    const type = file.type || '';
    
    if (type.startsWith('image/')) {
      return file.url ? (
        <img src={file.url} alt={file.name} />
      ) : (
        <FaImage />
      );
    }
    
    if (type.startsWith('video/')) return <FaVideo />;
    if (type.startsWith('audio/')) return <FaMusic />;
    if (type.includes('pdf') || type.includes('document')) return <FaFileAlt />;
    
    return <FaFile />;
  };

  const getFileColor = (file) => {
    const type = file.type || '';
    
    if (type.startsWith('image/')) return '#4CAF50';
    if (type.startsWith('video/')) return '#FF9800';
    if (type.startsWith('audio/')) return '#9C27B0';
    if (type.includes('pdf')) return '#F44336';
    if (type.includes('document')) return '#2196F3';
    
    return '#607D8B';
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const filteredFiles = files.filter(file => {
    // Filter by type
    if (activeFilter !== 'all') {
      const type = file.type || '';
      switch (activeFilter) {
        case 'images':
          if (!type.startsWith('image/')) return false;
          break;
        case 'videos':
          if (!type.startsWith('video/')) return false;
          break;
        case 'audio':
          if (!type.startsWith('audio/')) return false;
          break;
        case 'documents':
          if (!type.includes('pdf') && !type.includes('document') && !type.includes('text')) return false;
          break;
      }
    }
    
    // Filter by search query
    if (searchQuery) {
      return file.name?.toLowerCase().includes(searchQuery.toLowerCase());
    }
    
    return true;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return (a.name || '').localeCompare(b.name || '');
      case 'size':
        return (b.size || 0) - (a.size || 0);
      case 'date':
      default:
        return (b.uploadedAt || b.savedAt || 0) - (a.uploadedAt || a.savedAt || 0);
    }
  });

  const handleDownload = (file) => {
    MobileHaptics.medium();
    if (file.url) {
      const link = document.createElement('a');
      link.href = file.url;
      link.download = file.name || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleDelete = (file) => {
    MobileHaptics.heavy();
    if (file.source === 'files') {
      storage.deleteFile(file.id);
    } else {
      // Delete from media storage
      const data = storage.getData();
      if (data && data.media) {
        delete data.media[file.id];
        storage.saveData(data);
      }
    }
    loadFiles();
    loadStorageInfo();
  };

  const handleView = (file) => {
    MobileHaptics.light();
    if (file.url) {
      window.open(file.url, '_blank');
    }
  };

  const getStoragePercentage = () => {
    const maxStorage = 50 * 1024 * 1024; // 50MB limit for demo
    return Math.min((storageInfo.total / maxStorage) * 100, 100);
  };

  return (
    <FileManagerContainer theme={theme}>
      <FileManagerHeader theme={theme}>
        <HeaderButton onClick={onClose}>
          <FaTimes />
        </HeaderButton>
        <HeaderTitle>File Manager</HeaderTitle>
        <HeaderActions>
          <HeaderButton onClick={() => setSortBy(sortBy === 'date' ? 'name' : 'date')}>
            <FaSort />
          </HeaderButton>
        </HeaderActions>
      </FileManagerHeader>

      <FilterTabs theme={theme}>
        {filters.map((filter) => (
          <FilterTab
            key={filter.id}
            $active={activeFilter === filter.id}
            onClick={() => setActiveFilter(filter.id)}
            theme={theme}
          >
            {filter.label}
          </FilterTab>
        ))}
      </FilterTabs>

      <SearchContainer theme={theme}>
        <SearchInput
          type="text"
          placeholder="Search files..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          theme={theme}
        />
      </SearchContainer>

      <FilesList>
        {filteredFiles.map((file) => (
          <FileItem key={file.id} theme={theme}>
            <FileIcon color={getFileColor(file)} theme={theme}>
              {getFileIcon(file)}
            </FileIcon>
            
            <FileInfo>
              <FileName theme={theme}>{file.name || 'Unknown file'}</FileName>
              <FileDetails theme={theme}>
                <span>{formatFileSize(file.size || 0)}</span>
                <span>•</span>
                <span>{formatDate(file.uploadedAt || file.savedAt || Date.now())}</span>
              </FileDetails>
            </FileInfo>
            
            <FileActions>
              <ActionButton
                color="#4CAF50"
                onClick={() => handleView(file)}
                theme={theme}
              >
                <FaEye />
              </ActionButton>
              
              <ActionButton
                color="#2196F3"
                onClick={() => handleDownload(file)}
                theme={theme}
              >
                <FaDownload />
              </ActionButton>
              
              <ActionButton
                color="#F44336"
                onClick={() => handleDelete(file)}
                theme={theme}
              >
                <FaTrash />
              </ActionButton>
            </FileActions>
          </FileItem>
        ))}
        
        {filteredFiles.length === 0 && (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px 20px',
            color: theme.colors.secondaryText 
          }}>
            {searchQuery ? 'No files found' : 'No files yet'}
          </div>
        )}
      </FilesList>

      <StorageInfo theme={theme}>
        <StorageText theme={theme}>
          Storage Used: {formatFileSize(storageInfo.total || 0)} / 50 MB
        </StorageText>
        <StorageBar theme={theme}>
          <StorageUsed $percentage={getStoragePercentage()} theme={theme} />
        </StorageBar>
        <StorageText theme={theme}>
          {getStoragePercentage().toFixed(1)}% used
        </StorageText>
      </StorageInfo>
    </FileManagerContainer>
  );
};

export default FileManager;
