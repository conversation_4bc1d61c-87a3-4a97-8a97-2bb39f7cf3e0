import React, { useEffect, useState } from 'react';
import styled, { keyframes } from 'styled-components';
import Logo from '../../assets/logo';
import { FaComments, FaUsers, FaLock, FaGlobe, FaCheck } from 'react-icons/fa';

const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;

const fadeOut = keyframes`
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
`;

const slideUp = keyframes`
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
`;

const rotate = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

const SplashContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: ${props => props.theme.mode === 'dark'
    ? 'linear-gradient(135deg, #1F2C34 0%, #121B22 100%)'
    : 'linear-gradient(135deg, #25D366 0%, #128C7E 100%)'};
  z-index: 9999;
  animation: ${props => props.$isClosing ? fadeOut : fadeIn} 0.5s ease-in-out;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    animation: ${rotate} 30s linear infinite;
    z-index: -1;
  }
`;

const LogoWrapper = styled.div`
  animation: ${pulse} 2s infinite;
  margin-bottom: 20px;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    z-index: -1;
  }
`;

const AppName = styled.h1`
  font-size: 28px;
  font-weight: bold;
  color: white;
  margin: 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  animation: ${slideUp} 0.5s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
`;

const Tagline = styled.div`
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 30px;
  text-align: center;
  animation: ${slideUp} 0.5s ease-out forwards;
  animation-delay: 0.4s;
  opacity: 0;
`;

const LoadingText = styled.div`
  margin-top: 30px;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  animation: ${slideUp} 0.5s ease-out forwards;
  animation-delay: 0.6s;
  opacity: 0;
`;

const LoadingBar = styled.div`
  width: 240px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  margin-top: 15px;
  overflow: hidden;
  position: relative;
  animation: ${slideUp} 0.5s ease-out forwards;
  animation-delay: 0.8s;
  opacity: 0;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 30%;
    background-color: white;
    border-radius: 2px;
    animation: loading 1.5s infinite ease-in-out;
  }

  @keyframes loading {
    0% {
      left: -30%;
    }
    100% {
      left: 100%;
    }
  }
`;

const FeaturesList = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 40px;
  width: 100%;
  max-width: 600px;
  animation: ${slideUp} 0.5s ease-out forwards;
  animation-delay: 1s;
  opacity: 0;
`;

const FeatureItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 15px;

  @media (max-width: 768px) {
    margin: 0 10px;
  }
`;

const FeatureIcon = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;

  svg {
    color: white;
    font-size: 24px;
  }

  @media (max-width: 768px) {
    width: 40px;
    height: 40px;

    svg {
      font-size: 20px;
    }
  }
`;

const FeatureText = styled.div`
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;

  @media (max-width: 768px) {
    font-size: 12px;
  }
`;

const Version = styled.div`
  position: absolute;
  bottom: 20px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  animation: ${slideUp} 0.5s ease-out forwards;
  animation-delay: 1.2s;
  opacity: 0;
`;

const SplashScreen = ({ onComplete, theme }) => {
  const [isClosing, setIsClosing] = useState(false);
  const [loadingMessages, setLoadingMessages] = useState([
    'Loading your conversations...',
    'Preparing your messages...',
    'Setting up secure connection...',
    'Almost there...'
  ]);
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);

  useEffect(() => {
    // Cycle through loading messages
    const messageInterval = setInterval(() => {
      setCurrentMessageIndex(prev => (prev + 1) % loadingMessages.length);
    }, 1000);

    // Start closing animation after delay
    const timer = setTimeout(() => {
      setIsClosing(true);

      const fadeOutTimer = setTimeout(() => {
        onComplete();
      }, 500); // Match this with the fadeOut animation duration

      return () => clearTimeout(fadeOutTimer);
    }, 3500);

    return () => {
      clearTimeout(timer);
      clearInterval(messageInterval);
    };
  }, [onComplete, loadingMessages]);

  return (
    <SplashContainer theme={theme} $isClosing={isClosing}>
      <LogoWrapper>
        <Logo vertical size="80px" large theme={theme} color="white" />
      </LogoWrapper>

      <AppName>GBChat</AppName>
      <Tagline>Connect with friends and family securely</Tagline>

      <FeaturesList>
        <FeatureItem>
          <FeatureIcon>
            <FaComments />
          </FeatureIcon>
          <FeatureText>Messaging</FeatureText>
        </FeatureItem>

        <FeatureItem>
          <FeatureIcon>
            <FaUsers />
          </FeatureIcon>
          <FeatureText>Groups</FeatureText>
        </FeatureItem>

        <FeatureItem>
          <FeatureIcon>
            <FaLock />
          </FeatureIcon>
          <FeatureText>Privacy</FeatureText>
        </FeatureItem>

        <FeatureItem>
          <FeatureIcon>
            <FaGlobe />
          </FeatureIcon>
          <FeatureText>Web</FeatureText>
        </FeatureItem>
      </FeaturesList>

      <LoadingText>{loadingMessages[currentMessageIndex]}</LoadingText>
      <LoadingBar />

      <Version>Version 1.0.0</Version>
    </SplashContainer>
  );
};

export default SplashScreen;
