import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  FaToggleOn, 
  FaToggleOff, 
  FaLock, 
  FaFingerprint, 
  FaEye, 
  FaEyeSlash,
  FaShieldAlt,
  FaUserSecret,
  FaKey
} from 'react-icons/fa';

const SecuritySettingsContainer = styled.div`
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const SettingsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const SettingItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const SettingInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const IconWrapper = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary}20;
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    color: ${props => props.color || props.theme.colors.primary};
    font-size: 18px;
  }
`;

const SettingText = styled.div`
  display: flex;
  flex-direction: column;
`;

const SettingName = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 0.95rem;
`;

const SettingDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 0.8rem;
  margin-top: 4px;
`;

const ToggleButton = styled.div`
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
  font-size: 1.5rem;
  cursor: pointer;
`;

const SecurityBadge = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 24px 0;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary}10;
  border-radius: 8px;
  border: 1px dashed ${props => props.theme.colors.primary};
`;

const BadgeContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: ${props => props.theme.colors.text};
  
  svg {
    font-size: 32px;
    color: ${props => props.theme.colors.primary};
  }
`;

const BadgeTitle = styled.div`
  font-weight: 600;
  font-size: 1rem;
`;

const BadgeDescription = styled.div`
  font-size: 0.85rem;
  color: ${props => props.theme.colors.secondaryText};
  text-align: center;
  max-width: 300px;
`;

const SecuritySettings = ({ theme }) => {
  const [settings, setSettings] = useState({
    screenLock: false,
    fingerprintLock: false,
    showLastSeen: true,
    twoFactorAuth: false,
    securityNotifications: true,
    encryptBackup: false
  });
  
  const toggleSetting = (setting) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };
  
  return (
    <SecuritySettingsContainer theme={theme}>
      <SectionTitle theme={theme}>Security Settings</SectionTitle>
      
      <SecurityBadge theme={theme}>
        <BadgeContent theme={theme}>
          <FaShieldAlt />
          <BadgeTitle theme={theme}>Your chats are secure</BadgeTitle>
          <BadgeDescription theme={theme}>
            Messages and calls are secured with end-to-end encryption. 
            No one outside of this chat can read or listen to them.
          </BadgeDescription>
        </BadgeContent>
      </SecurityBadge>
      
      <SettingsList>
        <SettingItem theme={theme} onClick={() => toggleSetting('screenLock')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#2196F3">
              <FaLock />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Screen lock</SettingName>
              <SettingDescription theme={theme}>
                Require passcode to open the app
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.screenLock} 
            theme={theme}
          >
            {settings.screenLock ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        {settings.screenLock && (
          <SettingItem theme={theme} onClick={() => toggleSetting('fingerprintLock')}>
            <SettingInfo>
              <IconWrapper theme={theme} color="#9C27B0">
                <FaFingerprint />
              </IconWrapper>
              <SettingText>
                <SettingName theme={theme}>Fingerprint lock</SettingName>
                <SettingDescription theme={theme}>
                  Use fingerprint to unlock the app
                </SettingDescription>
              </SettingText>
            </SettingInfo>
            <ToggleButton 
              $active={settings.fingerprintLock} 
              theme={theme}
            >
              {settings.fingerprintLock ? <FaToggleOn /> : <FaToggleOff />}
            </ToggleButton>
          </SettingItem>
        )}
        
        <SettingItem theme={theme} onClick={() => toggleSetting('showLastSeen')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#4CAF50">
              {settings.showLastSeen ? <FaEye /> : <FaEyeSlash />}
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Last seen</SettingName>
              <SettingDescription theme={theme}>
                Allow others to see when you were last online
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.showLastSeen} 
            theme={theme}
          >
            {settings.showLastSeen ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('twoFactorAuth')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#FF9800">
              <FaKey />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Two-step verification</SettingName>
              <SettingDescription theme={theme}>
                Add an extra layer of security to your account
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.twoFactorAuth} 
            theme={theme}
          >
            {settings.twoFactorAuth ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('securityNotifications')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#E91E63">
              <FaShieldAlt />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Security notifications</SettingName>
              <SettingDescription theme={theme}>
                Get notified when your security code changes
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.securityNotifications} 
            theme={theme}
          >
            {settings.securityNotifications ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('encryptBackup')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#607D8B">
              <FaUserSecret />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Encrypted backups</SettingName>
              <SettingDescription theme={theme}>
                Secure your backup with end-to-end encryption
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.encryptBackup} 
            theme={theme}
          >
            {settings.encryptBackup ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
      </SettingsList>
    </SecuritySettingsContainer>
  );
};

export default SecuritySettings;
