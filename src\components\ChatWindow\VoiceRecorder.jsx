import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { FaMicrophone, FaStop, FaTrash, FaPaperPlane } from 'react-icons/fa';

const RecorderContainer = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 10px;
`;

const RecordButton = styled.button`
  background-color: ${props => props.$isRecording 
    ? '#e74c3c' 
    : props.theme.colors.primary};
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    transform: scale(1.05);
  }
  
  ${props => props.$isRecording && `
    animation: pulse 1.5s infinite;
    
    @keyframes pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
      }
      70% {
        box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
      }
    }
  `}
`;

const RecordingInfo = styled.div`
  flex: 1;
  margin: 0 15px;
  display: flex;
  flex-direction: column;
`;

const RecordingTime = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.text};
  font-weight: 500;
`;

const RecordingStatus = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.secondaryText};
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.icon};
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 5px;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const SendButton = styled(ActionButton)`
  color: ${props => props.theme.colors.primary};
`;

const VoiceRecorder = ({ onSend, theme }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [recordingComplete, setRecordingComplete] = useState(false);
  const timerRef = useRef(null);
  
  useEffect(() => {
    if (isRecording) {
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } else {
      clearInterval(timerRef.current);
    }
    
    return () => clearInterval(timerRef.current);
  }, [isRecording]);
  
  const toggleRecording = () => {
    if (recordingComplete) {
      // Reset if recording is complete
      setRecordingTime(0);
      setRecordingComplete(false);
    } else if (isRecording) {
      // Stop recording
      setIsRecording(false);
      setRecordingComplete(true);
    } else {
      // Start recording
      setIsRecording(true);
      setRecordingTime(0);
    }
  };
  
  const cancelRecording = () => {
    setIsRecording(false);
    setRecordingTime(0);
    setRecordingComplete(false);
  };
  
  const sendVoiceMessage = () => {
    if (recordingComplete) {
      onSend({
        type: 'voice',
        duration: recordingTime
      });
      cancelRecording();
    }
  };
  
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };
  
  return (
    <RecorderContainer>
      <RecordButton 
        onClick={toggleRecording} 
        $isRecording={isRecording}
        theme={theme}
      >
        {isRecording ? <FaStop /> : <FaMicrophone />}
      </RecordButton>
      
      <RecordingInfo>
        <RecordingTime theme={theme}>
          {formatTime(recordingTime)}
        </RecordingTime>
        <RecordingStatus theme={theme}>
          {isRecording 
            ? 'Recording...' 
            : recordingComplete 
              ? 'Recording complete' 
              : 'Tap to record'}
        </RecordingStatus>
      </RecordingInfo>
      
      {(isRecording || recordingComplete) && (
        <ActionButton onClick={cancelRecording} theme={theme}>
          <FaTrash />
        </ActionButton>
      )}
      
      {recordingComplete && (
        <SendButton onClick={sendVoiceMessage} theme={theme}>
          <FaPaperPlane />
        </SendButton>
      )}
    </RecorderContainer>
  );
};

export default VoiceRecorder;
