import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';
import Message from './Message';
import { useTheme } from '../../context/ThemeContext';

const MessageListContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  background-color: ${props => props.theme.colors.chatBackground};

  /* Responsive padding for different screen sizes */
  @media (min-width: 1200px) {
    padding: 24px;
  }

  @media (min-width: 1600px) {
    padding: 30px;
  }

  @media (min-width: 2000px) {
    padding: 40px;
  }

  @media (max-width: 768px) {
    padding: 16px;
  }
`;

const DateSeparator = styled.div`
  text-align: center;
  margin: 10px 0;
  position: relative;
  z-index: 1;
`;

const DateBadge = styled.span`
  background-color: ${props => props.theme.colors.secondary};
  color: ${props => props.theme.colors.secondaryText};
  font-size: 12px;
  padding: 5px 12px;
  border-radius: 8px;
  display: inline-block;
  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
`;

const MessageList = ({ messages, isGroupChat, searchQuery }) => {
  const messagesEndRef = useRef(null);
  const { currentTheme } = useTheme();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Filter messages by search query if provided
  const filteredMessages = searchQuery
    ? messages.filter(message =>
        message.text.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : messages;

  // Group messages by date
  const groupedMessages = filteredMessages.reduce((groups, message) => {
    const date = message.time.includes('Yesterday')
      ? 'Yesterday'
      : message.time.includes(':')
        ? 'Today'
        : message.time.split(',')[0];

    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(message);
    return groups;
  }, {});

  return (
    <MessageListContainer theme={currentTheme}>
      {Object.entries(groupedMessages).map(([date, dateMessages]) => (
        <React.Fragment key={date}>
          <DateSeparator>
            <DateBadge theme={currentTheme}>{date}</DateBadge>
          </DateSeparator>
          {dateMessages.map((message) => (
            <Message
              key={message.id}
              message={message}
              isGroupChat={isGroupChat}
              searchQuery={searchQuery}
            />
          ))}
        </React.Fragment>
      ))}
      <div ref={messagesEndRef} />
    </MessageListContainer>
  );
};

export default MessageList;
