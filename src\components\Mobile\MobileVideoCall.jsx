import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  FaPhoneSlash,
  FaVideo,
  FaVideoSlash,
  FaMicrophone,
  FaMicrophoneSlash,
  FaVolumeUp,
  FaExpand,
  FaCompress,
  FaEllipsisV,
  FaCamera,
  FaSyncAlt
} from 'react-icons/fa';
import MobileHaptics from './MobileHaptics';

const VideoCallContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  color: white;
`;

const VideoArea = styled.div`
  flex: 1;
  position: relative;
  overflow: hidden;
`;

const RemoteVideo = styled.div`
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
`;

const LocalVideo = styled.div`
  position: absolute;
  top: 20px;
  right: 20px;
  width: 120px;
  height: 160px;
  background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
  border-radius: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
`;

const ContactInfo = styled.div`
  position: absolute;
  top: 60px;
  left: 20px;
  right: 20px;
  text-align: center;
`;

const ContactName = styled.h2`
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`;

const CallStatus = styled.div`
  font-size: 16px;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
`;

const CallDuration = styled.div`
  font-size: 14px;
  opacity: 0.8;
  margin-top: 4px;
`;

const ControlsContainer = styled.div`
  padding: 30px 20px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
`;

const ControlsRow = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
`;

const ControlButton = styled.button`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  ${props => props.$variant === 'end' && `
    background: #ff4757;
    color: white;

    &:hover {
      background: #ff3742;
      transform: scale(1.1);
    }
  `}

  ${props => props.$variant === 'toggle' && `
    background: ${props.$active ? '#ff4757' : 'rgba(255, 255, 255, 0.2)'};
    color: white;

    &:hover {
      background: ${props.$active ? '#ff3742' : 'rgba(255, 255, 255, 0.3)'};
    }
  `}

  ${props => props.$variant === 'action' && `
    background: rgba(255, 255, 255, 0.2);
    color: white;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  `}

  &:active {
    transform: scale(0.95);
  }
`;

const SmallControlButton = styled.button`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }
`;

const Avatar = styled.div`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  font-weight: 600;
  margin: 0 auto 20px;
`;

const MobileVideoCall = ({
  contact,
  isIncoming = false,
  onEndCall,
  onAccept,
  onDecline
}) => {
  const [callDuration, setCallDuration] = useState(0);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isMicEnabled, setIsMicEnabled] = useState(true);
  const [isSpeakerOn, setIsSpeakerOn] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [callStatus, setCallStatus] = useState(isIncoming ? 'Incoming call...' : 'Calling...');

  useEffect(() => {
    let interval;
    if (!isIncoming) {
      interval = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isIncoming]);

  useEffect(() => {
    if (!isIncoming) {
      setTimeout(() => setCallStatus('Connected'), 2000);
    }
  }, [isIncoming]);

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleEndCall = () => {
    MobileHaptics.heavy();
    if (onEndCall) onEndCall();
  };

  const handleAccept = () => {
    MobileHaptics.success();
    if (onAccept) onAccept();
  };

  const handleDecline = () => {
    MobileHaptics.heavy();
    if (onDecline) onDecline();
  };

  const toggleVideo = () => {
    MobileHaptics.medium();
    setIsVideoEnabled(!isVideoEnabled);
  };

  const toggleMic = () => {
    MobileHaptics.medium();
    setIsMicEnabled(!isMicEnabled);
  };

  const toggleSpeaker = () => {
    MobileHaptics.light();
    setIsSpeakerOn(!isSpeakerOn);
  };

  const toggleFullscreen = () => {
    MobileHaptics.light();
    setIsFullscreen(!isFullscreen);
  };

  const getInitials = (name) => {
    return name?.split(' ').map(n => n[0]).join('').toUpperCase() || '';
  };

  return (
    <VideoCallContainer>
      <VideoArea>
        <RemoteVideo>
          {isVideoEnabled ? (
            <>
              <ContactInfo>
                <ContactName>{contact?.name || 'Unknown'}</ContactName>
                <CallStatus>{callStatus}</CallStatus>
                {callDuration > 0 && (
                  <CallDuration>{formatDuration(callDuration)}</CallDuration>
                )}
              </ContactInfo>

              <LocalVideo onClick={toggleFullscreen}>
                <FaCamera size={24} />
              </LocalVideo>
            </>
          ) : (
            <>
              <Avatar>
                {contact?.avatar ? (
                  <img src={contact.avatar} alt={contact.name} style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: '50%',
                    objectFit: 'cover'
                  }} />
                ) : (
                  getInitials(contact?.name)
                )}
              </Avatar>

              <ContactInfo>
                <ContactName>{contact?.name || 'Unknown'}</ContactName>
                <CallStatus>{callStatus}</CallStatus>
                {callDuration > 0 && (
                  <CallDuration>{formatDuration(callDuration)}</CallDuration>
                )}
              </ContactInfo>
            </>
          )}
        </RemoteVideo>
      </VideoArea>

      <ControlsContainer>
        {!isIncoming && (
          <ControlsRow>
            <SmallControlButton onClick={toggleSpeaker}>
              <FaVolumeUp />
            </SmallControlButton>

            <SmallControlButton>
              <FaSyncAlt />
            </SmallControlButton>

            <SmallControlButton>
              <FaEllipsisV />
            </SmallControlButton>
          </ControlsRow>
        )}

        <ControlsRow>
          {isIncoming ? (
            <>
              <ControlButton $variant="end" onClick={handleDecline}>
                <FaPhoneSlash />
              </ControlButton>

              <ControlButton $variant="toggle" $active={false} onClick={handleAccept}>
                <FaVideo />
              </ControlButton>
            </>
          ) : (
            <>
              <ControlButton
                $variant="toggle"
                $active={!isVideoEnabled}
                onClick={toggleVideo}
              >
                {isVideoEnabled ? <FaVideo /> : <FaVideoSlash />}
              </ControlButton>

              <ControlButton $variant="end" onClick={handleEndCall}>
                <FaPhoneSlash />
              </ControlButton>

              <ControlButton
                $variant="toggle"
                $active={!isMicEnabled}
                onClick={toggleMic}
              >
                {isMicEnabled ? <FaMicrophone /> : <FaMicrophoneSlash />}
              </ControlButton>
            </>
          )}
        </ControlsRow>
      </ControlsContainer>
    </VideoCallContainer>
  );
};

export default MobileVideoCall;
