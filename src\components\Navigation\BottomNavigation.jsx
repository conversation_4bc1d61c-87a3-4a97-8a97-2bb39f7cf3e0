import React from 'react';
import styled from 'styled-components';
import { FaComments, FaUsers, FaPhone, FaCog, FaCamera } from 'react-icons/fa';

const BottomNavContainer = styled.div`
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: ${props => props.theme.colors.background};
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  
  @media (max-width: 768px) {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
`;

const NavItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
  
  ${props => props.$active && `
    color: ${props.theme.colors.primary};
    border-top: 2px solid ${props.theme.colors.primary};
  `}
`;

const NavIcon = styled.div`
  font-size: 20px;
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.icon};
  margin-bottom: 4px;
`;

const NavLabel = styled.div`
  font-size: 10px;
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
`;

const BottomNavigation = ({ activeTab, onTabChange, theme }) => {
  const tabs = [
    { id: 'chats', icon: <FaComments />, label: 'Chats' },
    { id: 'status', icon: <FaCamera />, label: 'Status' },
    { id: 'calls', icon: <FaPhone />, label: 'Calls' },
    { id: 'groups', icon: <FaUsers />, label: 'Groups' },
    { id: 'settings', icon: <FaCog />, label: 'Settings' }
  ];
  
  return (
    <BottomNavContainer theme={theme}>
      {tabs.map(tab => (
        <NavItem 
          key={tab.id}
          $active={activeTab === tab.id}
          onClick={() => onTabChange(tab.id)}
          theme={theme}
        >
          <NavIcon $active={activeTab === tab.id} theme={theme}>
            {tab.icon}
          </NavIcon>
          <NavLabel $active={activeTab === tab.id} theme={theme}>
            {tab.label}
          </NavLabel>
        </NavItem>
      ))}
    </BottomNavContainer>
  );
};

export default BottomNavigation;
