export const mockChats = [
  {
    id: 1,
    name: '<PERSON>',
    lastMessage: 'Hey, how are you doing?',
    lastMessageTime: '10:30 AM',
    unreadCount: 2,
    lastSeen: '11:45 AM',
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    isOnline: true,
    about: 'Living life one day at a time',
    phone: '+****************',
    isGroup: false
  },
  {
    id: 2,
    name: '<PERSON>',
    lastMessage: 'Can we meet tomorrow?',
    lastMessageTime: '9:15 AM',
    unreadCount: 0,
    lastSeen: '10:20 AM',
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    isOnline: false,
    about: 'Professional photographer and traveler',
    phone: '+****************',
    isGroup: false
  },
  {
    id: 3,
    name: 'Work Group',
    lastMessage: 'Meeting at 3 PM today',
    lastMessageTime: 'Yesterday',
    unreadCount: 5,
    lastSeen: 'Yesterday',
    avatar: 'https://img.freepik.com/free-vector/business-team-discussing-ideas-startup_74855-4380.jpg',
    isOnline: true,
    about: 'Official work group for team communications',
    members: ['<PERSON>', '<PERSON> <PERSON>', '<PERSON>', '<PERSON>', 'You'],
    isGroup: true
  },
  {
    id: 4,
    name: 'Family Group',
    lastMessage: 'Mom: Dinner at 7 PM',
    lastMessageTime: 'Yesterday',
    unreadCount: 0,
    lastSeen: 'Yesterday',
    avatar: 'https://img.freepik.com/free-vector/big-family-meeting_74855-5220.jpg',
    isOnline: true,
    about: 'Family matters and updates',
    members: ['Mom', 'Dad', 'Sister', 'Brother', 'You'],
    isGroup: true
  },
  {
    id: 5,
    name: 'Alice Johnson',
    lastMessage: 'Thanks for the help!',
    lastMessageTime: '7/20/2023',
    unreadCount: 0,
    lastSeen: '7/20/2023',
    avatar: 'https://randomuser.me/api/portraits/women/63.jpg',
    isOnline: false,
    about: 'Software developer and coffee enthusiast',
    phone: '+****************',
    isGroup: false
  },
  {
    id: 6,
    name: 'Mike Johnson',
    lastMessage: 'Did you see the game last night?',
    lastMessageTime: '7/19/2023',
    unreadCount: 1,
    lastSeen: '7/19/2023',
    avatar: 'https://randomuser.me/api/portraits/men/67.jpg',
    isOnline: true,
    about: 'Sports enthusiast and fitness trainer',
    phone: '+****************',
    isGroup: false
  },
  {
    id: 7,
    name: 'Sarah Williams',
    lastMessage: 'Let me know when you arrive',
    lastMessageTime: '7/18/2023',
    unreadCount: 0,
    lastSeen: '7/18/2023',
    avatar: 'https://randomuser.me/api/portraits/women/90.jpg',
    isOnline: false,
    about: 'Artist and nature lover',
    phone: '+****************',
    isGroup: false
  },
  {
    id: 8,
    name: 'Tech Support Group',
    lastMessage: 'Check the latest update',
    lastMessageTime: '7/17/2023',
    unreadCount: 3,
    lastSeen: '7/17/2023',
    avatar: 'https://img.freepik.com/free-vector/technical-support-concept-illustration_114360-2482.jpg',
    isOnline: true,
    about: 'Technical support and troubleshooting',
    members: ['Alice Johnson', 'Mike Johnson', 'Tech Admin', 'You'],
    isGroup: true
  },
  {
    id: 9,
    name: 'David Wilson',
    lastMessage: 'Thanks for the recommendation',
    lastMessageTime: '7/16/2023',
    unreadCount: 0,
    lastSeen: '7/16/2023',
    avatar: 'https://randomuser.me/api/portraits/men/22.jpg',
    isOnline: false,
    about: 'Music producer and DJ',
    phone: '+****************',
    isGroup: false
  },
  {
    id: 10,
    name: 'Emma Thompson',
    lastMessage: 'See you at the conference',
    lastMessageTime: '7/15/2023',
    unreadCount: 0,
    lastSeen: '7/15/2023',
    avatar: 'https://randomuser.me/api/portraits/women/22.jpg',
    isOnline: true,
    about: 'Marketing specialist and public speaker',
    phone: '+****************',
    isGroup: false
  }
];

export const mockMessages = {
  1: [
    {
      id: 1,
      text: 'Hey there!',
      time: '10:00 AM',
      isSent: false,
    },
    {
      id: 2,
      text: 'Hi! How are you?',
      time: '10:05 AM',
      isSent: true,
    },
    {
      id: 3,
      text: 'I\'m good, thanks for asking. How about you?',
      time: '10:10 AM',
      isSent: false,
    },
    {
      id: 4,
      text: 'Doing well! Just working on some projects.',
      time: '10:15 AM',
      isSent: true,
    },
    {
      id: 5,
      text: 'That sounds interesting. What kind of projects?',
      time: '10:20 AM',
      isSent: false,
    },
    {
      id: 6,
      text: 'Building a WhatsApp clone with React!',
      time: '10:25 AM',
      isSent: true,
    },
    {
      id: 7,
      text: 'Hey, how are you doing?',
      time: '10:30 AM',
      isSent: false,
    },
  ],
  2: [
    {
      id: 1,
      text: 'Hello Jane!',
      time: '9:00 AM',
      isSent: true,
    },
    {
      id: 2,
      text: 'Hi there! Do you have time to meet tomorrow?',
      time: '9:05 AM',
      isSent: false,
    },
    {
      id: 3,
      text: 'Sure, what time works for you?',
      time: '9:10 AM',
      isSent: true,
    },
    {
      id: 4,
      text: 'Can we meet tomorrow?',
      time: '9:15 AM',
      isSent: false,
    },
  ],
  3: [
    {
      id: 1,
      text: 'Good morning team!',
      time: 'Yesterday, 8:00 AM',
      isSent: false,
      sender: 'Manager',
      senderAvatar: 'https://randomuser.me/api/portraits/men/42.jpg',
    },
    {
      id: 2,
      text: 'Morning!',
      time: 'Yesterday, 8:05 AM',
      isSent: true,
    },
    {
      id: 3,
      text: 'We need to discuss the new project today.',
      time: 'Yesterday, 8:10 AM',
      isSent: false,
      sender: 'Manager',
      senderAvatar: 'https://randomuser.me/api/portraits/men/42.jpg',
    },
    {
      id: 4,
      text: 'Meeting at 3 PM today',
      time: 'Yesterday, 8:15 AM',
      isSent: false,
      sender: 'Manager',
      senderAvatar: 'https://randomuser.me/api/portraits/men/42.jpg',
    },
  ],
  4: [
    {
      id: 1,
      text: 'Hi everyone!',
      time: 'Yesterday, 6:00 PM',
      isSent: false,
      sender: 'Dad',
      senderAvatar: 'https://randomuser.me/api/portraits/men/76.jpg',
    },
    {
      id: 2,
      text: 'Hello!',
      time: 'Yesterday, 6:05 PM',
      isSent: true,
    },
    {
      id: 3,
      text: 'Dinner at 7 PM',
      time: 'Yesterday, 6:10 PM',
      isSent: false,
      sender: 'Mom',
      senderAvatar: 'https://randomuser.me/api/portraits/women/76.jpg',
    },
  ],
  5: [
    {
      id: 1,
      text: 'Hi Alice, can you help me with something?',
      time: '7/20/2023, 2:00 PM',
      isSent: true,
    },
    {
      id: 2,
      text: 'Sure, what do you need?',
      time: '7/20/2023, 2:05 PM',
      isSent: false,
    },
    {
      id: 3,
      text: 'I need help with my React project',
      time: '7/20/2023, 2:10 PM',
      isSent: true,
    },
    {
      id: 4,
      text: 'No problem, I can help you with that',
      time: '7/20/2023, 2:15 PM',
      isSent: false,
    },
    {
      id: 5,
      text: 'Thanks for the help!',
      time: '7/20/2023, 2:20 PM',
      isSent: false,
    },
  ],
  6: [
    {
      id: 1,
      text: 'Hey Mike, did you see the game last night?',
      time: '7/19/2023, 8:00 PM',
      isSent: true,
    },
    {
      id: 2,
      text: 'Yeah, it was amazing! That last-minute goal was incredible.',
      time: '7/19/2023, 8:05 PM',
      isSent: false,
    },
    {
      id: 3,
      text: 'I know right? I couldn\'t believe it!',
      time: '7/19/2023, 8:10 PM',
      isSent: true,
    },
    {
      id: 4,
      text: 'Did you see the game last night?',
      time: '7/19/2023, 8:15 PM',
      isSent: false,
    },
  ],
  7: [
    {
      id: 1,
      text: 'Hi Sarah, I\'m on my way to the gallery',
      time: '7/18/2023, 3:00 PM',
      isSent: true,
    },
    {
      id: 2,
      text: 'Great! I\'ll be waiting at the entrance',
      time: '7/18/2023, 3:05 PM',
      isSent: false,
    },
    {
      id: 3,
      text: 'I\'m about 10 minutes away',
      time: '7/18/2023, 3:10 PM',
      isSent: true,
    },
    {
      id: 4,
      text: 'Let me know when you arrive',
      time: '7/18/2023, 3:15 PM',
      isSent: false,
    },
  ],
  8: [
    {
      id: 1,
      text: 'Hello everyone, we have a new update available',
      time: '7/17/2023, 10:00 AM',
      isSent: false,
      sender: 'Tech Admin',
      senderAvatar: 'https://randomuser.me/api/portraits/men/91.jpg',
    },
    {
      id: 2,
      text: 'What\'s new in this update?',
      time: '7/17/2023, 10:05 AM',
      isSent: true,
    },
    {
      id: 3,
      text: 'It includes bug fixes and performance improvements',
      time: '7/17/2023, 10:10 AM',
      isSent: false,
      sender: 'Tech Admin',
      senderAvatar: 'https://randomuser.me/api/portraits/men/91.jpg',
    },
    {
      id: 4,
      text: 'I\'m having trouble installing it',
      time: '7/17/2023, 10:15 AM',
      isSent: false,
      sender: 'Alice Johnson',
      senderAvatar: 'https://randomuser.me/api/portraits/women/63.jpg',
    },
    {
      id: 5,
      text: 'Check the latest update',
      time: '7/17/2023, 10:20 AM',
      isSent: false,
      sender: 'Tech Admin',
      senderAvatar: 'https://randomuser.me/api/portraits/men/91.jpg',
    },
  ],
  9: [
    {
      id: 1,
      text: 'Hey David, I listened to that playlist you recommended',
      time: '7/16/2023, 7:00 PM',
      isSent: true,
    },
    {
      id: 2,
      text: 'What did you think of it?',
      time: '7/16/2023, 7:05 PM',
      isSent: false,
    },
    {
      id: 3,
      text: 'It was fantastic! Especially the third track',
      time: '7/16/2023, 7:10 PM',
      isSent: true,
    },
    {
      id: 4,
      text: 'Thanks for the recommendation',
      time: '7/16/2023, 7:15 PM',
      isSent: false,
    },
  ],
  10: [
    {
      id: 1,
      text: 'Emma, are you attending the marketing conference next week?',
      time: '7/15/2023, 11:00 AM',
      isSent: true,
    },
    {
      id: 2,
      text: 'Yes, I\'ll be there! I\'m actually one of the speakers',
      time: '7/15/2023, 11:05 AM',
      isSent: false,
    },
    {
      id: 3,
      text: 'That\'s awesome! What topic are you presenting?',
      time: '7/15/2023, 11:10 AM',
      isSent: true,
    },
    {
      id: 4,
      text: 'Digital marketing trends for 2023. See you at the conference!',
      time: '7/15/2023, 11:15 AM',
      isSent: false,
    },
  ],
};
