import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  FaDatabase,
  FaDownload,
  FaUpload,
  FaTrash,
  FaChartBar,
  FaFile,
  FaImage,
  FaVideo,
  FaMusic,
  FaUsers,
  FaComment,
  FaPhone,
  FaExclamationTriangle,
  FaCheckCircle
} from 'react-icons/fa';
import storage from '../../utils/storage';

const StorageContainer = styled.div`
  padding: 16px;
`;

const StorageSection = styled.div`
  margin-bottom: 24px;
  padding: 16px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 12px;
`;

const SectionTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const StorageItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid ${props => props.theme.colors.border};

  &:last-child {
    border-bottom: none;
  }
`;

const ItemInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const ItemIcon = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
`;

const ItemDetails = styled.div``;

const ItemName = styled.div`
  font-size: 14px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
`;

const ItemSize = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.secondaryText};
`;

const ItemAction = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: ${props => props.theme.colors.primary};
`;

const ActionButton = styled.button`
  padding: 8px 16px;
  border: 1px solid ${props => props.theme.colors.primary};
  border-radius: 6px;
  background: ${props => props.$primary ? props.theme.colors.primary : 'transparent'};
  color: ${props => props.$primary ? 'white' : props.theme.colors.primary};
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 4px;

  &:hover {
    background: ${props => props.$primary ? props.theme.colors.primaryDark : props.theme.colors.primary};
    color: white;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
`;

const StorageBar = styled.div`
  width: 100%;
  height: 8px;
  background-color: ${props => props.theme.colors.border};
  border-radius: 4px;
  overflow: hidden;
  margin: 12px 0;
`;

const StorageUsed = styled.div`
  height: 100%;
  background-color: ${props => {
    if (props.$percentage > 80) return '#f44336';
    if (props.$percentage > 60) return '#ff9800';
    return props.theme.colors.primary;
  }};
  width: ${props => props.$percentage}%;
  transition: width 0.3s ease;
`;

const StorageText = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.secondaryText};
  text-align: center;
`;

const DataStorageSettings = ({ theme }) => {
  const [storageInfo, setStorageInfo] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadStorageInfo();
  }, []);

  const loadStorageInfo = () => {
    const breakdown = storage.getStorageBreakdown();
    setStorageInfo(breakdown);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStoragePercentage = () => {
    const maxStorage = 50 * 1024 * 1024; // 50MB limit
    return Math.min((storageInfo.total / maxStorage) * 100, 100);
  };

  const handleExportData = () => {
    setIsLoading(true);
    const backup = storage.createBackup();
    if (backup) {
      const blob = new Blob([backup], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `gbchat-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
    setIsLoading(false);
  };

  const handleImportData = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        setIsLoading(true);
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const success = storage.restoreBackup(e.target.result);
            if (success) {
              alert('Data imported successfully!');
              loadStorageInfo();
            } else {
              alert('Failed to import data. Please check the file format.');
            }
          } catch (error) {
            alert('Invalid backup file.');
          }
          setIsLoading(false);
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const handleClearData = (type) => {
    if (confirm(`Are you sure you want to clear all ${type}? This action cannot be undone.`)) {
      setIsLoading(true);
      switch (type) {
        case 'messages':
          storage.updateSection('messages', {});
          break;
        case 'media':
          storage.updateSection('media', {});
          break;
        case 'files':
          storage.updateSection('files', {});
          break;
        case 'all':
          storage.clearAll();
          break;
      }
      loadStorageInfo();
      setIsLoading(false);
      alert(`${type} cleared successfully!`);
    }
  };

  const storageItems = [
    { name: 'Messages', size: storageInfo.messages || 0, icon: <FaComment />, color: '#25d366', type: 'messages' },
    { name: 'Media', size: storageInfo.media || 0, icon: <FaImage />, color: '#4caf50', type: 'media' },
    { name: 'Files', size: storageInfo.files || 0, icon: <FaFile />, color: '#2196f3', type: 'files' },
    { name: 'Contacts', size: storageInfo.contacts || 0, icon: <FaUsers />, color: '#ff9800', type: 'contacts' },
    { name: 'Settings', size: storageInfo.settings || 0, icon: <FaDatabase />, color: '#9c27b0', type: 'settings' }
  ];

  return (
    <StorageContainer theme={theme}>
      <StorageSection theme={theme}>
        <SectionTitle theme={theme}>
          <FaChartBar /> Storage Overview
        </SectionTitle>

        <StorageText theme={theme}>
          Used: {formatFileSize(storageInfo.total || 0)} / 50 MB
        </StorageText>
        <StorageBar theme={theme}>
          <StorageUsed $percentage={getStoragePercentage()} theme={theme} />
        </StorageBar>
        <StorageText theme={theme}>
          {getStoragePercentage().toFixed(1)}% used
        </StorageText>
      </StorageSection>

      <StorageSection theme={theme}>
        <SectionTitle theme={theme}>
          <FaDatabase /> Storage Breakdown
        </SectionTitle>

        {storageItems.map((item, index) => (
          <StorageItem key={index} theme={theme}>
            <ItemInfo>
              <ItemIcon color={item.color} theme={theme}>
                {item.icon}
              </ItemIcon>
              <ItemDetails>
                <ItemName theme={theme}>{item.name}</ItemName>
                <ItemSize theme={theme}>{formatFileSize(item.size)}</ItemSize>
              </ItemDetails>
            </ItemInfo>
            <ItemAction theme={theme}>
              {item.size > 0 && (
                <ActionButton
                  onClick={() => handleClearData(item.type)}
                  theme={theme}
                  disabled={isLoading}
                >
                  <FaTrash /> Clear
                </ActionButton>
              )}
            </ItemAction>
          </StorageItem>
        ))}
      </StorageSection>

      <StorageSection theme={theme}>
        <SectionTitle theme={theme}>
          <FaDownload /> Backup & Restore
        </SectionTitle>

        <ActionButtons>
          <ActionButton
            $primary
            onClick={handleExportData}
            theme={theme}
            disabled={isLoading}
          >
            <FaDownload /> Export Data
          </ActionButton>

          <ActionButton
            onClick={handleImportData}
            theme={theme}
            disabled={isLoading}
          >
            <FaUpload /> Import Data
          </ActionButton>

          <ActionButton
            onClick={() => handleClearData('all')}
            theme={theme}
            disabled={isLoading}
            style={{ borderColor: '#f44336', color: '#f44336' }}
          >
            <FaTrash /> Clear All Data
          </ActionButton>
        </ActionButtons>
      </StorageSection>

      {getStoragePercentage() > 80 && (
        <StorageSection theme={theme} style={{ backgroundColor: '#fff3cd', border: '1px solid #ffeaa7' }}>
          <SectionTitle theme={theme} style={{ color: '#856404' }}>
            <FaExclamationTriangle /> Storage Warning
          </SectionTitle>
          <div style={{ color: '#856404', fontSize: '14px' }}>
            Your storage is almost full. Consider clearing some data or exporting your messages.
          </div>
        </StorageSection>
      )}
    </StorageContainer>
  );
};

export default DataStorageSettings;
