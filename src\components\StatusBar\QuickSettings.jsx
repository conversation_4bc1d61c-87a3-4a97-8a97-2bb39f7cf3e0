import React, { useState } from 'react';
import styled from 'styled-components';
import {
  FaPlane,
  FaMoon,
  FaGhost,
  FaBell,
  FaBellSlash,
  FaWifi,
  FaBan,
  FaLock,
  FaLockOpen,
  FaEye,
  FaEyeSlash,
  FaTimes,
  FaCheck
} from 'react-icons/fa';

const QuickSettingsContainer = styled.div`
  position: fixed;
  top: ${props => props.$show ? '70px' : '-400px'};
  right: 20px;
  width: 300px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  transition: top 0.3s ease-in-out;
  overflow: hidden;

  @media (max-width: 768px) {
    width: calc(100% - 40px);
    right: 20px;
  }
`;

const QuickSettingsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const HeaderTitle = styled.h3`
  margin: 0;
  color: ${props => props.theme.colors.text};
  font-size: 16px;
  font-weight: 600;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.secondaryText};
  cursor: pointer;
  font-size: 16px;

  &:hover {
    color: ${props => props.theme.colors.text};
  }
`;

const QuickSettingsContent = styled.div`
  padding: 10px;
`;

const SettingsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
`;

const SettingItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const IconWrapper = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.$active
    ? `${props.theme.colors.primary}`
    : `${props.theme.colors.secondaryText}20`};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  transition: background-color 0.2s;

  svg {
    color: ${props => props.$active
      ? 'white'
      : props.theme.colors.secondaryText};
    font-size: 18px;
  }
`;

const SettingLabel = styled.span`
  font-size: 12px;
  color: ${props => props.theme.colors.text};
  text-align: center;
`;

const SettingStatus = styled.span`
  font-size: 10px;
  color: ${props => props.$active
    ? props.theme.colors.primary
    : props.theme.colors.secondaryText};
  margin-top: 4px;
`;

const QuickSettings = ({ show, onClose, theme }) => {
  const [settings, setSettings] = useState({
    airplaneMode: false,
    dndMode: false,
    ghostMode: false,
    wifiEnabled: true,
    privateMode: false,
    readReceipts: true
  });

  const toggleSetting = (setting) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  return (
    <QuickSettingsContainer $show={show} theme={theme}>
      <QuickSettingsHeader theme={theme}>
        <HeaderTitle theme={theme}>Quick Settings</HeaderTitle>
        <CloseButton onClick={onClose} theme={theme}>
          <FaTimes />
        </CloseButton>
      </QuickSettingsHeader>

      <QuickSettingsContent>
        <SettingsGrid>
          <SettingItem
            onClick={() => toggleSetting('airplaneMode')}
            theme={theme}
          >
            <IconWrapper
              $active={settings.airplaneMode}
              theme={theme}
            >
              <FaPlane />
            </IconWrapper>
            <SettingLabel theme={theme}>Airplane Mode</SettingLabel>
            <SettingStatus
              $active={settings.airplaneMode}
              theme={theme}
            >
              {settings.airplaneMode ? 'On' : 'Off'}
            </SettingStatus>
          </SettingItem>

          <SettingItem
            onClick={() => toggleSetting('dndMode')}
            theme={theme}
          >
            <IconWrapper
              $active={settings.dndMode}
              theme={theme}
            >
              {settings.dndMode ? <FaBellSlash /> : <FaBell />}
            </IconWrapper>
            <SettingLabel theme={theme}>Do Not Disturb</SettingLabel>
            <SettingStatus
              $active={settings.dndMode}
              theme={theme}
            >
              {settings.dndMode ? 'On' : 'Off'}
            </SettingStatus>
          </SettingItem>

          <SettingItem
            onClick={() => toggleSetting('ghostMode')}
            theme={theme}
          >
            <IconWrapper
              $active={settings.ghostMode}
              theme={theme}
            >
              <FaGhost />
            </IconWrapper>
            <SettingLabel theme={theme}>Ghost Mode</SettingLabel>
            <SettingStatus
              $active={settings.ghostMode}
              theme={theme}
            >
              {settings.ghostMode ? 'On' : 'Off'}
            </SettingStatus>
          </SettingItem>

          <SettingItem
            onClick={() => toggleSetting('wifiEnabled')}
            theme={theme}
          >
            <IconWrapper
              $active={settings.wifiEnabled}
              theme={theme}
            >
              {settings.wifiEnabled ? <FaWifi /> : <FaBan />}
            </IconWrapper>
            <SettingLabel theme={theme}>Wi-Fi</SettingLabel>
            <SettingStatus
              $active={settings.wifiEnabled}
              theme={theme}
            >
              {settings.wifiEnabled ? 'Connected' : 'Disabled'}
            </SettingStatus>
          </SettingItem>

          <SettingItem
            onClick={() => toggleSetting('privateMode')}
            theme={theme}
          >
            <IconWrapper
              $active={settings.privateMode}
              theme={theme}
            >
              {settings.privateMode ? <FaLock /> : <FaLockOpen />}
            </IconWrapper>
            <SettingLabel theme={theme}>Private Mode</SettingLabel>
            <SettingStatus
              $active={settings.privateMode}
              theme={theme}
            >
              {settings.privateMode ? 'On' : 'Off'}
            </SettingStatus>
          </SettingItem>

          <SettingItem
            onClick={() => toggleSetting('readReceipts')}
            theme={theme}
          >
            <IconWrapper
              $active={settings.readReceipts}
              theme={theme}
            >
              {settings.readReceipts ? <FaCheck /> : <FaEyeSlash />}
            </IconWrapper>
            <SettingLabel theme={theme}>Read Receipts</SettingLabel>
            <SettingStatus
              $active={settings.readReceipts}
              theme={theme}
            >
              {settings.readReceipts ? 'On' : 'Off'}
            </SettingStatus>
          </SettingItem>
        </SettingsGrid>
      </QuickSettingsContent>
    </QuickSettingsContainer>
  );
};

export default QuickSettings;
