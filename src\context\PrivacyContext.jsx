import React, { createContext, useState, useContext, useEffect } from 'react';

const defaultPrivacySettings = {
  hideOnlineStatus: false,
  hideReadReceipts: false,
  hideTypingIndicator: false,
  showDeletedMessages: true,
  lastSeenPrivacy: 'everyone', // 'everyone', 'contacts', 'nobody'
  profilePhotoPrivacy: 'everyone', // 'everyone', 'contacts', 'nobody'
  statusPrivacy: 'everyone', // 'everyone', 'contacts', 'nobody'
};

const PrivacyContext = createContext();

export const usePrivacy = () => useContext(PrivacyContext);

export const PrivacyProvider = ({ children }) => {
  const [privacySettings, setPrivacySettings] = useState(() => {
    const savedSettings = localStorage.getItem('whatsapp-privacy-settings');
    return savedSettings ? JSON.parse(savedSettings) : defaultPrivacySettings;
  });

  useEffect(() => {
    localStorage.setItem('whatsapp-privacy-settings', JSON.stringify(privacySettings));
  }, [privacySettings]);

  const updatePrivacySetting = (setting, value) => {
    setPrivacySettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  return (
    <PrivacyContext.Provider value={{ privacySettings, updatePrivacySetting }}>
      {children}
    </PrivacyContext.Provider>
  );
};
