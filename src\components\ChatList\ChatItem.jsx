import React from 'react';
import styled from 'styled-components';
import { useTheme } from '../../context/ThemeContext';
import { FaCheckDouble } from 'react-icons/fa';

const ChatItemContainer = styled.div`
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  cursor: pointer;
  background-color: ${props => props.$isActive ? props.theme.colors.secondary : 'transparent'};

  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const Avatar = styled.div`
  width: 49px;
  height: 49px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  margin-right: 15px;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 20px;
  position: relative;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const OnlineIndicator = styled.div`
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #4CAF50;
  border: 2px solid ${props => props.theme.colors.background};
  z-index: 1;
`;

const ChatInfo = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
`;

const ChatHeader = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 3px;
`;

const ChatName = styled.div`
  font-weight: ${props => props.$unread ? 'bold' : 'normal'};
  color: ${props => props.theme.colors.text};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const ChatTime = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.secondaryText};
  white-space: nowrap;
`;

const LastMessageContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const LastMessage = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
`;

const MessageStatus = styled.div`
  display: flex;
  align-items: center;
  color: ${props => props.$read ? props.theme.colors.primary : props.theme.colors.secondaryText};
  font-size: 14px;
  margin-left: 5px;
`;

const UnreadBadge = styled.div`
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-left: 5px;
`;

const ChatItem = ({ chat, isActive, onClick }) => {
  const { currentTheme } = useTheme();

  const getInitials = (name) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <ChatItemContainer $isActive={isActive} onClick={onClick} theme={currentTheme}>
      <Avatar theme={currentTheme}>
        {chat.avatar ? (
          <img src={chat.avatar} alt={chat.name} />
        ) : (
          getInitials(chat.name)
        )}
        {chat.isOnline && <OnlineIndicator theme={currentTheme} />}
      </Avatar>
      <ChatInfo>
        <ChatHeader>
          <ChatName $unread={chat.unreadCount > 0} theme={currentTheme}>{chat.name}</ChatName>
          <ChatTime theme={currentTheme}>{chat.lastMessageTime}</ChatTime>
        </ChatHeader>
        <LastMessageContainer>
          <LastMessage theme={currentTheme}>
            {chat.isGroup && chat.lastMessage.includes(':') ? chat.lastMessage : chat.lastMessage}
          </LastMessage>
          {chat.unreadCount > 0 ? (
            <UnreadBadge theme={currentTheme}>{chat.unreadCount}</UnreadBadge>
          ) : (
            <MessageStatus $read={true} theme={currentTheme}>
              <FaCheckDouble />
            </MessageStatus>
          )}
        </LastMessageContainer>
      </ChatInfo>
    </ChatItemContainer>
  );
};

export default ChatItem;
