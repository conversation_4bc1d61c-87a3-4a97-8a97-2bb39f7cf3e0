import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  FaArrowLeft,
  FaPalette,
  FaFont,
  FaCommentAlt,
  FaImage,
  FaEye,
  FaAdjust,
  FaBrush,
  FaDesktop,
  FaMobile,
  FaTabletAlt,
  FaChevronRight,
  FaMoon,
  FaSun,
  FaToggleOn,
  FaToggleOff
} from 'react-icons/fa';
import { useTheme } from '../../context/ThemeContext';

const AppearanceContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${props => props.theme.colors.background};
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
`;

const SettingsHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  min-height: 60px;
  position: sticky;
  top: 0;
  z-index: 10;
`;

const HeaderTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
`;

const HeaderButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const SettingsContent = styled.div`
  flex: 1;
  padding: 0;
`;

const SectionHeader = styled.div`
  padding: 16px 16px 8px 16px;
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.primary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background-color: ${props => props.theme.colors.secondary};
`;

const MenuItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const MenuItemContent = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const MenuIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
`;

const MenuInfo = styled.div`
  flex: 1;
`;

const MenuTitle = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 2px;
`;

const MenuSubtitle = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.secondaryText};
`;

const MenuAction = styled.div`
  display: flex;
  align-items: center;
  color: ${props => props.theme.colors.secondaryText};
`;

const QuickToggleSection = styled.div`
  padding: 16px;
  background-color: ${props => props.theme.colors.background};
  border-bottom: 8px solid ${props => props.theme.colors.secondary};
`;

const QuickToggle = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 12px;
  margin-bottom: 12px;
`;

const ToggleInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const ToggleIcon = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary}20;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.color || props.theme.colors.primary};
  font-size: 16px;
`;

const ToggleText = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
`;

const ToggleSwitch = styled.div`
  position: relative;
  width: 50px;
  height: 24px;
  background-color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.border};
  border-radius: 12px;
  cursor: pointer;
  transition: background-color 0.3s;

  &::after {
    content: '';
    position: absolute;
    top: 2px;
    left: ${props => props.$active ? '26px' : '2px'};
    width: 20px;
    height: 20px;
    background-color: white;
    border-radius: 50%;
    transition: left 0.3s;
  }
`;

const AppearanceHub = ({ onClose, onOpenThemes, onOpenFonts, onOpenChatStyle }) => {
  const { currentTheme, isDarkMode, toggleDarkMode } = useTheme();
  const [settings, setSettings] = useState({
    highContrast: false,
    reduceMotion: false,
    autoTheme: false
  });

  const toggleSetting = (setting) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  const handleMenuClick = (action) => {
    switch (action) {
      case 'themes':
        if (onOpenThemes) onOpenThemes();
        break;
      case 'fonts':
        if (onOpenFonts) onOpenFonts();
        break;
      case 'chatStyle':
        if (onOpenChatStyle) onOpenChatStyle();
        break;
      default:
        console.log('Opening:', action);
    }
  };

  return (
    <AppearanceContainer theme={currentTheme}>
      <SettingsHeader theme={currentTheme}>
        <HeaderButton onClick={onClose}>
          <FaArrowLeft />
        </HeaderButton>
        <HeaderTitle>Appearance</HeaderTitle>
        <HeaderButton onClick={() => {}}>
          {/* Placeholder for future action */}
        </HeaderButton>
      </SettingsHeader>

      <SettingsContent>
        <QuickToggleSection theme={currentTheme}>
          <QuickToggle theme={currentTheme}>
            <ToggleInfo>
              <ToggleIcon color={isDarkMode ? '#ffa726' : '#2196f3'} theme={currentTheme}>
                {isDarkMode ? <FaMoon /> : <FaSun />}
              </ToggleIcon>
              <ToggleText theme={currentTheme}>
                {isDarkMode ? 'Dark Mode' : 'Light Mode'}
              </ToggleText>
            </ToggleInfo>
            <ToggleSwitch 
              $active={isDarkMode} 
              onClick={toggleDarkMode}
              theme={currentTheme}
            />
          </QuickToggle>

          <QuickToggle theme={currentTheme}>
            <ToggleInfo>
              <ToggleIcon color="#9c27b0" theme={currentTheme}>
                <FaEye />
              </ToggleIcon>
              <ToggleText theme={currentTheme}>High Contrast</ToggleText>
            </ToggleInfo>
            <ToggleSwitch 
              $active={settings.highContrast} 
              onClick={() => toggleSetting('highContrast')}
              theme={currentTheme}
            />
          </QuickToggle>

          <QuickToggle theme={currentTheme}>
            <ToggleInfo>
              <ToggleIcon color="#607d8b" theme={currentTheme}>
                <FaAdjust />
              </ToggleIcon>
              <ToggleText theme={currentTheme}>Reduce Motion</ToggleText>
            </ToggleInfo>
            <ToggleSwitch 
              $active={settings.reduceMotion} 
              onClick={() => toggleSetting('reduceMotion')}
              theme={currentTheme}
            />
          </QuickToggle>
        </QuickToggleSection>

        <SectionHeader theme={currentTheme}>Themes & Colors</SectionHeader>
        
        <MenuItem theme={currentTheme} onClick={() => handleMenuClick('themes')}>
          <MenuItemContent>
            <MenuIcon color="#25d366" theme={currentTheme}>
              <FaPalette />
            </MenuIcon>
            <MenuInfo>
              <MenuTitle theme={currentTheme}>Themes</MenuTitle>
              <MenuSubtitle theme={currentTheme}>Choose from 8 available themes</MenuSubtitle>
            </MenuInfo>
          </MenuItemContent>
          <MenuAction theme={currentTheme}>
            <FaChevronRight />
          </MenuAction>
        </MenuItem>

        <MenuItem theme={currentTheme} onClick={() => handleMenuClick('chatStyle')}>
          <MenuItemContent>
            <MenuIcon color="#2196f3" theme={currentTheme}>
              <FaCommentAlt />
            </MenuIcon>
            <MenuInfo>
              <MenuTitle theme={currentTheme}>Chat Wallpaper & Style</MenuTitle>
              <MenuSubtitle theme={currentTheme}>Customize chat bubbles and backgrounds</MenuSubtitle>
            </MenuInfo>
          </MenuItemContent>
          <MenuAction theme={currentTheme}>
            <FaChevronRight />
          </MenuAction>
        </MenuItem>

        <SectionHeader theme={currentTheme}>Typography</SectionHeader>
        
        <MenuItem theme={currentTheme} onClick={() => handleMenuClick('fonts')}>
          <MenuItemContent>
            <MenuIcon color="#ff9800" theme={currentTheme}>
              <FaFont />
            </MenuIcon>
            <MenuInfo>
              <MenuTitle theme={currentTheme}>Fonts & Typography</MenuTitle>
              <MenuSubtitle theme={currentTheme}>Font size, family, and text styling</MenuSubtitle>
            </MenuInfo>
          </MenuItemContent>
          <MenuAction theme={currentTheme}>
            <FaChevronRight />
          </MenuAction>
        </MenuItem>
      </SettingsContent>
    </AppearanceContainer>
  );
};

export default AppearanceHub;
