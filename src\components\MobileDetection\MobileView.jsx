import React from 'react';
import styled from 'styled-components';
import { FaMobile, FaDesktop, FaArrowRight } from 'react-icons/fa';

const MobileViewContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: ${props => props.theme.colors.background};
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  
  @media (max-width: 768px) {
    display: none;
  }
`;

const ContentWrapper = styled.div`
  max-width: 600px;
  text-align: center;
`;

const Title = styled.h2`
  font-size: 24px;
  margin-bottom: 20px;
  color: ${props => props.theme.colors.text};
`;

const Description = styled.p`
  font-size: 16px;
  margin-bottom: 30px;
  color: ${props => props.theme.colors.secondaryText};
  line-height: 1.5;
`;

const DeviceIllustration = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
  gap: 30px;
`;

const DeviceIcon = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: ${props => props.$active 
    ? props.theme.colors.primary 
    : props.theme.colors.secondary};
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    font-size: 40px;
    color: ${props => props.$active 
      ? 'white' 
      : props.theme.colors.secondaryText};
  }
`;

const ArrowIcon = styled.div`
  font-size: 24px;
  color: ${props => props.theme.colors.secondaryText};
`;

const Button = styled.button`
  padding: 12px 24px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: ${props => {
      const color = props.theme.colors.primary;
      // Darken the color by 10%
      return color.startsWith('#') 
        ? `#${color.substring(1).split('').map(c => {
            const hex = parseInt(c, 16);
            return Math.max(0, hex - 1).toString(16);
          }).join('')}`
        : color;
    }};
  }
`;

const FeatureList = styled.div`
  margin: 30px 0;
  text-align: left;
`;

const FeatureItem = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  
  &:before {
    content: '✓';
    display: inline-block;
    width: 24px;
    height: 24px;
    background-color: ${props => props.theme.colors.primary};
    color: white;
    border-radius: 50%;
    margin-right: 15px;
    text-align: center;
    line-height: 24px;
    font-size: 14px;
  }
`;

const FeatureText = styled.div`
  color: ${props => props.theme.colors.text};
  font-size: 16px;
`;

const MobileView = ({ theme, onContinue }) => {
  return (
    <MobileViewContainer theme={theme}>
      <ContentWrapper>
        <Title theme={theme}>Desktop View Detected</Title>
        
        <DeviceIllustration>
          <DeviceIcon $active={false} theme={theme}>
            <FaDesktop />
          </DeviceIcon>
          <ArrowIcon theme={theme}>
            <FaArrowRight />
          </ArrowIcon>
          <DeviceIcon $active={true} theme={theme}>
            <FaMobile />
          </DeviceIcon>
        </DeviceIllustration>
        
        <Description theme={theme}>
          GBChat is optimized for mobile devices. For the best experience, we recommend using a mobile device or resizing your browser window to a narrower width.
        </Description>
        
        <FeatureList>
          <FeatureItem theme={theme}>
            <FeatureText theme={theme}>Responsive design optimized for mobile screens</FeatureText>
          </FeatureItem>
          <FeatureItem theme={theme}>
            <FeatureText theme={theme}>Touch-friendly interface for easy navigation</FeatureText>
          </FeatureItem>
          <FeatureItem theme={theme}>
            <FeatureText theme={theme}>Improved performance on mobile devices</FeatureText>
          </FeatureItem>
        </FeatureList>
        
        <Button onClick={onContinue} theme={theme}>
          Continue to Desktop Version
        </Button>
      </ContentWrapper>
    </MobileViewContainer>
  );
};

export default MobileView;
