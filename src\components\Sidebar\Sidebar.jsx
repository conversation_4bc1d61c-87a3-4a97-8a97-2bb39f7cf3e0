import React, { useState } from 'react';
import styled from 'styled-components';
import ChatList from '../ChatList/ChatList';
import SidebarHeader from './SidebarHeader';
import Settings from '../Settings/Settings';
import { useTheme } from '../../context/ThemeContext';
import { FaComments, FaCircle, FaPhone } from 'react-icons/fa';

const SidebarContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 30%;
  height: 100%;
  border-right: 1px solid ${props => props.theme.colors.border};
  background-color: ${props => props.theme.colors.background};
  position: relative;

  /* Mobile view */
  @media (max-width: 768px) {
    width: 100%;
    display: ${props => props.$isMobileView && props.$activeChat ? 'none' : 'flex'};
  }

  /* Tablet and small desktop */
  @media (min-width: 769px) and (max-width: 1199px) {
    width: 35%;
  }

  /* Medium desktop */
  @media (min-width: 1200px) and (max-width: 1599px) {
    width: 30%;
  }

  /* Large desktop */
  @media (min-width: 1600px) and (max-width: 1999px) {
    width: 25%;
  }

  /* Ultra wide */
  @media (min-width: 2000px) {
    width: 20%;
  }
`;

const SearchContainer = styled.div`
  padding: 8px 16px;
  background-color: ${props => props.theme.colors.secondary};
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 8px 12px;
  border-radius: 20px;
  border: none;
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
  outline: none;

  &::placeholder {
    color: ${props => props.theme.colors.secondaryText};
  }
`;

const TabsContainer = styled.div`
  display: flex;
  background-color: ${props => props.theme.colors.secondary};
  border-top: 1px solid ${props => props.theme.colors.border};
  position: sticky;
  bottom: 0;
  width: 100%;
`;

const Tab = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
  cursor: pointer;
  transition: color 0.2s;

  &:hover {
    color: ${props => props.theme.colors.primary};
  }

  svg {
    font-size: 1.2rem;
    margin-bottom: 4px;
  }
`;

const TabLabel = styled.div`
  font-size: 12px;
  font-weight: ${props => props.$active ? '600' : '400'};
`;

const SectionHeader = styled.div`
  padding: 10px 16px;
  font-size: 13px;
  color: ${props => props.theme.colors.secondaryText};
  background-color: ${props => props.theme.colors.secondary};
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ChatTypeSelector = styled.div`
  display: flex;
  padding: 10px 16px;
  background-color: ${props => props.theme.colors.secondary};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const ChatTypeButton = styled.button`
  flex: 1;
  padding: 8px 0;
  background-color: ${props => props.$active ? props.theme.colors.primary : 'transparent'};
  color: ${props => props.$active ? 'white' : props.theme.colors.secondaryText};
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: ${props => props.$active ? '500' : 'normal'};
  cursor: pointer;
  transition: all 0.2s;
  margin: 0 4px;

  &:hover {
    background-color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.border};
  }
`;

const Sidebar = ({
  chats,
  activeChat,
  setActiveChat,
  isMobileView,
  onProfileClick,
  onLogout,
  onTabChange,
  activeTab = 'chats',
  onMenuClick,
  onQuickSettingsClick
}) => {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [chatType, setChatType] = useState('all'); // 'all', 'private', 'groups'
  const { currentTheme } = useTheme();

  const handleSettingsClick = () => {
    // Use the comprehensive settings instead of the regular settings
    if (window.handleNavigation) {
      window.handleNavigation('advanced');
    } else {
      setIsSettingsOpen(true);
    }
  };

  const handleCloseSettings = () => {
    setIsSettingsOpen(false);
  };

  const handleTabClick = (tab) => {
    if (onTabChange) {
      onTabChange(tab);
    }
  };

  const filteredChats = chats.filter(chat => {
    if (chatType === 'all') return true;
    if (chatType === 'private') return !chat.isGroup;
    if (chatType === 'groups') return chat.isGroup;
    return true;
  });

  // Expose the setChatType function to the window object
  // so it can be called from the NavigationDrawer
  React.useEffect(() => {
    // Define the function to set chat type to groups
    window.setChatTypeToGroups = () => setChatType('groups');

    // Check if there's a pending groups filter request
    if (window.pendingGroupsFilter) {
      setChatType('groups');
      window.pendingGroupsFilter = false;
    }

    return () => {
      delete window.setChatTypeToGroups;
    };
  }, []);

  // Check if the activeTab is 'groups' and update the chat type accordingly
  React.useEffect(() => {
    if (activeTab === 'groups') {
      setChatType('groups');
    }
  }, [activeTab]);

  return (
    <SidebarContainer
      theme={currentTheme}
      $isMobileView={isMobileView}
      $activeChat={activeChat}
    >
      <SidebarHeader
        onSettingsClick={handleSettingsClick}
        onProfileClick={onProfileClick}
        onLogout={onLogout}
        onMenuClick={onMenuClick}
        onQuickSettingsClick={onQuickSettingsClick}
      />

      {activeTab === 'chats' && (
        <>
          <SearchContainer theme={currentTheme}>
            <SearchInput
              placeholder="Search or start new chat"
              theme={currentTheme}
            />
          </SearchContainer>

          <ChatTypeSelector theme={currentTheme}>
            <ChatTypeButton
              $active={chatType === 'all'}
              onClick={() => setChatType('all')}
              theme={currentTheme}
            >
              All
            </ChatTypeButton>
            <ChatTypeButton
              $active={chatType === 'private'}
              onClick={() => setChatType('private')}
              theme={currentTheme}
            >
              Private
            </ChatTypeButton>
            <ChatTypeButton
              $active={chatType === 'groups'}
              onClick={() => setChatType('groups')}
              theme={currentTheme}
            >
              Groups
            </ChatTypeButton>
          </ChatTypeSelector>

          {chatType === 'all' && (
            <>
              {filteredChats.some(chat => !chat.isGroup) && (
                <SectionHeader theme={currentTheme}>Private Chats</SectionHeader>
              )}
              <ChatList
                chats={filteredChats.filter(chat => !chat.isGroup)}
                activeChat={activeChat}
                setActiveChat={setActiveChat}
              />

              {filteredChats.some(chat => chat.isGroup) && (
                <SectionHeader theme={currentTheme}>Group Chats</SectionHeader>
              )}
              <ChatList
                chats={filteredChats.filter(chat => chat.isGroup)}
                activeChat={activeChat}
                setActiveChat={setActiveChat}
              />
            </>
          )}

          {chatType !== 'all' && (
            <ChatList
              chats={filteredChats}
              activeChat={activeChat}
              setActiveChat={setActiveChat}
            />
          )}
        </>
      )}

      <TabsContainer theme={currentTheme}>
        <Tab
          $active={activeTab === 'chats'}
          onClick={() => handleTabClick('chats')}
          theme={currentTheme}
        >
          <FaComments />
          <TabLabel $active={activeTab === 'chats'}>Chats</TabLabel>
        </Tab>
        <Tab
          $active={activeTab === 'status'}
          onClick={() => handleTabClick('status')}
          theme={currentTheme}
        >
          <FaCircle />
          <TabLabel $active={activeTab === 'status'}>Status</TabLabel>
        </Tab>
        <Tab
          $active={activeTab === 'calls'}
          onClick={() => handleTabClick('calls')}
          theme={currentTheme}
        >
          <FaPhone />
          <TabLabel $active={activeTab === 'calls'}>Calls</TabLabel>
        </Tab>
      </TabsContainer>

      <Settings
        isOpen={isSettingsOpen}
        onClose={handleCloseSettings}
        theme={currentTheme}
      />
    </SidebarContainer>
  );
};

export default Sidebar;
