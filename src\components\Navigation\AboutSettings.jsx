import React from 'react';
import styled from 'styled-components';
import { 
  FaInfoCircle, 
  FaCode, 
  FaLock, 
  FaFileAlt, 
  FaGlobe,
  FaHeart,
  FaStar,
  FaGithub,
  FaTwitter,
  FaFacebook,
  FaInstagram,
  FaLinkedin
} from 'react-icons/fa';
import Logo from '../../assets/logo';

const AboutContainer = styled.div`
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const LogoSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
`;

const AppVersion = styled.div`
  margin-top: 12px;
  color: ${props => props.theme.colors.secondaryText};
  font-size: 14px;
`;

const AboutList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const AboutItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const AboutInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const IconWrapper = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary}20;
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    color: ${props => props.color || props.theme.colors.primary};
    font-size: 18px;
  }
`;

const AboutText = styled.div`
  display: flex;
  flex-direction: column;
`;

const AboutTitle = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 0.95rem;
`;

const AboutDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 0.8rem;
  margin-top: 4px;
`;

const TeamSection = styled.div`
  margin-top: 24px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const TeamTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  margin-bottom: 16px;
  
  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const TeamDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
`;

const SocialLinks = styled.div`
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
`;

const SocialLink = styled.a`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  transition: transform 0.2s;
  
  &:hover {
    transform: scale(1.1);
  }
`;

const RatingSection = styled.div`
  margin-top: 24px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const RatingTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  margin-bottom: 16px;
  
  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const RatingStars = styled.div`
  display: flex;
  gap: 4px;
  margin-bottom: 16px;
`;

const StarIcon = styled.div`
  color: ${props => props.$active ? '#FFC107' : props.theme.colors.border};
  font-size: 30px;
  cursor: pointer;
  
  &:hover {
    color: #FFC107;
  }
`;

const RateButton = styled.button`
  padding: 8px 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: ${props => {
      const color = props.theme.colors.primary;
      // Darken the color by 10%
      return color.startsWith('#') 
        ? `#${color.substring(1).split('').map(c => {
            const hex = parseInt(c, 16);
            return Math.max(0, hex - 1).toString(16);
          }).join('')}`
        : color;
    }};
  }
`;

const AboutSettings = ({ theme }) => {
  const [rating, setRating] = React.useState(0);
  
  const handleRating = (value) => {
    setRating(value);
  };
  
  const handleSubmitRating = () => {
    console.log(`Submitting rating: ${rating}`);
    // Add logic to submit rating here
    alert(`Thanks for rating us ${rating} stars!`);
  };
  
  const aboutItems = [
    { 
      title: 'Terms of Service', 
      description: 'Read our terms and conditions', 
      icon: <FaFileAlt />, 
      color: '#4CAF50',
      onClick: () => console.log('Opening Terms of Service...')
    },
    { 
      title: 'Privacy Policy', 
      description: 'How we handle your data', 
      icon: <FaLock />, 
      color: '#2196F3',
      onClick: () => console.log('Opening Privacy Policy...')
    },
    { 
      title: 'Licenses', 
      description: 'Open source licenses', 
      icon: <FaCode />, 
      color: '#9C27B0',
      onClick: () => console.log('Opening Licenses...')
    },
    { 
      title: 'Website', 
      description: 'Visit our official website', 
      icon: <FaGlobe />, 
      color: '#FF9800',
      onClick: () => console.log('Opening Website...')
    }
  ];
  
  return (
    <AboutContainer theme={theme}>
      <SectionTitle theme={theme}>About GBChat</SectionTitle>
      
      <LogoSection>
        <Logo size="80px" theme={theme} />
        <AppVersion theme={theme}>Version 1.0.0</AppVersion>
      </LogoSection>
      
      <AboutList>
        {aboutItems.map((item, index) => (
          <AboutItem key={index} theme={theme} onClick={item.onClick}>
            <AboutInfo>
              <IconWrapper theme={theme} color={item.color}>
                {item.icon}
              </IconWrapper>
              <AboutText>
                <AboutTitle theme={theme}>{item.title}</AboutTitle>
                <AboutDescription theme={theme}>{item.description}</AboutDescription>
              </AboutText>
            </AboutInfo>
          </AboutItem>
        ))}
      </AboutList>
      
      <TeamSection theme={theme}>
        <TeamTitle theme={theme}>
          <FaHeart />
          Our Team
        </TeamTitle>
        
        <TeamDescription theme={theme}>
          GBChat is developed by a passionate team of developers dedicated to creating a secure and feature-rich messaging experience. We're constantly working to improve the app and add new features.
        </TeamDescription>
        
        <SocialLinks>
          <SocialLink href="#" color="#333" theme={theme}>
            <FaGithub />
          </SocialLink>
          <SocialLink href="#" color="#1DA1F2" theme={theme}>
            <FaTwitter />
          </SocialLink>
          <SocialLink href="#" color="#4267B2" theme={theme}>
            <FaFacebook />
          </SocialLink>
          <SocialLink href="#" color="#E1306C" theme={theme}>
            <FaInstagram />
          </SocialLink>
          <SocialLink href="#" color="#0077B5" theme={theme}>
            <FaLinkedin />
          </SocialLink>
        </SocialLinks>
      </TeamSection>
      
      <RatingSection theme={theme}>
        <RatingTitle theme={theme}>
          <FaStar />
          Rate GBChat
        </RatingTitle>
        
        <RatingStars>
          {[1, 2, 3, 4, 5].map(value => (
            <StarIcon 
              key={value} 
              $active={rating >= value}
              theme={theme}
              onClick={() => handleRating(value)}
            >
              <FaStar />
            </StarIcon>
          ))}
        </RatingStars>
        
        <RateButton 
          theme={theme} 
          onClick={handleSubmitRating}
          disabled={rating === 0}
        >
          Submit Rating
        </RateButton>
      </RatingSection>
    </AboutContainer>
  );
};

export default AboutSettings;
