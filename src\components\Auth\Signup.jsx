import React, { useState } from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON><PERSON><PERSON>, FaEnvelope, <PERSON>aLock, <PERSON>aEye, FaEyeSlash, FaArrowLeft } from 'react-icons/fa';
import Logo from '../../assets/logo';

const SignupContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
  background-color: ${props => props.theme.colors.background};
  padding: 20px;
`;

const SignupForm = styled.form`
  width: 100%;
  max-width: 400px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
  position: relative;
`;

const LogoContainer = styled.div`
  margin-bottom: 20px;
`;

const Title = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: 20px;
  text-align: center;
`;

const InputGroup = styled.div`
  position: relative;
  margin-bottom: 20px;
`;

const Input = styled.input`
  width: 100%;
  padding: 12px 15px 12px 45px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 5px;
  font-size: 16px;
  color: ${props => props.theme.colors.text};
  background-color: ${props => props.theme.colors.background};
  transition: border-color 0.3s;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }

  &::placeholder {
    color: ${props => props.theme.colors.secondaryText};
  }
`;

const InputIcon = styled.div`
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.secondaryText};
  font-size: 18px;
`;

const PasswordToggle = styled.div`
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.secondaryText};
  font-size: 18px;
  cursor: pointer;

  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const Button = styled.button`
  width: 100%;
  padding: 12px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 10px;

  &:hover {
    background-color: ${props => {
      const color = props.theme.colors.primary;
      // Darken the color by 10%
      return color.startsWith('#')
        ? '#' + color.substring(1).match(/.{2}/g).map(c => {
            const num = parseInt(c, 16);
            return Math.max(0, num - 25).toString(16).padStart(2, '0');
          }).join('')
        : color;
    }};
  }

  &:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
`;

const BackButton = styled.button`
  position: absolute;
  top: 20px;
  left: 20px;
  background: none;
  border: none;
  color: ${props => props.theme.colors.text};
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;

  &:hover {
    color: ${props => props.theme.colors.primary};
  }

  span {
    font-size: 14px;
  }
`;

const LoginLink = styled.div`
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  color: ${props => props.theme.colors.secondaryText};

  a {
    color: ${props => props.theme.colors.primary};
    text-decoration: none;
    font-weight: 600;

    &:hover {
      text-decoration: underline;
    }
  }
`;

const ErrorMessage = styled.div`
  color: #e74c3c;
  font-size: 14px;
  margin-top: 5px;
  margin-bottom: 15px;
`;

const TermsText = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.secondaryText};
  text-align: center;
  margin-top: 20px;

  a {
    color: ${props => props.theme.colors.primary};
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
`;

const Signup = ({ onSignup, onLoginClick, theme }) => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();

    // Simple validation
    if (!name || !email || !password || !confirmPassword) {
      setError('Please fill in all fields');
      return;
    }

    if (!email.includes('@')) {
      setError('Please enter a valid email address');
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setError('');
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      onSignup({ name, email });
    }, 1500);
  };

  return (
    <SignupContainer theme={theme}>
      <LogoContainer theme={theme}>
        <Logo vertical large theme={theme} />
      </LogoContainer>

      <SignupForm onSubmit={handleSubmit} theme={theme}>
        <BackButton onClick={onLoginClick} theme={theme}>
          <FaArrowLeft />
          <span>Back to Login</span>
        </BackButton>

        <Title theme={theme}>Create Account</Title>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <InputGroup>
          <InputIcon theme={theme}>
            <FaUser />
          </InputIcon>
          <Input
            type="text"
            placeholder="Full Name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            theme={theme}
          />
        </InputGroup>

        <InputGroup>
          <InputIcon theme={theme}>
            <FaEnvelope />
          </InputIcon>
          <Input
            type="email"
            placeholder="Email Address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            theme={theme}
          />
        </InputGroup>

        <InputGroup>
          <InputIcon theme={theme}>
            <FaLock />
          </InputIcon>
          <Input
            type={showPassword ? 'text' : 'password'}
            placeholder="Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            theme={theme}
          />
          <PasswordToggle
            onClick={() => setShowPassword(!showPassword)}
            theme={theme}
          >
            {showPassword ? <FaEyeSlash /> : <FaEye />}
          </PasswordToggle>
        </InputGroup>

        <InputGroup>
          <InputIcon theme={theme}>
            <FaLock />
          </InputIcon>
          <Input
            type={showConfirmPassword ? 'text' : 'password'}
            placeholder="Confirm Password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            theme={theme}
          />
          <PasswordToggle
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            theme={theme}
          >
            {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
          </PasswordToggle>
        </InputGroup>

        <Button
          type="submit"
          disabled={isLoading}
          theme={theme}
        >
          {isLoading ? 'Creating Account...' : 'Sign Up'}
        </Button>

        <TermsText theme={theme}>
          By signing up, you agree to our <a href="#terms">Terms of Service</a> and <a href="#privacy">Privacy Policy</a>
        </TermsText>

        <LoginLink theme={theme}>
          Already have an account? <a href="#login" onClick={(e) => {
            e.preventDefault();
            onLoginClick();
          }}>Login</a>
        </LoginLink>
      </SignupForm>
    </SignupContainer>
  );
};

export default Signup;
