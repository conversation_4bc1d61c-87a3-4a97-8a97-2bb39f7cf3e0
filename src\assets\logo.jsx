import React from 'react';
import styled from 'styled-components';
import { FaCommentAlt, FaComments } from 'react-icons/fa';

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: ${props => props.$vertical ? 'column' : 'row'};
  gap: ${props => props.$vertical ? '10px' : '15px'};
`;

const LogoIcon = styled.div`
  width: ${props => props.$size || '50px'};
  height: ${props => props.$size || '50px'};
  border-radius: ${props => props.$rounded ? '50%' : '15px'};
  background-color: ${props => props.$transparent ? 'transparent' : props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: ${props => props.$transparent ? 'none' : '0 4px 12px rgba(0, 0, 0, 0.15)'};
  transition: all 0.3s ease;
  overflow: hidden;

  svg {
    color: ${props => props.$iconColor || 'white'};
    font-size: ${props => props.$size ? `calc(${props.$size} * 0.6)` : '30px'};
    filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
  }

  &:hover {
    transform: ${props => props.$noHover ? 'none' : 'scale(1.05)'};
    box-shadow: ${props => props.$transparent ? 'none' : '0 6px 16px rgba(0, 0, 0, 0.2)'};
  }

  &::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: ${props => props.$transparent ? 'none' : 'linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%)'};
    top: 0;
    left: 0;
  }
`;

const LogoText = styled.div`
  font-size: ${props => props.$large ? '28px' : '20px'};
  font-weight: 700;
  color: ${props => props.$color || props.theme.colors.text};
  letter-spacing: 1px;
  text-shadow: ${props => props.$textShadow ? '0 2px 4px rgba(0, 0, 0, 0.2)' : 'none'};
`;

const AppName = styled.div`
  font-size: ${props => props.$large ? '18px' : '14px'};
  color: ${props => props.$color || props.theme.colors.secondaryText};
  letter-spacing: 1px;
  text-shadow: ${props => props.$textShadow ? '0 1px 2px rgba(0, 0, 0, 0.2)' : 'none'};
`;

const Logo = ({
  vertical = false,
  showName = true,
  size,
  large = false,
  theme,
  rounded = false,
  variant = 'default', // 'default' or 'alt'
  color, // For text color
  transparent = false, // For transparent background
  noHover = false, // Disable hover effects
  textShadow = false // Add text shadow
}) => {
  // Default theme colors if theme is not provided
  const defaultTheme = {
    colors: {
      primary: '#25D366',
      text: '#333333',
      secondaryText: '#666666'
    }
  };

  const themeToUse = theme || defaultTheme;

  // Choose icon based on variant
  const LogoIconComponent = variant === 'alt' ? FaComments : FaCommentAlt;

  return (
    <LogoContainer $vertical={vertical} theme={themeToUse}>
      <LogoIcon
        $size={size}
        $rounded={rounded}
        $transparent={transparent}
        $noHover={noHover}
        $iconColor={color}
        theme={themeToUse}
      >
        <LogoIconComponent />
      </LogoIcon>
      {showName && (
        <div>
          <LogoText
            $large={large}
            $color={color}
            $textShadow={textShadow}
            theme={themeToUse}
          >
            GBChat
          </LogoText>
          {vertical && (
            <AppName
              $large={large}
              $color={color}
              $textShadow={textShadow}
              theme={themeToUse}
            >
              Enhanced Messaging
            </AppName>
          )}
        </div>
      )}
    </LogoContainer>
  );
};

export default Logo;
