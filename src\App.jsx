import { useState, useEffect } from 'react';
import styled from 'styled-components';
import Sidebar from './components/Sidebar/Sidebar';
import ChatWindow from './components/ChatWindow/ChatWindow';
import { mockChats, mockMessages } from './data/mockData';
import { ThemeProvider, useTheme } from './context/ThemeContext';
import { PrivacyProvider } from './context/PrivacyContext';
import { AuthProvider, useAuth } from './context/AuthContext';
import SplashScreen from './components/SplashScreen/SplashScreen';
import Login from './components/Auth/Login';
import Signup from './components/Auth/Signup';
import StatusBar from './components/Status/StatusBar';
import CallsTab from './components/Calls/CallsTab';
import ProfileSettings from './components/Profile/ProfileSettings';
import GlobalStyles from './styles/GlobalStyles';
import NavigationDrawer from './components/Navigation/NavigationDrawer';
import BottomNavigation from './components/Navigation/BottomNavigation';
import QuickSettings from './components/StatusBar/QuickSettings';
import MobileView from './components/MobileDetection/MobileView';
import DataStorageSettings from './components/Navigation/DataStorageSettings';
import WebVersionSettings from './components/Navigation/WebVersionSettings';
import HelpSettings from './components/Navigation/HelpSettings';
import AboutSettings from './components/Navigation/AboutSettings';
import MobileStatusBar from './components/Mobile/MobileStatusBar';
import MobileGestures from './components/Mobile/MobileGestures';
import MobileFloatingButton from './components/Mobile/MobileFloatingButton';
import MobileKeyboard from './components/Mobile/MobileKeyboard';
import MobileHaptics from './components/Mobile/MobileHaptics';
import MobileVideoCall from './components/Mobile/MobileVideoCall';
import MobileVoiceCall from './components/Mobile/MobileVoiceCall';
import MobileCamera from './components/Mobile/MobileCamera';
import ContactsManager from './components/Mobile/ContactsManager';
import FileManager from './components/Mobile/FileManager';
import ProfileSettings from './components/Settings/ProfileSettings';
import EnhancedPrivacySettings from './components/Settings/EnhancedPrivacySettings';
import EnhancedNotificationSettings from './components/Settings/EnhancedNotificationSettings';

const AppContainer = styled.div`
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  position: relative;

  /* Large screens */
  @media (min-width: 1200px) {
    max-width: 95%;
    height: 95vh;
    margin: 2.5vh auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
  }

  /* Extra large screens */
  @media (min-width: 1600px) {
    max-width: 1600px;
  }

  /* Ultra wide screens */
  @media (min-width: 2000px) {
    max-width: 1800px;
  }
`;

const AppContent = () => {
  const { currentUser, login, signup, logout } = useAuth();
  const { currentTheme } = useTheme();

  const [chats, setChats] = useState(mockChats);
  const [activeChat, setActiveChat] = useState(null);
  const [messages, setMessages] = useState({...mockMessages});
  const [isMobileView, setIsMobileView] = useState(window.innerWidth <= 768);
  const [showSplash, setShowSplash] = useState(true);
  const [authMode, setAuthMode] = useState('login'); // 'login' or 'signup'
  const [showProfileSettings, setShowProfileSettings] = useState(false);
  const [activeTab, setActiveTab] = useState('chats'); // 'chats', 'status', 'calls'
  const [showNavDrawer, setShowNavDrawer] = useState(false);
  const [showQuickSettings, setShowQuickSettings] = useState(false);
  const [activeNavSection, setActiveNavSection] = useState('');
  const [showDataStorage, setShowDataStorage] = useState(false);
  const [showWebVersion, setShowWebVersion] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [showAbout, setShowAbout] = useState(false);
  const [showMobileWarning, setShowMobileWarning] = useState(!isMobileView && window.innerWidth > 768);
  const [forceMobileView, setForceMobileView] = useState(false);
  const [showMobileKeyboard, setShowMobileKeyboard] = useState(false);
  const [currentInputText, setCurrentInputText] = useState('');
  const [showFloatingButton, setShowFloatingButton] = useState(true);
  const [showVideoCall, setShowVideoCall] = useState(false);
  const [showVoiceCall, setShowVoiceCall] = useState(false);
  const [showCamera, setShowCamera] = useState(false);
  const [currentCall, setCurrentCall] = useState(null);
  const [showContacts, setShowContacts] = useState(false);
  const [showFileManager, setShowFileManager] = useState(false);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [showPrivacySettings, setShowPrivacySettings] = useState(false);
  const [showNotificationSettings, setShowNotificationSettings] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth <= 768;
      setIsMobileView(isMobile);

      // Show mobile warning if switching from mobile to desktop
      if (!isMobile && !forceMobileView) {
        setShowMobileWarning(true);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleSendMessage = (text, messageData = {}) => {
    if (!activeChat) return;

    const newMessage = {
      id: messages[activeChat.id].length + 1,
      text,
      time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      isSent: true,
      ...messageData
    };

    setMessages(prevMessages => ({
      ...prevMessages,
      [activeChat.id]: [...prevMessages[activeChat.id], newMessage],
    }));

    // Update last message in chat list
    setChats(prevChats =>
      prevChats.map(chat =>
        chat.id === activeChat.id
          ? {
              ...chat,
              lastMessage: messageData.type === 'voice' ? '🎤 Voice message' : text,
              lastMessageTime: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
              unreadCount: 0
            }
          : chat
      )
    );
  };

  const handleBackClick = () => {
    setActiveChat(null);
  };

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  const handleLogin = (userData) => {
    login(userData);
  };

  const handleSignup = (userData) => {
    signup(userData);
  };

  const handleLogout = () => {
    logout();
    setActiveChat(null);
  };

  const handleProfileClick = () => {
    setShowProfileSettings(true);
  };

  const handleCloseProfile = () => {
    setShowProfileSettings(false);
  };

  const handleTabChange = (tab) => {
    // First set the active tab
    setActiveTab(tab);

    // Handle special tabs
    if (tab === 'settings') {
      // Open profile settings when settings tab is clicked
      setShowProfileSettings(true);
    } else if (tab === 'groups') {
      // For groups tab, we need to:
      // 1. Make sure we're in the chats view
      // 2. Filter to show only groups

      // Force the sidebar to show only groups
      if (window.setChatTypeToGroups) {
        window.setChatTypeToGroups();
      } else {
        // If the global function isn't available yet, set it up for when it becomes available
        window.pendingGroupsFilter = true;
      }
    }
  };

  const handleToggleNavDrawer = () => {
    setShowNavDrawer(prev => !prev);
  };

  const handleCloseNavDrawer = () => {
    setShowNavDrawer(false);
  };

  const handleToggleQuickSettings = () => {
    setShowQuickSettings(prev => !prev);
  };

  const handleCloseQuickSettings = () => {
    setShowQuickSettings(false);
  };

  const handleNavigation = (section) => {
    setActiveNavSection(section);

    // Handle navigation based on section
    if (section === 'profile') {
      setShowProfileSettings(true);
    } else if (section === 'groups') {
      setActiveTab('chats');
      // Set chat type to groups in the sidebar
      if (window.setChatTypeToGroups) {
        window.setChatTypeToGroups();
      }
    } else if (section === 'archived' || section === 'starred') {
      setActiveTab('chats');
    } else if (section === 'contacts') {
      setShowContacts(true);
    } else if (section === 'files') {
      setShowFileManager(true);
    } else if (section === 'privacy') {
      setShowPrivacySettings(true);
    } else if (section === 'notifications') {
      setShowNotificationSettings(true);
    } else if (section === 'advanced' || section === 'settings') {
      setShowAdvancedSettings(true);
    } else if (section === 'theme' || section === 'general' ||
               section === 'security' || section === 'fonts' ||
               section === 'chatStyle') {
      // Open advanced settings for these sections
      setShowAdvancedSettings(true);
    } else if (section === 'data') {
      setShowDataStorage(true);
    } else if (section === 'web') {
      setShowWebVersion(true);
    } else if (section === 'help') {
      setShowHelp(true);
    } else if (section === 'about') {
      setShowAbout(true);
    }
  };

  const handleContinueDesktop = () => {
    setShowMobileWarning(false);
    setForceMobileView(true);
  };

  const handleCloseDataStorage = () => {
    setShowDataStorage(false);
  };

  const handleCloseWebVersion = () => {
    setShowWebVersion(false);
  };

  const handleCloseHelp = () => {
    setShowHelp(false);
  };

  const handleCloseAbout = () => {
    setShowAbout(false);
  };

  const handleCloseContacts = () => {
    setShowContacts(false);
  };

  const handleCloseFileManager = () => {
    setShowFileManager(false);
  };

  const handleCloseAdvancedSettings = () => {
    setShowAdvancedSettings(false);
  };

  const handleClosePrivacySettings = () => {
    setShowPrivacySettings(false);
  };

  const handleCloseNotificationSettings = () => {
    setShowNotificationSettings(false);
  };

  // Mobile-specific handlers
  const handleMobileSwipeLeft = () => {
    if (activeChat && isMobileView) {
      setActiveChat(null);
    }
  };

  const handleMobileSwipeRight = () => {
    if (!activeChat && isMobileView) {
      setShowNavDrawer(true);
    }
  };

  const handlePullToRefresh = async () => {
    // Simulate refresh
    return new Promise(resolve => {
      setTimeout(() => {
        MobileHaptics.success();
        resolve();
      }, 1000);
    });
  };

  const handleFloatingButtonAction = (action) => {
    MobileHaptics.medium();
    switch (action) {
      case 'newChat':
        setActiveTab('chats');
        break;
      case 'newGroup':
        setActiveTab('groups');
        break;
      case 'camera':
        setShowCamera(true);
        break;
      case 'contacts':
        setShowContacts(true);
        break;
      case 'files':
        setShowFileManager(true);
        break;
      case 'call':
        if (activeChat) {
          setCurrentCall(activeChat);
          setShowVoiceCall(true);
        } else {
          setActiveTab('calls');
        }
        break;
      case 'videoCall':
        if (activeChat) {
          setCurrentCall(activeChat);
          setShowVideoCall(true);
        }
        break;
      default:
        break;
    }
  };

  const handleMobileKeyboardToggle = () => {
    setShowMobileKeyboard(!showMobileKeyboard);
  };

  const handleTextInsert = (text) => {
    setCurrentInputText(text);
    setShowMobileKeyboard(false);
  };

  // Call handlers
  const handleVoiceCall = (contact) => {
    MobileHaptics.medium();
    setCurrentCall(contact || activeChat);
    setShowVoiceCall(true);
  };

  const handleVideoCall = (contact) => {
    MobileHaptics.medium();
    setCurrentCall(contact || activeChat);
    setShowVideoCall(true);
  };

  const handleEndCall = () => {
    MobileHaptics.heavy();
    setShowVoiceCall(false);
    setShowVideoCall(false);
    setCurrentCall(null);
  };

  const handleAcceptCall = () => {
    MobileHaptics.success();
    // Handle call acceptance logic
  };

  const handleDeclineCall = () => {
    MobileHaptics.heavy();
    setShowVoiceCall(false);
    setShowVideoCall(false);
    setCurrentCall(null);
  };

  // Camera handlers
  const handleCameraCapture = (media) => {
    MobileHaptics.success();
    // Handle captured media
    console.log('Captured media:', media);
  };

  const handleCloseCamera = () => {
    MobileHaptics.light();
    setShowCamera(false);
  };

  // Show splash screen
  if (showSplash) {
    return <SplashScreen onComplete={handleSplashComplete} theme={currentTheme} />;
  }

  // Show authentication screens if not logged in
  if (!currentUser) {
    return authMode === 'login' ? (
      <Login
        onLogin={handleLogin}
        onSignupClick={() => setAuthMode('signup')}
        theme={currentTheme}
      />
    ) : (
      <Signup
        onSignup={handleSignup}
        onLoginClick={() => setAuthMode('login')}
        theme={currentTheme}
      />
    );
  }

  // Main app content when logged in
  return (
    <>
      {/* Mobile Status Bar */}
      {isMobileView && <MobileStatusBar theme={currentTheme} />}

      {/* Mobile Warning for Desktop Users */}
      {showMobileWarning && (
        <MobileView
          theme={currentTheme}
          onContinue={handleContinueDesktop}
        />
      )}
    <AppContainer>
      {/* Profile Settings */}
      <ProfileSettings
        isOpen={showProfileSettings}
        onClose={handleCloseProfile}
        theme={currentTheme}
      />

      {/* Navigation Drawer */}
      <NavigationDrawer
        show={showNavDrawer}
        onClose={handleCloseNavDrawer}
        theme={currentTheme}
        activeSection={activeNavSection}
        onNavigate={handleNavigation}
        user={currentUser}
      />

      {/* Quick Settings */}
      <QuickSettings
        show={showQuickSettings}
        onClose={handleCloseQuickSettings}
        theme={currentTheme}
      />

      {/* Data Storage Settings */}
      <ProfileSettings
        isOpen={showDataStorage}
        onClose={handleCloseDataStorage}
        theme={currentTheme}
        customContent={<DataStorageSettings theme={currentTheme} />}
        title="Data and Storage"
      />

      {/* Web Version Settings */}
      <ProfileSettings
        isOpen={showWebVersion}
        onClose={handleCloseWebVersion}
        theme={currentTheme}
        customContent={<WebVersionSettings theme={currentTheme} />}
        title="Web Version"
      />

      {/* Help Settings */}
      <ProfileSettings
        isOpen={showHelp}
        onClose={handleCloseHelp}
        theme={currentTheme}
        customContent={<HelpSettings theme={currentTheme} />}
        title="Help"
      />

      {/* About Settings */}
      <ProfileSettings
        isOpen={showAbout}
        onClose={handleCloseAbout}
        theme={currentTheme}
        customContent={<AboutSettings theme={currentTheme} />}
        title="About"
      />

      {/* Main Content */}
      <MobileGestures
        theme={currentTheme}
        onSwipeLeft={handleMobileSwipeLeft}
        onSwipeRight={handleMobileSwipeRight}
        onPullToRefresh={handlePullToRefresh}
        enableSwipe={isMobileView}
        enablePullToRefresh={isMobileView && activeTab === 'chats'}
      >
        {activeTab === 'chats' || activeTab === 'groups' ? (
          <>
            <Sidebar
              chats={chats}
              activeChat={activeChat}
              setActiveChat={setActiveChat}
              isMobileView={isMobileView}
              onProfileClick={handleProfileClick}
              onLogout={handleLogout}
              onTabChange={handleTabChange}
              activeTab={activeTab}
              onMenuClick={handleToggleNavDrawer}
              onQuickSettingsClick={handleToggleQuickSettings}
            />
            <ChatWindow
              activeChat={activeChat}
              messages={activeChat ? messages[activeChat.id] : []}
              sendMessage={handleSendMessage}
              isMobileView={isMobileView}
              onBackClick={handleBackClick}
              onVoiceCall={handleVoiceCall}
              onVideoCall={handleVideoCall}
            />
          </>
        ) : activeTab === 'status' ? (
          <StatusBar theme={currentTheme} />
        ) : activeTab === 'calls' ? (
          <CallsTab theme={currentTheme} />
        ) : activeTab === 'settings' ? (
          <ProfileSettings
            isOpen={true}
            onClose={() => setActiveTab('chats')}
            theme={currentTheme}
          />
        ) : (
          <StatusBar theme={currentTheme} />
        )}
      </MobileGestures>

      {/* Mobile Floating Action Button */}
      {isMobileView && showFloatingButton && (
        <MobileFloatingButton
          theme={currentTheme}
          onNewChat={() => handleFloatingButtonAction('newChat')}
          onNewGroup={() => handleFloatingButtonAction('newGroup')}
          onCamera={() => handleFloatingButtonAction('camera')}
          onContacts={() => handleFloatingButtonAction('contacts')}
          onFiles={() => handleFloatingButtonAction('files')}
          onCall={() => handleFloatingButtonAction('call')}
          onVideoCall={() => handleFloatingButtonAction('videoCall')}
        />
      )}

      {/* Mobile Keyboard */}
      {isMobileView && (
        <MobileKeyboard
          theme={currentTheme}
          visible={showMobileKeyboard}
          onClose={() => setShowMobileKeyboard(false)}
          onTextInsert={handleTextInsert}
          currentText={currentInputText}
        />
      )}

      {/* Bottom Navigation for Mobile */}
      <BottomNavigation
        activeTab={activeTab}
        onTabChange={handleTabChange}
        theme={currentTheme}
      />

      {/* Mobile Video Call */}
      {showVideoCall && (
        <MobileVideoCall
          contact={currentCall}
          onEndCall={handleEndCall}
          onAccept={handleAcceptCall}
          onDecline={handleDeclineCall}
        />
      )}

      {/* Mobile Voice Call */}
      {showVoiceCall && (
        <MobileVoiceCall
          contact={currentCall}
          onEndCall={handleEndCall}
          onAccept={handleAcceptCall}
          onDecline={handleDeclineCall}
        />
      )}

      {/* Mobile Camera */}
      {showCamera && (
        <MobileCamera
          onClose={handleCloseCamera}
          onCapture={handleCameraCapture}
          theme={currentTheme}
        />
      )}

      {/* Contacts Manager */}
      {showContacts && (
        <ContactsManager
          theme={currentTheme}
          onClose={handleCloseContacts}
          onSelectContact={(contact) => {
            // Handle contact selection for new chat
            console.log('Selected contact:', contact);
            setShowContacts(false);
          }}
        />
      )}

      {/* File Manager */}
      {showFileManager && (
        <FileManager
          theme={currentTheme}
          onClose={handleCloseFileManager}
        />
      )}

      {/* Advanced Settings */}
      {showAdvancedSettings && (
        <ProfileSettings
          theme={currentTheme}
          onClose={handleCloseAdvancedSettings}
          onOpenProfile={() => {
            setShowAdvancedSettings(false);
            setShowProfileSettings(true);
          }}
          onOpenPrivacy={() => {
            setShowAdvancedSettings(false);
            setShowPrivacySettings(true);
          }}
          onOpenNotifications={() => {
            setShowAdvancedSettings(false);
            setShowNotificationSettings(true);
          }}
        />
      )}

      {/* Enhanced Privacy Settings */}
      {showPrivacySettings && (
        <EnhancedPrivacySettings
          theme={currentTheme}
          onClose={handleClosePrivacySettings}
        />
      )}

      {/* Enhanced Notification Settings */}
      {showNotificationSettings && (
        <EnhancedNotificationSettings
          theme={currentTheme}
          onClose={handleCloseNotificationSettings}
        />
      )}
    </AppContainer>
    </>
  );
};

function App() {
  return (
    <ThemeProvider>
      <PrivacyProvider>
        <AuthProvider>
          <GlobalStyles />
          <AppContent />
        </AuthProvider>
      </PrivacyProvider>
    </ThemeProvider>
  );
}

export default App;
