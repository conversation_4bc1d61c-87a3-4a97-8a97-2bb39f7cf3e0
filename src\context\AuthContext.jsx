import React, { createContext, useState, useContext, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Check if user is stored in localStorage
    const storedUser = localStorage.getItem('chatwave-user');
    
    if (storedUser) {
      setCurrentUser(JSON.parse(storedUser));
    }
    
    setLoading(false);
  }, []);
  
  const login = (userData) => {
    setCurrentUser(userData);
    localStorage.setItem('chatwave-user', JSON.stringify(userData));
  };
  
  const signup = (userData) => {
    setCurrentUser(userData);
    localStorage.setItem('chatwave-user', JSON.stringify(userData));
  };
  
  const logout = () => {
    setCurrentUser(null);
    localStorage.removeItem('chatwave-user');
  };
  
  const value = {
    currentUser,
    login,
    signup,
    logout,
    loading
  };
  
  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
