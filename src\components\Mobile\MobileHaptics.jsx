// Mobile Haptic Feedback Utility
class MobileHaptics {
  static isSupported() {
    return 'vibrate' in navigator || 'hapticFeedback' in navigator;
  }

  static light() {
    if (this.isSupported()) {
      // Light haptic feedback
      if (navigator.vibrate) {
        navigator.vibrate(10);
      }
    }
  }

  static medium() {
    if (this.isSupported()) {
      // Medium haptic feedback
      if (navigator.vibrate) {
        navigator.vibrate(25);
      }
    }
  }

  static heavy() {
    if (this.isSupported()) {
      // Heavy haptic feedback
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    }
  }

  static success() {
    if (this.isSupported()) {
      // Success pattern: short-long-short
      if (navigator.vibrate) {
        navigator.vibrate([10, 50, 10]);
      }
    }
  }

  static error() {
    if (this.isSupported()) {
      // Error pattern: long-short-long
      if (navigator.vibrate) {
        navigator.vibrate([50, 25, 50]);
      }
    }
  }

  static notification() {
    if (this.isSupported()) {
      // Notification pattern: double tap
      if (navigator.vibrate) {
        navigator.vibrate([15, 25, 15]);
      }
    }
  }

  static selection() {
    if (this.isSupported()) {
      // Selection feedback: very light
      if (navigator.vibrate) {
        navigator.vibrate(5);
      }
    }
  }

  static longPress() {
    if (this.isSupported()) {
      // Long press feedback: medium with pause
      if (navigator.vibrate) {
        navigator.vibrate([30, 100, 30]);
      }
    }
  }
}

export default MobileHaptics;
