import React, { useState } from 'react';
import styled from 'styled-components';
import { FaEllipsisV, FaSearch, FaArrowLeft, FaVideo, FaPhone, FaTimes } from 'react-icons/fa';
import { useTheme } from '../../context/ThemeContext';
import { usePrivacy } from '../../context/PrivacyContext';

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: ${props => props.theme.colors.secondary};
  height: 60px;
  position: relative;
  z-index: 10;
`;

const ProfileSection = styled.div`
  display: flex;
  align-items: center;
`;

const Avatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  color: white;
  font-size: 20px;
  position: relative;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const OnlineIndicator = styled.div`
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #4CAF50;
  border: 2px solid ${props => props.theme.colors.secondary};
  z-index: 1;
`;

const ChatInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const ChatName = styled.div`
  font-weight: 500;
  color: ${props => props.theme.colors.text};
`;

const LastSeen = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.secondaryText};
`;

const IconsSection = styled.div`
  display: flex;
  gap: 24px;
  color: ${props => props.theme.colors.icon};
  font-size: 1.2rem;
`;

const IconWrapper = styled.div`
  cursor: pointer;

  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const BackButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.icon};
  font-size: 1.2rem;
  cursor: pointer;
  margin-right: 10px;
  display: ${props => props.$show ? 'block' : 'none'};
  padding: 0;

  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const SearchBarContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: ${props => props.theme.colors.secondary};
  display: ${props => props.$show ? 'flex' : 'none'};
  align-items: center;
  padding: 0 16px;
  z-index: 20;
  animation: slideIn 0.2s ease-out;

  @keyframes slideIn {
    from { transform: translateY(-60px); }
    to { transform: translateY(0); }
  }
`;

const SearchInput = styled.input`
  flex: 1;
  padding: 8px 12px;
  border-radius: 20px;
  border: none;
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
  outline: none;
  margin: 0 10px;

  &::placeholder {
    color: ${props => props.theme.colors.secondaryText};
  }
`;

const SearchButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.icon};
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const ChatHeader = ({ chat, showBackButton, onBackClick, onSearch }) => {
  const { currentTheme } = useTheme();
  const { privacySettings } = usePrivacy();
  const [showSearchBar, setShowSearchBar] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const getInitials = (name) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const handleSearchClick = () => {
    setShowSearchBar(true);
  };

  const handleCloseSearch = () => {
    setShowSearchBar(false);
    setSearchQuery('');
    if (onSearch) {
      onSearch('');
    }
  };

  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    if (onSearch) {
      onSearch(query);
    }
  };

  return (
    <HeaderContainer theme={currentTheme}>
      <ProfileSection>
        <BackButton
          $show={showBackButton}
          onClick={onBackClick}
          theme={currentTheme}
        >
          <FaArrowLeft />
        </BackButton>
        <Avatar theme={currentTheme}>
          {chat.avatar ? (
            <img src={chat.avatar} alt={chat.name} />
          ) : (
            getInitials(chat.name)
          )}
          {chat.isOnline && <OnlineIndicator theme={currentTheme} />}
        </Avatar>
        <ChatInfo>
          <ChatName theme={currentTheme}>{chat.name}</ChatName>
          {!privacySettings.hideOnlineStatus && (
            <LastSeen theme={currentTheme}>
              {chat.isOnline
                ? 'online'
                : chat.isGroup
                  ? `${chat.members?.length || 0} members`
                  : `last seen today at ${chat.lastSeen}`}
            </LastSeen>
          )}
        </ChatInfo>
      </ProfileSection>
      <IconsSection theme={currentTheme}>
        <IconWrapper theme={currentTheme}>
          <FaVideo />
        </IconWrapper>
        <IconWrapper theme={currentTheme}>
          <FaPhone />
        </IconWrapper>
        <IconWrapper theme={currentTheme} onClick={handleSearchClick}>
          <FaSearch />
        </IconWrapper>
        <IconWrapper theme={currentTheme}>
          <FaEllipsisV />
        </IconWrapper>
      </IconsSection>

      <SearchBarContainer $show={showSearchBar} theme={currentTheme}>
        <SearchButton theme={currentTheme} onClick={handleCloseSearch}>
          <FaArrowLeft />
        </SearchButton>
        <SearchInput
          placeholder="Search messages"
          value={searchQuery}
          onChange={handleSearchChange}
          theme={currentTheme}
          autoFocus
        />
        {searchQuery && (
          <SearchButton theme={currentTheme} onClick={() => setSearchQuery('')}>
            <FaTimes />
          </SearchButton>
        )}
      </SearchBarContainer>
    </HeaderContainer>
  );
};

export default ChatHeader;
