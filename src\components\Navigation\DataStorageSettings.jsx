import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  FaDatabase, 
  FaCloudDownloadAlt, 
  FaCloudUploadAlt, 
  FaToggleOn, 
  FaToggleOff,
  FaWifi,
  FaNetworkWired,
  FaImage,
  FaVideo,
  FaFileAudio,
  FaFile,
  FaTrash
} from 'react-icons/fa';

const DataStorageContainer = styled.div`
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const SettingsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const SettingItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const SettingInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const IconWrapper = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary}20;
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    color: ${props => props.color || props.theme.colors.primary};
    font-size: 18px;
  }
`;

const SettingText = styled.div`
  display: flex;
  flex-direction: column;
`;

const SettingName = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 0.95rem;
`;

const SettingDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 0.8rem;
  margin-top: 4px;
`;

const ToggleButton = styled.div`
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
  font-size: 1.5rem;
  cursor: pointer;
`;

const StorageInfo = styled.div`
  margin-top: 24px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const StorageTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  margin-bottom: 12px;
  
  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const StorageBar = styled.div`
  height: 8px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 4px;
  margin: 8px 0;
  overflow: hidden;
  position: relative;
`;

const StorageUsed = styled.div`
  height: 100%;
  width: ${props => props.$percentage}%;
  background-color: ${props => props.theme.colors.primary};
  border-radius: 4px;
`;

const StorageDetails = styled.div`
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: ${props => props.theme.colors.secondaryText};
`;

const StorageBreakdown = styled.div`
  margin-top: 16px;
`;

const StorageItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  
  &:last-child {
    border-bottom: none;
  }
`;

const StorageItemInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const StorageItemIcon = styled.div`
  color: ${props => props.color || props.theme.colors.primary};
  font-size: 16px;
`;

const StorageItemName = styled.div`
  color: ${props => props.theme.colors.text};
  font-size: 14px;
`;

const StorageItemSize = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 14px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 16px;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  border: none;
  background-color: ${props => props.theme.colors.secondary};
  color: ${props => props.theme.colors.text};
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.primary}20;
  }
  
  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const NetworkUsageContainer = styled.div`
  margin-top: 24px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const UsageStats = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
`;

const UsageColumn = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const UsageValue = styled.div`
  font-size: 18px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
`;

const UsageLabel = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.secondaryText};
  margin-top: 4px;
`;

const DataStorageSettings = ({ theme }) => {
  const [settings, setSettings] = useState({
    autoDownloadWifi: true,
    autoDownloadMobile: false,
    highQualityUploads: false,
    dataCompression: true,
    autoBackup: true
  });
  
  const toggleSetting = (setting) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };
  
  const storageData = {
    total: 5, // GB
    used: 2.7, // GB
    breakdown: [
      { type: 'Photos', size: 1.2, icon: <FaImage />, color: '#4CAF50' },
      { type: 'Videos', size: 0.8, icon: <FaVideo />, color: '#2196F3' },
      { type: 'Audio', size: 0.4, icon: <FaFileAudio />, color: '#FF9800' },
      { type: 'Documents', size: 0.2, icon: <FaFile />, color: '#9C27B0' },
      { type: 'Other', size: 0.1, icon: <FaFile />, color: '#607D8B' }
    ]
  };
  
  const networkUsage = {
    sent: 1.2, // GB
    received: 3.5, // GB
    wifi: 3.8, // GB
    mobile: 0.9 // GB
  };
  
  const storagePercentage = (storageData.used / storageData.total) * 100;
  
  const handleClearCache = () => {
    if (window.confirm('Are you sure you want to clear the cache? This action cannot be undone.')) {
      console.log('Cache cleared!');
      // Add logic to clear cache here
    }
  };
  
  const handleManageStorage = () => {
    console.log('Navigating to storage management...');
    // Add logic to navigate to storage management here
  };
  
  return (
    <DataStorageContainer theme={theme}>
      <SectionTitle theme={theme}>Data and Storage</SectionTitle>
      
      <SettingsList>
        <SettingItem theme={theme} onClick={() => toggleSetting('autoDownloadWifi')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#4CAF50">
              <FaWifi />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Auto-download on Wi-Fi</SettingName>
              <SettingDescription theme={theme}>
                Automatically download media when connected to Wi-Fi
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.autoDownloadWifi} 
            theme={theme}
          >
            {settings.autoDownloadWifi ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('autoDownloadMobile')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#2196F3">
              <FaNetworkWired />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Auto-download on mobile data</SettingName>
              <SettingDescription theme={theme}>
                Automatically download media when using mobile data
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.autoDownloadMobile} 
            theme={theme}
          >
            {settings.autoDownloadMobile ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('highQualityUploads')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#9C27B0">
              <FaCloudUploadAlt />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>High quality uploads</SettingName>
              <SettingDescription theme={theme}>
                Upload media in high quality (uses more data)
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.highQualityUploads} 
            theme={theme}
          >
            {settings.highQualityUploads ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('dataCompression')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#FF9800">
              <FaCloudDownloadAlt />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Data compression</SettingName>
              <SettingDescription theme={theme}>
                Reduce data usage when using the app
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.dataCompression} 
            theme={theme}
          >
            {settings.dataCompression ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('autoBackup')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#E91E63">
              <FaCloudUploadAlt />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Auto backup</SettingName>
              <SettingDescription theme={theme}>
                Automatically backup chats to the cloud
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.autoBackup} 
            theme={theme}
          >
            {settings.autoBackup ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
      </SettingsList>
      
      <StorageInfo theme={theme}>
        <StorageTitle theme={theme}>
          <FaDatabase />
          Storage Usage
        </StorageTitle>
        
        <StorageBar theme={theme}>
          <StorageUsed $percentage={storagePercentage} theme={theme} />
        </StorageBar>
        
        <StorageDetails theme={theme}>
          <span>{storageData.used.toFixed(1)} GB used</span>
          <span>{storageData.total} GB total</span>
        </StorageDetails>
        
        <StorageBreakdown>
          {storageData.breakdown.map((item, index) => (
            <StorageItem key={index} theme={theme}>
              <StorageItemInfo>
                <StorageItemIcon color={item.color}>
                  {item.icon}
                </StorageItemIcon>
                <StorageItemName theme={theme}>{item.type}</StorageItemName>
              </StorageItemInfo>
              <StorageItemSize theme={theme}>{item.size.toFixed(1)} GB</StorageItemSize>
            </StorageItem>
          ))}
        </StorageBreakdown>
        
        <ActionButtons>
          <ActionButton theme={theme} onClick={handleClearCache} title="Clear all cached data">
            <FaTrash />
            Clear Cache
          </ActionButton>
          <ActionButton theme={theme} onClick={handleManageStorage} title="Manage your storage usage">
            <FaDatabase />
            Manage Storage
          </ActionButton>
        </ActionButtons>
      </StorageInfo>
      
      <NetworkUsageContainer theme={theme}>
        <StorageTitle theme={theme}>
          <FaNetworkWired />
          Network Usage
        </StorageTitle>
        
        <UsageStats>
          <UsageColumn>
            <UsageValue theme={theme}>{networkUsage.sent.toFixed(1)} GB</UsageValue>
            <UsageLabel theme={theme}>Sent</UsageLabel>
          </UsageColumn>
          
          <UsageColumn>
            <UsageValue theme={theme}>{networkUsage.received.toFixed(1)} GB</UsageValue>
            <UsageLabel theme={theme}>Received</UsageLabel>
          </UsageColumn>
          
          <UsageColumn>
            <UsageValue theme={theme}>{networkUsage.wifi.toFixed(1)} GB</UsageValue>
            <UsageLabel theme={theme}>Wi-Fi</UsageLabel>
          </UsageColumn>
          
          <UsageColumn>
            <UsageValue theme={theme}>{networkUsage.mobile.toFixed(1)} GB</UsageValue>
            <UsageLabel theme={theme}>Mobile</UsageLabel>
          </UsageColumn>
        </UsageStats>
      </NetworkUsageContainer>
    </DataStorageContainer>
  );
};

export default DataStorageSettings;
