import React from 'react';
import styled from 'styled-components';
import ChatItem from './ChatItem';
import { useTheme } from '../../context/ThemeContext';

const ChatListContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  background-color: ${props => props.theme.colors.background};
`;

const ChatList = ({ chats, activeChat, setActiveChat }) => {
  const { currentTheme } = useTheme();

  return (
    <ChatListContainer theme={currentTheme}>
      {chats.map((chat) => (
        <ChatItem
          key={chat.id}
          chat={chat}
          isActive={activeChat?.id === chat.id}
          onClick={() => setActiveChat(chat)}
        />
      ))}
    </ChatListContainer>
  );
};

export default ChatList;
