import React, { useState } from 'react';
import styled from 'styled-components';
import { FaToggleOn, FaToggleOff, FaDownload, FaTrash, FaLock, FaDatabase, FaCloudUploadAlt, FaExclamationTriangle } from 'react-icons/fa';

const AdvancedSettingsContainer = styled.div`
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const SettingsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const SettingItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const SettingInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const IconWrapper = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary}20;
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    color: ${props => props.color || props.theme.colors.primary};
    font-size: 18px;
  }
`;

const SettingText = styled.div`
  display: flex;
  flex-direction: column;
`;

const SettingName = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 0.95rem;
`;

const SettingDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 0.8rem;
  margin-top: 4px;
`;

const ToggleButton = styled.div`
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
  font-size: 1.5rem;
  cursor: pointer;
`;

const StorageInfo = styled.div`
  margin-top: 24px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const StorageTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  margin-bottom: 12px;
  
  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const StorageBar = styled.div`
  height: 8px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 4px;
  margin: 8px 0;
  overflow: hidden;
  position: relative;
`;

const StorageUsed = styled.div`
  height: 100%;
  width: ${props => props.$percentage}%;
  background-color: ${props => props.theme.colors.primary};
  border-radius: 4px;
`;

const StorageDetails = styled.div`
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: ${props => props.theme.colors.secondaryText};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 16px;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  border: none;
  background-color: ${props => props.theme.colors.secondary};
  color: ${props => props.theme.colors.text};
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.primary}20;
  }
  
  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const AdvancedSettings = ({ theme }) => {
  const [settings, setSettings] = useState({
    autoDownload: true,
    readReceipts: true,
    encryption: true,
    dataBackup: false,
    autoSync: true
  });
  
  const toggleSetting = (setting) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };
  
  const storageUsed = 2.7; // GB
  const storageTotal = 5; // GB
  const storagePercentage = (storageUsed / storageTotal) * 100;

  const handleClearCache = () => {
    if (window.confirm('Are you sure you want to clear the cache? This action cannot be undone.')) {
      console.log('Cache cleared!');
      // Add logic to clear cache here
    }
  };

  const handleManageStorage = () => {
    console.log('Navigating to storage management...');
    // Add logic to navigate to storage management here
  };
  
  return (
    <AdvancedSettingsContainer theme={theme}>
      <SectionTitle theme={theme}>Advanced Settings</SectionTitle>
      
      <SettingsList>
        <SettingItem theme={theme} onClick={() => toggleSetting('autoDownload')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#4CAF50">
              <FaDownload />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Auto-download media</SettingName>
              <SettingDescription theme={theme}>
                Automatically download photos and videos
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.autoDownload} 
            theme={theme}
          >
            {settings.autoDownload ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('readReceipts')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#2196F3">
              <FaLock />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Read receipts</SettingName>
              <SettingDescription theme={theme}>
                Let others know when you've read their messages
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.readReceipts} 
            theme={theme}
          >
            {settings.readReceipts ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('encryption')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#9C27B0">
              <FaLock />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>End-to-end encryption</SettingName>
              <SettingDescription theme={theme}>
                Messages are secured with end-to-end encryption
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.encryption} 
            theme={theme}
          >
            {settings.encryption ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('dataBackup')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#FF9800">
              <FaCloudUploadAlt />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Cloud backup</SettingName>
              <SettingDescription theme={theme}>
                Backup your chats to the cloud
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.dataBackup} 
            theme={theme}
          >
            {settings.dataBackup ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
      </SettingsList>
      
      <StorageInfo theme={theme}>
        <StorageTitle theme={theme}>
          <FaDatabase />
          Storage and Data
        </StorageTitle>
        
        <StorageBar theme={theme}>
          <StorageUsed $percentage={storagePercentage} theme={theme} />
        </StorageBar>
        
        <StorageDetails theme={theme}>
          <span>{storageUsed.toFixed(1)} GB used</span>
          <span>{storageTotal} GB total</span>
        </StorageDetails>
        
        <ActionButtons>
          <ActionButton theme={theme} onClick={handleClearCache} title="Clear all cached data">
            <FaTrash />
            Clear Cache
          </ActionButton>
          <ActionButton theme={theme} onClick={handleManageStorage} title="Manage your storage usage">
            <FaDownload />
            Manage Storage
          </ActionButton>
        </ActionButtons>
      </StorageInfo>
    </AdvancedSettingsContainer>
  );
};

export default AdvancedSettings;
