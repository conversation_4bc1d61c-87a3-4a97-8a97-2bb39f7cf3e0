import React from 'react';
import styled from 'styled-components';
import { 
  FaQuestionCircle, 
  FaBook, 
  FaHeadset, 
  FaBug, 
  FaInfoCircle,
  FaExclamationTriangle,
  FaLock,
  FaShieldAlt,
  FaUserFriends,
  FaComments,
  FaChevronRight
} from 'react-icons/fa';

const HelpContainer = styled.div`
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const HelpList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const HelpItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const HelpInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const IconWrapper = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary}20;
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    color: ${props => props.color || props.theme.colors.primary};
    font-size: 18px;
  }
`;

const HelpText = styled.div`
  display: flex;
  flex-direction: column;
`;

const HelpTitle = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 0.95rem;
`;

const HelpDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 0.8rem;
  margin-top: 4px;
`;

const ChevronIcon = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 16px;
`;

const FAQContainer = styled.div`
  margin-top: 24px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const FAQTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  margin-bottom: 16px;
  
  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const FAQItem = styled.div`
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const FAQQuestion = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  svg {
    color: ${props => props.theme.colors.secondaryText};
    transition: transform 0.2s;
    transform: ${props => props.$expanded ? 'rotate(90deg)' : 'rotate(0)'};
  }
`;

const FAQAnswer = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 14px;
  line-height: 1.5;
  padding-left: 16px;
  border-left: 2px solid ${props => props.theme.colors.border};
  margin-top: 8px;
  display: ${props => props.$expanded ? 'block' : 'none'};
`;

const ContactContainer = styled.div`
  margin-top: 24px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const ContactTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  margin-bottom: 16px;
  
  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const ContactButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 12px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: ${props => {
      const color = props.theme.colors.primary;
      // Darken the color by 10%
      return color.startsWith('#') 
        ? `#${color.substring(1).split('').map(c => {
            const hex = parseInt(c, 16);
            return Math.max(0, hex - 1).toString(16);
          }).join('')}`
        : color;
    }};
  }
  
  svg {
    font-size: 16px;
  }
`;

const HelpSettings = ({ theme }) => {
  const [expandedFAQ, setExpandedFAQ] = React.useState(null);
  
  const toggleFAQ = (index) => {
    setExpandedFAQ(expandedFAQ === index ? null : index);
  };
  
  const faqs = [
    {
      question: 'How do I change my profile picture?',
      answer: 'To change your profile picture, go to Settings > Profile, then tap on your current profile picture and select a new image from your gallery or take a new photo.'
    },
    {
      question: 'How can I block someone?',
      answer: 'To block someone, open the chat with the person you want to block, tap on their name at the top to view their profile, scroll down and tap "Block".'
    },
    {
      question: 'How do I create a group chat?',
      answer: 'To create a group chat, go to the Chats tab, tap the new chat button, then select "New Group". Choose the contacts you want to add, then tap the arrow to proceed. Add a group name and optional group icon, then tap "Create".'
    },
    {
      question: 'Can I delete messages after sending them?',
      answer: 'Yes, you can delete messages after sending them. Press and hold the message you want to delete, then tap the delete icon. You can choose to delete the message for yourself or for everyone in the chat.'
    },
    {
      question: 'How do I enable dark mode?',
      answer: 'To enable dark mode, go to Settings > Theme > Dark Mode. You can also set it to follow your system settings or schedule it for specific times.'
    }
  ];
  
  const helpTopics = [
    { title: 'User Guide', description: 'Learn how to use GBChat', icon: <FaBook />, color: '#4CAF50' },
    { title: 'Privacy & Security', description: 'Understand your privacy options', icon: <FaLock />, color: '#2196F3' },
    { title: 'Groups & Communities', description: 'How to manage group chats', icon: <FaUserFriends />, color: '#9C27B0' },
    { title: 'Messaging Features', description: 'Learn about all messaging options', icon: <FaComments />, color: '#FF9800' },
    { title: 'Troubleshooting', description: 'Fix common problems', icon: <FaBug />, color: '#F44336' },
    { title: 'Security Tips', description: 'Keep your account secure', icon: <FaShieldAlt />, color: '#607D8B' }
  ];
  
  const handleContactSupport = () => {
    console.log('Contacting support...');
    // Add logic to contact support here
  };
  
  const handleReportBug = () => {
    console.log('Reporting bug...');
    // Add logic to report bug here
  };
  
  return (
    <HelpContainer theme={theme}>
      <SectionTitle theme={theme}>Help Center</SectionTitle>
      
      <HelpList>
        {helpTopics.map((topic, index) => (
          <HelpItem key={index} theme={theme}>
            <HelpInfo>
              <IconWrapper theme={theme} color={topic.color}>
                {topic.icon}
              </IconWrapper>
              <HelpText>
                <HelpTitle theme={theme}>{topic.title}</HelpTitle>
                <HelpDescription theme={theme}>{topic.description}</HelpDescription>
              </HelpText>
            </HelpInfo>
            <ChevronIcon theme={theme}>
              <FaChevronRight />
            </ChevronIcon>
          </HelpItem>
        ))}
      </HelpList>
      
      <FAQContainer theme={theme}>
        <FAQTitle theme={theme}>
          <FaQuestionCircle />
          Frequently Asked Questions
        </FAQTitle>
        
        {faqs.map((faq, index) => (
          <FAQItem key={index}>
            <FAQQuestion 
              theme={theme} 
              onClick={() => toggleFAQ(index)}
              $expanded={expandedFAQ === index}
            >
              {faq.question}
              <FaChevronRight />
            </FAQQuestion>
            <FAQAnswer 
              theme={theme}
              $expanded={expandedFAQ === index}
            >
              {faq.answer}
            </FAQAnswer>
          </FAQItem>
        ))}
      </FAQContainer>
      
      <ContactContainer theme={theme}>
        <ContactTitle theme={theme}>
          <FaHeadset />
          Contact Support
        </ContactTitle>
        
        <ContactButton theme={theme} onClick={handleContactSupport}>
          <FaHeadset />
          Contact Support Team
        </ContactButton>
        
        <div style={{ marginTop: '12px' }}>
          <ContactButton 
            theme={theme} 
            onClick={handleReportBug}
            style={{ 
              backgroundColor: theme.colors.secondary,
              color: theme.colors.text
            }}
          >
            <FaBug />
            Report a Bug
          </ContactButton>
        </div>
      </ContactContainer>
    </HelpContainer>
  );
};

export default HelpSettings;
