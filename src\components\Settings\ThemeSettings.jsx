import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../context/ThemeContext';
import {
  FaMoon,
  FaSun,
  FaArrowLeft,
  FaTimes,
  FaPalette,
  FaImage,
  FaEye,
  FaDownload,
  FaUpload,
  FaCheck,
  FaPlus,
  FaTrash,
  FaEdit,
  FaRandom,
  FaAdjust,
  FaBrush,
  FaGradient,
  FaDesktop,
  FaMobile,
  FaTabletAlt
} from 'react-icons/fa';

const ThemeSettingsContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${props => props.theme.colors.background};
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
`;

const SettingsHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  min-height: 60px;
  position: sticky;
  top: 0;
  z-index: 10;
`;

const HeaderTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
`;

const HeaderButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const SettingsContent = styled.div`
  flex: 1;
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const ThemeGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

const ThemeOption = styled.div`
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid ${props => props.$isActive ? props.theme.colors.primary : 'transparent'};
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.05);
  }
`;

const ThemePreview = styled.div`
  height: 100px;
  display: flex;
  flex-direction: column;
`;

const ThemeHeader = styled.div`
  height: 30%;
  background-color: ${props => props.color};
`;

const ThemeBody = styled.div`
  height: 70%;
  background-color: ${props => props.color};
  display: flex;
`;

const ThemeSidebar = styled.div`
  width: 30%;
  background-color: ${props => props.color};
`;

const ThemeChat = styled.div`
  width: 70%;
  background-color: ${props => props.color};
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 4px;
`;

const ThemeMessage = styled.div`
  width: 60%;
  height: 10px;
  border-radius: 4px;
  background-color: ${props => props.color};
  align-self: ${props => props.$sent ? 'flex-end' : 'flex-start'};
  margin-bottom: 4px;
`;

const ThemeName = styled.div`
  padding: 8px;
  text-align: center;
  font-size: 0.9rem;
  color: ${props => props.theme.colors.text};
  background-color: ${props => props.theme.colors.secondary};
`;

const CustomizationSection = styled.div`
  margin-top: 24px;
`;

const CustomOption = styled.div`
  margin-bottom: 16px;
`;

const CustomLabel = styled.label`
  display: block;
  margin-bottom: 8px;
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
`;

const CustomInput = styled.input`
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid ${props => props.theme.colors.border};
  background-color: ${props => props.theme.colors.secondary};
  color: ${props => props.theme.colors.text};
`;

const DarkModeToggle = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 8px;
  margin-bottom: 20px;
`;

const ToggleLabel = styled.div`
  display: flex;
  align-items: center;
  color: ${props => props.theme.colors.text};
  font-weight: 500;

  svg {
    margin-right: 10px;
    color: ${props => props.$isDark ? '#9b59b6' : '#f39c12'};
  }
`;

const ToggleSwitch = styled.label`
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;

  input {
    opacity: 0;
    width: 0;
    height: 0;
  }
`;

const Slider = styled.span`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;

  &:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }

  input:checked + & {
    background-color: ${props => props.theme.colors.primary};
  }

  input:checked + &:before {
    transform: translateX(26px);
  }
`;

const WallpaperGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  margin-top: 16px;
`;

const WallpaperOption = styled.div`
  position: relative;
  cursor: pointer;
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid ${props => props.$isActive ? props.theme.colors.primary : 'transparent'};
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const WallpaperPreview = styled.img`
  width: 100%;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
`;

const WallpaperUpload = styled.div`
  width: 100%;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: ${props => props.theme.colors.secondary};
  border: 2px dashed ${props => props.theme.colors.border};
  border-radius: 8px;
  color: ${props => props.theme.colors.secondaryText};
  font-size: 12px;
  gap: 4px;

  svg {
    font-size: 20px;
  }
`;

const WallpaperName = styled.div`
  text-align: center;
  font-size: 12px;
  color: ${props => props.theme.colors.text};
  margin-top: 8px;
  padding: 0 4px;
`;

const WallpaperCheck = styled.div`
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background-color: ${props => props.theme?.colors?.primary || '#25d366'};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
`;

const ColorGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
`;

const ColorOption = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 12px;
  border: 1px solid ${props => props.theme.colors.border};
`;

const ColorLabel = styled.div`
  font-size: 14px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
`;

const ColorPicker = styled.input`
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  background: none;

  &::-webkit-color-swatch-wrapper {
    padding: 0;
    border: none;
    border-radius: 50%;
  }

  &::-webkit-color-swatch {
    border: 2px solid ${props => props.theme.colors.border};
    border-radius: 50%;
  }
`;

const SettingsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
`;

const SettingItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 12px;
  border: 1px solid ${props => props.theme.colors.border};
`;

const SettingInfo = styled.div`
  flex: 1;
`;

const SettingName = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 4px;
`;

const SettingDescription = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.secondaryText};
`;

const DeviceGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-top: 16px;
`;

const DeviceOption = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: ${props => props.$isActive ? props.theme.colors.primary : props.theme.colors.secondary};
  color: ${props => props.$isActive ? 'white' : props.theme.colors.text};
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 8px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  svg {
    font-size: 24px;
  }

  span {
    font-size: 14px;
    font-weight: 500;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 24px;
  flex-wrap: wrap;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: 1px solid ${props => props.theme.colors.primary};
  border-radius: 8px;
  background: ${props => props.$primary ? props.theme.colors.primary : 'transparent'};
  color: ${props => props.$primary ? 'white' : props.theme.colors.primary};
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.$primary ? props.theme.colors.primaryDark || props.theme.colors.primary : props.theme.colors.primary};
    color: white;
    transform: translateY(-1px);
  }
`;

const ThemeSettings = ({ theme, onClose }) => {
  const { currentTheme, changeTheme, themes, toggleDarkMode, isDarkMode } = useTheme();

  const [selectedWallpaper, setSelectedWallpaper] = useState(null);
  const [customColors, setCustomColors] = useState({
    primary: currentTheme.colors.primary,
    secondary: currentTheme.colors.secondary,
    accent: currentTheme.colors.accent || currentTheme.colors.primary
  });
  const [settings, setSettings] = useState({
    autoDarkMode: false,
    highContrast: false,
    reduceMotion: false,
    deviceTheme: 'desktop'
  });

  const wallpapers = [
    { name: 'Default', url: '/wallpapers/default.jpg' },
    { name: 'Nature', url: '/wallpapers/nature.jpg' },
    { name: 'Abstract', url: '/wallpapers/abstract.jpg' },
    { name: 'Minimal', url: '/wallpapers/minimal.jpg' },
    { name: 'Gradient', url: '/wallpapers/gradient.jpg' },
    { name: 'Dark', url: '/wallpapers/dark.jpg' }
  ];

  const handleColorChange = (colorType, value) => {
    setCustomColors(prev => ({
      ...prev,
      [colorType]: value
    }));
  };

  const toggleSetting = (setting) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  const handleDeviceThemeChange = (device) => {
    setSettings(prev => ({
      ...prev,
      deviceTheme: device
    }));
  };

  const handleWallpaperUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const newWallpaper = {
          name: 'Custom',
          url: e.target.result
        };
        setSelectedWallpaper(newWallpaper);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSaveTheme = () => {
    // Save custom theme logic
    console.log('Saving theme with colors:', customColors);
    console.log('Settings:', settings);
    console.log('Wallpaper:', selectedWallpaper);
  };

  const handleResetTheme = () => {
    setCustomColors({
      primary: '#25d366',
      secondary: '#f0f0f0',
      accent: '#25d366'
    });
    setSettings({
      autoDarkMode: false,
      highContrast: false,
      reduceMotion: false,
      deviceTheme: 'desktop'
    });
    setSelectedWallpaper(null);
  };

  const handleExportTheme = () => {
    const themeData = {
      colors: customColors,
      settings: settings,
      wallpaper: selectedWallpaper,
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(themeData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `gbchat-theme-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <ThemeSettingsContainer theme={theme || currentTheme}>
      <SettingsHeader theme={currentTheme}>
        <HeaderButton onClick={onClose}>
          <FaArrowLeft />
        </HeaderButton>
        <HeaderTitle>Themes & Appearance</HeaderTitle>
        <HeaderButton onClick={() => {}}>
          {/* Placeholder for future action */}
        </HeaderButton>
      </SettingsHeader>

      <SettingsContent>
        <DarkModeToggle theme={currentTheme}>
          <ToggleLabel $isDark={isDarkMode} theme={currentTheme}>
            {isDarkMode ? <FaMoon /> : <FaSun />}
            {isDarkMode ? 'Dark Mode' : 'Light Mode'}
          </ToggleLabel>
          <ToggleSwitch>
          <input
            type="checkbox"
            checked={isDarkMode}
            onChange={toggleDarkMode}
          />
          <Slider theme={currentTheme} />
        </ToggleSwitch>
      </DarkModeToggle>

      <SectionTitle theme={currentTheme}>Choose Theme</SectionTitle>
      <ThemeGrid>
        {Object.entries(themes).map(([key, theme]) => (
          <ThemeOption
            key={key}
            onClick={() => changeTheme(key)}
            $isActive={currentTheme.name === theme.name}
            theme={currentTheme}
          >
            <ThemePreview>
              <ThemeHeader color={theme.colors.primary} />
              <ThemeBody color={theme.colors.background}>
                <ThemeSidebar color={theme.colors.secondary} />
                <ThemeChat color={theme.colors.chatBackground}>
                  <ThemeMessage color={theme.colors.receivedMessage} $sent={false} />
                  <ThemeMessage color={theme.colors.sentMessage} $sent={true} />
                </ThemeChat>
              </ThemeBody>
            </ThemePreview>
            <ThemeName theme={currentTheme}>{theme.name}</ThemeName>
          </ThemeOption>
        ))}
      </ThemeGrid>

      <CustomizationSection>
        <SectionTitle theme={currentTheme}>
          <FaImage /> Chat Wallpaper
        </SectionTitle>
        <WallpaperGrid>
          {wallpapers.map((wallpaper, index) => (
            <WallpaperOption
              key={index}
              onClick={() => setSelectedWallpaper(wallpaper)}
              $isActive={selectedWallpaper === wallpaper}
              theme={currentTheme}
            >
              <WallpaperPreview src={wallpaper.url} alt={wallpaper.name} />
              <WallpaperName theme={currentTheme}>{wallpaper.name}</WallpaperName>
              {selectedWallpaper === wallpaper && (
                <WallpaperCheck>
                  <FaCheck />
                </WallpaperCheck>
              )}
            </WallpaperOption>
          ))}
          <WallpaperOption theme={currentTheme} onClick={() => document.getElementById('wallpaper-upload').click()}>
            <WallpaperUpload theme={currentTheme}>
              <FaPlus />
              <span>Custom</span>
            </WallpaperUpload>
            <WallpaperName theme={currentTheme}>Upload</WallpaperName>
          </WallpaperOption>
        </WallpaperGrid>
        <input
          id="wallpaper-upload"
          type="file"
          accept="image/*"
          style={{ display: 'none' }}
          onChange={handleWallpaperUpload}
        />
      </CustomizationSection>

      <CustomizationSection>
        <SectionTitle theme={currentTheme}>
          <FaBrush /> Color Customization
        </SectionTitle>
        <ColorGrid>
          <ColorOption theme={currentTheme}>
            <ColorLabel theme={currentTheme}>Primary Color</ColorLabel>
            <ColorPicker
              type="color"
              value={customColors.primary}
              onChange={(e) => handleColorChange('primary', e.target.value)}
              theme={currentTheme}
            />
          </ColorOption>
          <ColorOption theme={currentTheme}>
            <ColorLabel theme={currentTheme}>Secondary Color</ColorLabel>
            <ColorPicker
              type="color"
              value={customColors.secondary}
              onChange={(e) => handleColorChange('secondary', e.target.value)}
              theme={currentTheme}
            />
          </ColorOption>
          <ColorOption theme={currentTheme}>
            <ColorLabel theme={currentTheme}>Accent Color</ColorLabel>
            <ColorPicker
              type="color"
              value={customColors.accent}
              onChange={(e) => handleColorChange('accent', e.target.value)}
              theme={currentTheme}
            />
          </ColorOption>
        </ColorGrid>
      </CustomizationSection>

      <CustomizationSection>
        <SectionTitle theme={currentTheme}>
          <FaAdjust /> Display Settings
        </SectionTitle>
        <SettingsList>
          <SettingItem theme={currentTheme}>
            <SettingInfo>
              <SettingName theme={currentTheme}>Auto Dark Mode</SettingName>
              <SettingDescription theme={currentTheme}>
                Automatically switch to dark mode at sunset
              </SettingDescription>
            </SettingInfo>
            <ToggleSwitch>
              <input
                type="checkbox"
                checked={settings.autoDarkMode}
                onChange={() => toggleSetting('autoDarkMode')}
              />
              <Slider theme={currentTheme} />
            </ToggleSwitch>
          </SettingItem>

          <SettingItem theme={currentTheme}>
            <SettingInfo>
              <SettingName theme={currentTheme}>High Contrast</SettingName>
              <SettingDescription theme={currentTheme}>
                Increase contrast for better visibility
              </SettingDescription>
            </SettingInfo>
            <ToggleSwitch>
              <input
                type="checkbox"
                checked={settings.highContrast}
                onChange={() => toggleSetting('highContrast')}
              />
              <Slider theme={currentTheme} />
            </ToggleSwitch>
          </SettingItem>

          <SettingItem theme={currentTheme}>
            <SettingInfo>
              <SettingName theme={currentTheme}>Reduce Motion</SettingName>
              <SettingDescription theme={currentTheme}>
                Minimize animations and transitions
              </SettingDescription>
            </SettingInfo>
            <ToggleSwitch>
              <input
                type="checkbox"
                checked={settings.reduceMotion}
                onChange={() => toggleSetting('reduceMotion')}
              />
              <Slider theme={currentTheme} />
            </ToggleSwitch>
          </SettingItem>
        </SettingsList>
      </CustomizationSection>

      <CustomizationSection>
        <SectionTitle theme={currentTheme}>
          <FaDesktop /> Device Preferences
        </SectionTitle>
        <DeviceGrid>
          <DeviceOption
            $isActive={settings.deviceTheme === 'desktop'}
            onClick={() => handleDeviceThemeChange('desktop')}
            theme={currentTheme}
          >
            <FaDesktop />
            <span>Desktop</span>
          </DeviceOption>
          <DeviceOption
            $isActive={settings.deviceTheme === 'mobile'}
            onClick={() => handleDeviceThemeChange('mobile')}
            theme={currentTheme}
          >
            <FaMobile />
            <span>Mobile</span>
          </DeviceOption>
          <DeviceOption
            $isActive={settings.deviceTheme === 'tablet'}
            onClick={() => handleDeviceThemeChange('tablet')}
            theme={currentTheme}
          >
            <FaTabletAlt />
            <span>Tablet</span>
          </DeviceOption>
        </DeviceGrid>
      </CustomizationSection>

      <ActionButtons>
        <ActionButton $primary onClick={handleSaveTheme} theme={currentTheme}>
          <FaCheck /> Save Theme
        </ActionButton>
        <ActionButton onClick={handleResetTheme} theme={currentTheme}>
          <FaRandom /> Reset to Default
        </ActionButton>
        <ActionButton onClick={handleExportTheme} theme={currentTheme}>
          <FaDownload /> Export Theme
        </ActionButton>
      </ActionButtons>
      </SettingsContent>
    </ThemeSettingsContainer>
  );
};

export default ThemeSettings;
