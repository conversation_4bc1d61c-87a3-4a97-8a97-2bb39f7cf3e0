import React from 'react';
import styled from 'styled-components';
import { useTheme } from '../../context/ThemeContext';
import { Fa<PERSON>oon, FaSun, FaArrowLeft, FaTimes } from 'react-icons/fa';

const ThemeSettingsContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${props => props.theme.colors.background};
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
`;

const SettingsHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  min-height: 60px;
  position: sticky;
  top: 0;
  z-index: 10;
`;

const HeaderTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
`;

const HeaderButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const SettingsContent = styled.div`
  flex: 1;
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const ThemeGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

const ThemeOption = styled.div`
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid ${props => props.$isActive ? props.theme.colors.primary : 'transparent'};
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.05);
  }
`;

const ThemePreview = styled.div`
  height: 100px;
  display: flex;
  flex-direction: column;
`;

const ThemeHeader = styled.div`
  height: 30%;
  background-color: ${props => props.color};
`;

const ThemeBody = styled.div`
  height: 70%;
  background-color: ${props => props.color};
  display: flex;
`;

const ThemeSidebar = styled.div`
  width: 30%;
  background-color: ${props => props.color};
`;

const ThemeChat = styled.div`
  width: 70%;
  background-color: ${props => props.color};
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 4px;
`;

const ThemeMessage = styled.div`
  width: 60%;
  height: 10px;
  border-radius: 4px;
  background-color: ${props => props.color};
  align-self: ${props => props.$sent ? 'flex-end' : 'flex-start'};
  margin-bottom: 4px;
`;

const ThemeName = styled.div`
  padding: 8px;
  text-align: center;
  font-size: 0.9rem;
  color: ${props => props.theme.colors.text};
  background-color: ${props => props.theme.colors.secondary};
`;

const CustomizationSection = styled.div`
  margin-top: 24px;
`;

const CustomOption = styled.div`
  margin-bottom: 16px;
`;

const CustomLabel = styled.label`
  display: block;
  margin-bottom: 8px;
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
`;

const CustomInput = styled.input`
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid ${props => props.theme.colors.border};
  background-color: ${props => props.theme.colors.secondary};
  color: ${props => props.theme.colors.text};
`;

const DarkModeToggle = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 8px;
  margin-bottom: 20px;
`;

const ToggleLabel = styled.div`
  display: flex;
  align-items: center;
  color: ${props => props.theme.colors.text};
  font-weight: 500;

  svg {
    margin-right: 10px;
    color: ${props => props.$isDark ? '#9b59b6' : '#f39c12'};
  }
`;

const ToggleSwitch = styled.label`
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;

  input {
    opacity: 0;
    width: 0;
    height: 0;
  }
`;

const Slider = styled.span`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;

  &:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }

  input:checked + & {
    background-color: ${props => props.theme.colors.primary};
  }

  input:checked + &:before {
    transform: translateX(26px);
  }
`;

const ThemeSettings = ({ theme, onClose }) => {
  const { currentTheme, changeTheme, themes, toggleDarkMode, isDarkMode } = useTheme();

  return (
    <ThemeSettingsContainer theme={theme || currentTheme}>
      <SettingsHeader theme={currentTheme}>
        <HeaderButton onClick={onClose}>
          <FaArrowLeft />
        </HeaderButton>
        <HeaderTitle>Themes & Appearance</HeaderTitle>
        <HeaderButton onClick={() => {}}>
          {/* Placeholder for future action */}
        </HeaderButton>
      </SettingsHeader>

      <SettingsContent>
        <DarkModeToggle theme={currentTheme}>
          <ToggleLabel $isDark={isDarkMode} theme={currentTheme}>
            {isDarkMode ? <FaMoon /> : <FaSun />}
            {isDarkMode ? 'Dark Mode' : 'Light Mode'}
          </ToggleLabel>
          <ToggleSwitch>
          <input
            type="checkbox"
            checked={isDarkMode}
            onChange={toggleDarkMode}
          />
          <Slider theme={currentTheme} />
        </ToggleSwitch>
      </DarkModeToggle>

      <SectionTitle theme={currentTheme}>Choose Theme</SectionTitle>
      <ThemeGrid>
        {Object.entries(themes).map(([key, theme]) => (
          <ThemeOption
            key={key}
            onClick={() => changeTheme(key)}
            $isActive={currentTheme.name === theme.name}
            theme={currentTheme}
          >
            <ThemePreview>
              <ThemeHeader color={theme.colors.primary} />
              <ThemeBody color={theme.colors.background}>
                <ThemeSidebar color={theme.colors.secondary} />
                <ThemeChat color={theme.colors.chatBackground}>
                  <ThemeMessage color={theme.colors.receivedMessage} $sent={false} />
                  <ThemeMessage color={theme.colors.sentMessage} $sent={true} />
                </ThemeChat>
              </ThemeBody>
            </ThemePreview>
            <ThemeName theme={currentTheme}>{theme.name}</ThemeName>
          </ThemeOption>
        ))}
      </ThemeGrid>

      <CustomizationSection>
        <SectionTitle theme={currentTheme}>Chat Wallpaper</SectionTitle>
        <CustomOption>
          <CustomLabel theme={currentTheme}>Upload Custom Background</CustomLabel>
          <CustomInput
            type="file"
            accept="image/*"
            theme={currentTheme}
          />
        </CustomOption>
      </CustomizationSection>
      </SettingsContent>
    </ThemeSettingsContainer>
  );
};

export default ThemeSettings;
