import React, { useState, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import { FaTimes, FaChevronLeft, FaChevronRight } from 'react-icons/fa';

const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;

const ViewerContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  animation: ${fadeIn} 0.3s ease-in-out;
`;

const ViewerHeader = styled.div`
  display: flex;
  align-items: center;
  padding: 15px;
  color: white;
`;

const ProfileImage = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-image: ${props => props.$image ? `url(${props.$image})` : 'none'};
  background-size: cover;
  background-position: center;
  margin-right: 15px;
`;

const ProfileInfo = styled.div`
  flex: 1;
`;

const ProfileName = styled.div`
  font-weight: 500;
  font-size: 16px;
`;

const Timestamp = styled.div`
  font-size: 12px;
  opacity: 0.7;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
`;

const StoryContent = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
`;

const ProgressContainer = styled.div`
  display: flex;
  width: 100%;
  padding: 0 15px;
  position: absolute;
  top: 10px;
  gap: 5px;
`;

const ProgressBar = styled.div`
  height: 3px;
  flex: 1;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
`;

const Progress = styled.div`
  height: 100%;
  width: ${props => props.$progress}%;
  background-color: white;
  transition: width ${props => props.$duration}ms linear;
`;

const StoryImage = styled.img`
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
`;

const StoryText = styled.div`
  max-width: 80%;
  padding: 20px;
  border-radius: 10px;
  background-color: ${props => props.$backgroundColor || '#3498db'};
  color: white;
  font-size: 24px;
  text-align: center;
`;

const StoryCaption = styled.div`
  position: absolute;
  bottom: 50px;
  left: 0;
  width: 100%;
  padding: 0 20px;
  text-align: center;
  color: white;
  font-size: 16px;
`;

const NavigationButton = styled.button`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 20px;
  opacity: 0.7;
  transition: opacity 0.3s;
  
  &:hover {
    opacity: 1;
  }
  
  ${props => props.$position === 'left' ? 'left: 0;' : 'right: 0;'}
`;

const formatTimestamp = (timestamp) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
  
  if (diffInHours < 1) {
    return 'Just now';
  } else if (diffInHours === 1) {
    return '1 hour ago';
  } else if (diffInHours < 24) {
    return `${diffInHours} hours ago`;
  } else {
    return date.toLocaleString();
  }
};

const StatusViewer = ({ status, onClose, theme }) => {
  const [currentStoryIndex, setCurrentStoryIndex] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  
  const currentStory = status.stories[currentStoryIndex];
  const storyDuration = 5000; // 5 seconds per story
  
  useEffect(() => {
    if (isPaused) return;
    
    setProgress(0);
    
    const timer = setTimeout(() => {
      if (currentStoryIndex < status.stories.length - 1) {
        setCurrentStoryIndex(currentStoryIndex + 1);
      } else {
        onClose();
      }
    }, storyDuration);
    
    return () => clearTimeout(timer);
  }, [currentStoryIndex, status.stories.length, onClose, isPaused]);
  
  useEffect(() => {
    if (isPaused) return;
    
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + (100 / (storyDuration / 100));
      });
    }, 100);
    
    return () => clearInterval(progressInterval);
  }, [currentStoryIndex, isPaused]);
  
  const handlePrevStory = (e) => {
    e.stopPropagation();
    if (currentStoryIndex > 0) {
      setCurrentStoryIndex(currentStoryIndex - 1);
    }
  };
  
  const handleNextStory = (e) => {
    e.stopPropagation();
    if (currentStoryIndex < status.stories.length - 1) {
      setCurrentStoryIndex(currentStoryIndex + 1);
    } else {
      onClose();
    }
  };
  
  const handlePause = () => {
    setIsPaused(true);
  };
  
  const handleResume = () => {
    setIsPaused(false);
  };
  
  return (
    <ViewerContainer>
      <ViewerHeader>
        <ProfileImage $image={status.image} />
        <ProfileInfo>
          <ProfileName>{status.name}</ProfileName>
          <Timestamp>{formatTimestamp(currentStory.timestamp)}</Timestamp>
        </ProfileInfo>
        <CloseButton onClick={onClose}>
          <FaTimes />
        </CloseButton>
      </ViewerHeader>
      
      <ProgressContainer>
        {status.stories.map((story, index) => (
          <ProgressBar key={story.id}>
            <Progress 
              $progress={index === currentStoryIndex ? progress : (index < currentStoryIndex ? 100 : 0)}
              $duration={index === currentStoryIndex ? 100 : 0}
            />
          </ProgressBar>
        ))}
      </ProgressContainer>
      
      <StoryContent 
        onMouseDown={handlePause}
        onMouseUp={handleResume}
        onTouchStart={handlePause}
        onTouchEnd={handleResume}
      >
        {currentStory.type === 'image' ? (
          <StoryImage src={currentStory.content} alt="Story" />
        ) : (
          <StoryText $backgroundColor={currentStory.backgroundColor}>
            {currentStory.content}
          </StoryText>
        )}
        
        {currentStory.caption && (
          <StoryCaption>{currentStory.caption}</StoryCaption>
        )}
        
        {currentStoryIndex > 0 && (
          <NavigationButton 
            $position="left" 
            onClick={handlePrevStory}
          >
            <FaChevronLeft />
          </NavigationButton>
        )}
        
        <NavigationButton 
          $position="right" 
          onClick={handleNextStory}
        >
          <FaChevronRight />
        </NavigationButton>
      </StoryContent>
    </ViewerContainer>
  );
};

export default StatusViewer;
