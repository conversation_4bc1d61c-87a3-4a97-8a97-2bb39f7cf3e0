Stack trace:
Frame         Function      Args
0007FFFF9F20  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9F20, 0007FFFF8E20) msys-2.0.dll+0x1FE8E
0007FFFF9F20  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA1F8) msys-2.0.dll+0x67F9
0007FFFF9F20  000210046832 (000210286019, 0007FFFF9DD8, 0007FFFF9F20, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F20  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9F20  000210068E24 (0007FFFF9F30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA200  00021006A225 (0007FFFF9F30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD4F760000 ntdll.dll
7FFD4E070000 KERNEL32.DLL
7FFD4CD10000 KERNELBASE.dll
7FFD4EAC0000 USER32.dll
7FFD4CCE0000 win32u.dll
7FFD4D580000 GDI32.dll
7FFD4D180000 gdi32full.dll
7FFD4CB70000 msvcp_win.dll
7FFD4D2C0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD4E2E0000 advapi32.dll
7FFD4DF00000 msvcrt.dll
7FFD4DFB0000 sechost.dll
7FFD4D5B0000 RPCRT4.dll
7FFD4BE70000 CRYPTBASE.DLL
7FFD4D0E0000 bcryptPrimitives.dll
7FFD4F6E0000 IMM32.DLL
