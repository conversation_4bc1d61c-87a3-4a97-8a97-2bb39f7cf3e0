Stack trace:
Frame         Function      Args
0007FFFF9B80  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9B80, 0007FFFF8A80) msys-2.0.dll+0x1FE8E
0007FFFF9B80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9E58) msys-2.0.dll+0x67F9
0007FFFF9B80  000210046832 (000210286019, 0007FFFF9A38, 0007FFFF9B80, 000000000000) msys-2.0.dll+0x6832
0007FFFF9B80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9B80  000210068E24 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9E60  00021006A225 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB9EE40000 ntdll.dll
7FFB9D630000 KERNEL32.DLL
7FFB9C3B0000 KERNELBASE.dll
7FFB9E490000 USER32.dll
7FFB9CA50000 win32u.dll
7FFB9D420000 GDI32.dll
7FFB9CA80000 gdi32full.dll
7FFB9C040000 msvcp_win.dll
7FFB9C780000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB9D910000 advapi32.dll
7FFB9E8A0000 msvcrt.dll
7FFB9D9D0000 sechost.dll
7FFB9E950000 RPCRT4.dll
7FFB9B550000 CRYPTBASE.DLL
7FFB9CBC0000 bcryptPrimitives.dll
7FFB9CCC0000 IMM32.DLL
