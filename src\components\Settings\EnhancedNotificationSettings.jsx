import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaTimes, 
  FaBell,
  FaBellSlash,
  FaVolumeUp,
  FaVolumeMute,
  FaVibrate,
  FaMobile,
  FaDesktop,
  FaClock,
  FaUsers,
  FaUser,
  FaPhone,
  FaVideo,
  FaComment,
  FaHeart,
  FaStar,
  FaExclamationTriangle,
  FaCheckCircle,
  FaMusic,
  FaPlay,
  FaPause,
  FaChevronRight,
  FaLightbulb,
  FaMoon,
  FaSun
} from 'react-icons/fa';
import storage from '../../utils/storage';
import MobileHaptics from '../Mobile/MobileHaptics';

const NotificationContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${props => props.theme.colors.background};
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
`;

const NotificationHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  min-height: 60px;
  position: sticky;
  top: 0;
  z-index: 10;
`;

const HeaderTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
`;

const HeaderButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const NotificationContent = styled.div`
  flex: 1;
  padding: 0;
`;

const NotificationSection = styled.div`
  margin-bottom: 8px;
  background-color: ${props => props.theme.colors.background};
`;

const SectionHeader = styled.div`
  padding: 16px 16px 8px 16px;
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.primary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const NotificationItem = styled.div`
  display: flex;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
  
  &:last-child {
    border-bottom: none;
  }
`;

const NotificationIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  margin-right: 16px;
`;

const NotificationInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const NotificationTitle = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 2px;
`;

const NotificationSubtitle = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.secondaryText};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const NotificationAction = styled.div`
  display: flex;
  align-items: center;
  color: ${props => props.theme.colors.secondaryText};
`;

const Toggle = styled.div`
  width: 50px;
  height: 28px;
  border-radius: 14px;
  background-color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.border};
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &::after {
    content: '';
    position: absolute;
    top: 2px;
    left: ${props => props.$active ? '24px' : '2px'};
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
`;

const VolumeSlider = styled.input`
  width: 100px;
  height: 4px;
  border-radius: 2px;
  background: ${props => props.theme.colors.border};
  outline: none;
  margin: 0 12px;
  
  &::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: ${props => props.theme.colors.primary};
    cursor: pointer;
  }
  
  &::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: ${props => props.theme.colors.primary};
    cursor: pointer;
    border: none;
  }
`;

const PlayButton = styled.button`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    opacity: 0.8;
  }
`;

const QuietHoursContainer = styled.div`
  padding: 16px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 12px;
  margin: 16px;
`;

const QuietHoursTitle = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const TimeSelector = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 8px 0;
`;

const TimeInput = styled.input`
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 8px;
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 14px;
  outline: none;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
  }
`;

const EnhancedNotificationSettings = ({ theme, onClose }) => {
  const [notificationSettings, setNotificationSettings] = useState({
    enabled: true,
    sound: true,
    vibration: true,
    popup: true,
    led: true,
    volume: 80,
    messageTone: 'default',
    groupTone: 'default',
    callTone: 'default',
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '07:00'
    },
    priority: {
      messages: true,
      groups: true,
      calls: true,
      mentions: true
    },
    preview: {
      showSender: true,
      showMessage: true,
      showMedia: false
    }
  });

  const [isPlaying, setIsPlaying] = useState(null);

  useEffect(() => {
    loadNotificationSettings();
  }, []);

  const loadNotificationSettings = () => {
    const settings = storage.getSection('notifications') || {};
    setNotificationSettings(prev => ({ ...prev, ...settings }));
  };

  const updateNotificationSetting = (key, value) => {
    const newSettings = { ...notificationSettings, [key]: value };
    setNotificationSettings(newSettings);
    storage.updateSection('notifications', newSettings);
    MobileHaptics.light();
  };

  const updateNestedSetting = (parent, key, value) => {
    const newSettings = {
      ...notificationSettings,
      [parent]: {
        ...notificationSettings[parent],
        [key]: value
      }
    };
    setNotificationSettings(newSettings);
    storage.updateSection('notifications', newSettings);
    MobileHaptics.light();
  };

  const playTone = (toneType) => {
    setIsPlaying(toneType);
    MobileHaptics.medium();
    
    // Simulate playing tone
    setTimeout(() => {
      setIsPlaying(null);
    }, 2000);
  };

  return (
    <NotificationContainer theme={theme}>
      <NotificationHeader theme={theme}>
        <HeaderButton onClick={onClose}>
          <FaTimes />
        </HeaderButton>
        <HeaderTitle>Notifications</HeaderTitle>
        <HeaderButton>
          <FaBell />
        </HeaderButton>
      </NotificationHeader>

      <NotificationContent>
        <NotificationSection theme={theme}>
          <SectionHeader theme={theme}>General</SectionHeader>
          
          <NotificationItem theme={theme}>
            <NotificationIcon color="#4caf50" theme={theme}>
              {notificationSettings.enabled ? <FaBell /> : <FaBellSlash />}
            </NotificationIcon>
            <NotificationInfo>
              <NotificationTitle theme={theme}>Notifications</NotificationTitle>
              <NotificationSubtitle theme={theme}>
                {notificationSettings.enabled ? 'Enabled' : 'Disabled'}
              </NotificationSubtitle>
            </NotificationInfo>
            <NotificationAction theme={theme}>
              <Toggle 
                $active={notificationSettings.enabled}
                onClick={() => updateNotificationSetting('enabled', !notificationSettings.enabled)}
                theme={theme}
              />
            </NotificationAction>
          </NotificationItem>

          <NotificationItem theme={theme}>
            <NotificationIcon color="#2196f3" theme={theme}>
              {notificationSettings.sound ? <FaVolumeUp /> : <FaVolumeMute />}
            </NotificationIcon>
            <NotificationInfo>
              <NotificationTitle theme={theme}>Sound</NotificationTitle>
              <NotificationSubtitle theme={theme}>
                Volume: {notificationSettings.volume}%
              </NotificationSubtitle>
            </NotificationInfo>
            <NotificationAction theme={theme}>
              <VolumeSlider
                type="range"
                min="0"
                max="100"
                value={notificationSettings.volume}
                onChange={(e) => updateNotificationSetting('volume', e.target.value)}
                theme={theme}
              />
              <Toggle 
                $active={notificationSettings.sound}
                onClick={() => updateNotificationSetting('sound', !notificationSettings.sound)}
                theme={theme}
              />
            </NotificationAction>
          </NotificationItem>

          <NotificationItem theme={theme}>
            <NotificationIcon color="#9c27b0" theme={theme}>
              <FaVibrate />
            </NotificationIcon>
            <NotificationInfo>
              <NotificationTitle theme={theme}>Vibration</NotificationTitle>
              <NotificationSubtitle theme={theme}>
                {notificationSettings.vibration ? 'Enabled' : 'Disabled'}
              </NotificationSubtitle>
            </NotificationInfo>
            <NotificationAction theme={theme}>
              <Toggle 
                $active={notificationSettings.vibration}
                onClick={() => updateNotificationSetting('vibration', !notificationSettings.vibration)}
                theme={theme}
              />
            </NotificationAction>
          </NotificationItem>
        </NotificationSection>

        <NotificationSection theme={theme}>
          <SectionHeader theme={theme}>Notification Tones</SectionHeader>
          
          <NotificationItem theme={theme}>
            <NotificationIcon color="#25d366" theme={theme}>
              <FaComment />
            </NotificationIcon>
            <NotificationInfo>
              <NotificationTitle theme={theme}>Message Tone</NotificationTitle>
              <NotificationSubtitle theme={theme}>
                {notificationSettings.messageTone}
              </NotificationSubtitle>
            </NotificationInfo>
            <NotificationAction theme={theme}>
              <PlayButton 
                onClick={() => playTone('message')}
                theme={theme}
              >
                {isPlaying === 'message' ? <FaPause /> : <FaPlay />}
              </PlayButton>
            </NotificationAction>
          </NotificationItem>

          <NotificationItem theme={theme}>
            <NotificationIcon color="#ff6b6b" theme={theme}>
              <FaUsers />
            </NotificationIcon>
            <NotificationInfo>
              <NotificationTitle theme={theme}>Group Tone</NotificationTitle>
              <NotificationSubtitle theme={theme}>
                {notificationSettings.groupTone}
              </NotificationSubtitle>
            </NotificationInfo>
            <NotificationAction theme={theme}>
              <PlayButton 
                onClick={() => playTone('group')}
                theme={theme}
              >
                {isPlaying === 'group' ? <FaPause /> : <FaPlay />}
              </PlayButton>
            </NotificationAction>
          </NotificationItem>

          <NotificationItem theme={theme}>
            <NotificationIcon color="#007bff" theme={theme}>
              <FaPhone />
            </NotificationIcon>
            <NotificationInfo>
              <NotificationTitle theme={theme}>Call Ringtone</NotificationTitle>
              <NotificationSubtitle theme={theme}>
                {notificationSettings.callTone}
              </NotificationSubtitle>
            </NotificationInfo>
            <NotificationAction theme={theme}>
              <PlayButton 
                onClick={() => playTone('call')}
                theme={theme}
              >
                {isPlaying === 'call' ? <FaPause /> : <FaPlay />}
              </PlayButton>
            </NotificationAction>
          </NotificationItem>
        </NotificationSection>

        <QuietHoursContainer theme={theme}>
          <QuietHoursTitle theme={theme}>
            <FaMoon /> Quiet Hours
          </QuietHoursTitle>
          
          <NotificationItem theme={theme} style={{ padding: '8px 0', border: 'none' }}>
            <NotificationInfo>
              <NotificationTitle theme={theme}>Enable Quiet Hours</NotificationTitle>
              <NotificationSubtitle theme={theme}>
                Mute notifications during specified hours
              </NotificationSubtitle>
            </NotificationInfo>
            <NotificationAction theme={theme}>
              <Toggle 
                $active={notificationSettings.quietHours.enabled}
                onClick={() => updateNestedSetting('quietHours', 'enabled', !notificationSettings.quietHours.enabled)}
                theme={theme}
              />
            </NotificationAction>
          </NotificationItem>

          {notificationSettings.quietHours.enabled && (
            <div>
              <TimeSelector>
                <span style={{ color: theme.colors.text, fontSize: '14px' }}>From:</span>
                <TimeInput
                  type="time"
                  value={notificationSettings.quietHours.start}
                  onChange={(e) => updateNestedSetting('quietHours', 'start', e.target.value)}
                  theme={theme}
                />
                <span style={{ color: theme.colors.text, fontSize: '14px' }}>To:</span>
                <TimeInput
                  type="time"
                  value={notificationSettings.quietHours.end}
                  onChange={(e) => updateNestedSetting('quietHours', 'end', e.target.value)}
                  theme={theme}
                />
              </TimeSelector>
            </div>
          )}
        </QuietHoursContainer>

        <NotificationSection theme={theme}>
          <SectionHeader theme={theme}>Priority Notifications</SectionHeader>
          
          <NotificationItem theme={theme}>
            <NotificationIcon color="#4caf50" theme={theme}>
              <FaComment />
            </NotificationIcon>
            <NotificationInfo>
              <NotificationTitle theme={theme}>Messages</NotificationTitle>
              <NotificationSubtitle theme={theme}>Show as priority</NotificationSubtitle>
            </NotificationInfo>
            <NotificationAction theme={theme}>
              <Toggle 
                $active={notificationSettings.priority.messages}
                onClick={() => updateNestedSetting('priority', 'messages', !notificationSettings.priority.messages)}
                theme={theme}
              />
            </NotificationAction>
          </NotificationItem>

          <NotificationItem theme={theme}>
            <NotificationIcon color="#ff9800" theme={theme}>
              <FaUsers />
            </NotificationIcon>
            <NotificationInfo>
              <NotificationTitle theme={theme}>Groups</NotificationTitle>
              <NotificationSubtitle theme={theme}>Show as priority</NotificationSubtitle>
            </NotificationInfo>
            <NotificationAction theme={theme}>
              <Toggle 
                $active={notificationSettings.priority.groups}
                onClick={() => updateNestedSetting('priority', 'groups', !notificationSettings.priority.groups)}
                theme={theme}
              />
            </NotificationAction>
          </NotificationItem>

          <NotificationItem theme={theme}>
            <NotificationIcon color="#2196f3" theme={theme}>
              <FaPhone />
            </NotificationIcon>
            <NotificationInfo>
              <NotificationTitle theme={theme}>Calls</NotificationTitle>
              <NotificationSubtitle theme={theme}>Show as priority</NotificationSubtitle>
            </NotificationInfo>
            <NotificationAction theme={theme}>
              <Toggle 
                $active={notificationSettings.priority.calls}
                onClick={() => updateNestedSetting('priority', 'calls', !notificationSettings.priority.calls)}
                theme={theme}
              />
            </NotificationAction>
          </NotificationItem>
        </NotificationSection>
      </NotificationContent>
    </NotificationContainer>
  );
};

export default EnhancedNotificationSettings;
