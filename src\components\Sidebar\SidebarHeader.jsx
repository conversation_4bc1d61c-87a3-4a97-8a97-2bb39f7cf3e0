import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import {
  FaEllipsisV,
  FaCommentAlt,
  FaCog,
  FaUserCircle,
  FaUsers,
  FaSignOutAlt,
  FaBars,
  FaSlidersH
} from 'react-icons/fa';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: ${props => props.theme.colors.secondary};
  height: 60px;
`;

const ProfileSection = styled.div`
  display: flex;
  align-items: center;
`;

const Avatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
  overflow: hidden;
  color: white;
  cursor: pointer;

  &:hover {
    opacity: 0.9;
  }
`;

const IconsSection = styled.div`
  display: flex;
  gap: 24px;
  color: ${props => props.theme.colors.icon};
  font-size: 1.2rem;
`;

const IconWrapper = styled.div`
  cursor: pointer;

  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const DropdownMenu = styled.div`
  position: absolute;
  top: 60px;
  right: 10px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 200px;
  z-index: 100;
  overflow: hidden;
  display: ${props => props.$isOpen ? 'block' : 'none'};
`;

const MenuItem = styled.div`
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: ${props => props.theme.colors.text};
  cursor: pointer;

  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }

  svg {
    color: ${props => props.$danger ? '#e74c3c' : props.theme.colors.icon};
  }
`;

const SidebarHeader = ({ onSettingsClick, onProfileClick, onLogout, onMenuClick, onQuickSettingsClick }) => {
  const { currentTheme } = useTheme();
  const { currentUser } = useAuth();
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setShowMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleMenuToggle = () => {
    setShowMenu(!showMenu);
  };

  const handleProfileClick = () => {
    if (onProfileClick) {
      onProfileClick();
      setShowMenu(false);
    }
  };

  const handleLogout = () => {
    if (onLogout) {
      onLogout();
      setShowMenu(false);
    }
  };

  return (
    <HeaderContainer theme={currentTheme}>
      <ProfileSection>
        <Avatar theme={currentTheme} onClick={handleProfileClick}>
          <FaUserCircle />
        </Avatar>
      </ProfileSection>
      <IconsSection theme={currentTheme}>
        <IconWrapper theme={currentTheme} onClick={onMenuClick}>
          <FaBars />
        </IconWrapper>
        <IconWrapper theme={currentTheme}>
          <FaUsers />
        </IconWrapper>
        <IconWrapper theme={currentTheme} onClick={onQuickSettingsClick}>
          <FaSlidersH />
        </IconWrapper>
        <IconWrapper theme={currentTheme} onClick={onSettingsClick}>
          <FaCog />
        </IconWrapper>
        <IconWrapper theme={currentTheme} onClick={handleMenuToggle}>
          <FaEllipsisV />
        </IconWrapper>
      </IconsSection>

      <DropdownMenu $isOpen={showMenu} theme={currentTheme} ref={menuRef}>
        <MenuItem theme={currentTheme} onClick={handleProfileClick}>
          <FaUserCircle />
          <span>Profile</span>
        </MenuItem>
        <MenuItem theme={currentTheme} $danger onClick={handleLogout}>
          <FaSignOutAlt />
          <span>Logout</span>
        </MenuItem>
      </DropdownMenu>
    </HeaderContainer>
  );
};

export default SidebarHeader;
