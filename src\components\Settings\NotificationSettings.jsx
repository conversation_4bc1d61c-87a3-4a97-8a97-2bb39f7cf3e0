import React, { useState } from 'react';
import styled from 'styled-components';
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON><PERSON><PERSON>, FaBell, FaVolumeUp, FaVolumeMute, FaRegClock, FaMoon } from 'react-icons/fa';

const NotificationSettingsContainer = styled.div`
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const SettingsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const SettingItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const SettingInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const IconWrapper = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary}20;
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    color: ${props => props.color || props.theme.colors.primary};
    font-size: 18px;
  }
`;

const SettingText = styled.div`
  display: flex;
  flex-direction: column;
`;

const SettingName = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 0.95rem;
`;

const SettingDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 0.8rem;
  margin-top: 4px;
`;

const ToggleButton = styled.div`
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
  font-size: 1.5rem;
  cursor: pointer;
`;

const SelectWrapper = styled.div`
  position: relative;
`;

const Select = styled.select`
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${props => props.theme.colors.border};
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
  cursor: pointer;
  appearance: none;
  padding-right: 30px;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const SelectArrow = styled.div`
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  
  &::before {
    content: '';
    display: block;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid ${props => props.theme.colors.text};
  }
`;

const NotificationSettings = ({ theme }) => {
  const [settings, setSettings] = useState({
    showNotifications: true,
    sound: true,
    vibration: true,
    useHighPriority: false,
    showPreview: true,
    doNotDisturb: false,
    lightIndicator: true
  });
  
  const [toneSelection, setToneSelection] = useState('default');
  
  const toggleSetting = (setting) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };
  
  return (
    <NotificationSettingsContainer theme={theme}>
      <SectionTitle theme={theme}>Notification Settings</SectionTitle>
      
      <SettingsList>
        <SettingItem theme={theme} onClick={() => toggleSetting('showNotifications')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#E91E63">
              <FaBell />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Show notifications</SettingName>
              <SettingDescription theme={theme}>
                Display notifications when you receive messages
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.showNotifications} 
            theme={theme}
          >
            {settings.showNotifications ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('sound')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#2196F3">
              {settings.sound ? <FaVolumeUp /> : <FaVolumeMute />}
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Notification sound</SettingName>
              <SettingDescription theme={theme}>
                Play sound for new messages
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.sound} 
            theme={theme}
          >
            {settings.sound ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        {settings.sound && (
          <SettingItem theme={theme}>
            <SettingInfo>
              <IconWrapper theme={theme} color="#4CAF50">
                <FaVolumeUp />
              </IconWrapper>
              <SettingText>
                <SettingName theme={theme}>Notification tone</SettingName>
                <SettingDescription theme={theme}>
                  Choose your notification sound
                </SettingDescription>
              </SettingText>
            </SettingInfo>
            <SelectWrapper>
              <Select 
                value={toneSelection}
                onChange={(e) => setToneSelection(e.target.value)}
                theme={theme}
              >
                <option value="default">Default</option>
                <option value="chime">Chime</option>
                <option value="bell">Bell</option>
                <option value="digital">Digital</option>
                <option value="classic">Classic</option>
              </Select>
              <SelectArrow theme={theme} />
            </SelectWrapper>
          </SettingItem>
        )}
        
        <SettingItem theme={theme} onClick={() => toggleSetting('doNotDisturb')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#9C27B0">
              <FaMoon />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Do not disturb</SettingName>
              <SettingDescription theme={theme}>
                Mute notifications during specific hours
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.doNotDisturb} 
            theme={theme}
          >
            {settings.doNotDisturb ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        {settings.doNotDisturb && (
          <SettingItem theme={theme}>
            <SettingInfo>
              <IconWrapper theme={theme} color="#FF9800">
                <FaRegClock />
              </IconWrapper>
              <SettingText>
                <SettingName theme={theme}>Quiet hours</SettingName>
                <SettingDescription theme={theme}>
                  Set your do not disturb schedule
                </SettingDescription>
              </SettingText>
            </SettingInfo>
            <div style={{ display: 'flex', gap: '8px' }}>
              <Select theme={theme} defaultValue="22:00">
                <option value="20:00">8:00 PM</option>
                <option value="21:00">9:00 PM</option>
                <option value="22:00">10:00 PM</option>
                <option value="23:00">11:00 PM</option>
                <option value="00:00">12:00 AM</option>
              </Select>
              <span style={{ color: theme.colors.text }}>to</span>
              <Select theme={theme} defaultValue="07:00">
                <option value="05:00">5:00 AM</option>
                <option value="06:00">6:00 AM</option>
                <option value="07:00">7:00 AM</option>
                <option value="08:00">8:00 AM</option>
                <option value="09:00">9:00 AM</option>
              </Select>
            </div>
          </SettingItem>
        )}
        
        <SettingItem theme={theme} onClick={() => toggleSetting('showPreview')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#607D8B">
              <FaBell />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Message preview</SettingName>
              <SettingDescription theme={theme}>
                Show message content in notifications
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.showPreview} 
            theme={theme}
          >
            {settings.showPreview ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
      </SettingsList>
    </NotificationSettingsContainer>
  );
};

export default NotificationSettings;
