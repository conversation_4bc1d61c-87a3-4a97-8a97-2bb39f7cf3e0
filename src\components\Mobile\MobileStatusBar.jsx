import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaWifi, 
  FaBatteryFull, 
  FaBatteryHalf, 
  FaBatteryQuarter, 
  FaSignal,
  FaBluetooth,
  FaVolumeUp,
  FaVolumeMute
} from 'react-icons/fa';

const StatusBarContainer = styled.div`
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 24px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  font-size: 12px;
  font-weight: 500;
  z-index: 1000;
  padding: 0 16px;
  align-items: center;
  justify-content: space-between;
  
  @media (max-width: 768px) {
    display: flex;
  }
`;

const LeftSection = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
`;

const TimeDisplay = styled.div`
  font-weight: 600;
`;

const IconWrapper = styled.div`
  display: flex;
  align-items: center;
  font-size: 10px;
`;

const BatteryPercentage = styled.span`
  font-size: 10px;
  margin-left: 2px;
`;

const MobileStatusBar = ({ theme }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [batteryLevel, setBatteryLevel] = useState(85);
  const [isCharging, setIsCharging] = useState(false);
  const [networkStrength, setNetworkStrength] = useState(4);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // Simulate battery changes
    const batteryTimer = setInterval(() => {
      setBatteryLevel(prev => {
        const change = Math.random() > 0.5 ? 1 : -1;
        return Math.max(10, Math.min(100, prev + change));
      });
    }, 30000);

    return () => {
      clearInterval(timer);
      clearInterval(batteryTimer);
    };
  }, []);

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  const getBatteryIcon = () => {
    if (batteryLevel > 75) return <FaBatteryFull />;
    if (batteryLevel > 25) return <FaBatteryHalf />;
    return <FaBatteryQuarter />;
  };

  const getSignalBars = () => {
    const bars = [];
    for (let i = 0; i < 4; i++) {
      bars.push(
        <div
          key={i}
          style={{
            width: '2px',
            height: `${3 + i * 2}px`,
            backgroundColor: i < networkStrength ? 'white' : 'rgba(255,255,255,0.3)',
            marginRight: '1px'
          }}
        />
      );
    }
    return bars;
  };

  return (
    <StatusBarContainer theme={theme}>
      <LeftSection>
        <TimeDisplay>{formatTime(currentTime)}</TimeDisplay>
      </LeftSection>
      
      <RightSection>
        <IconWrapper>
          <div style={{ display: 'flex', alignItems: 'end' }}>
            {getSignalBars()}
          </div>
        </IconWrapper>
        
        <IconWrapper>
          <FaWifi />
        </IconWrapper>
        
        <IconWrapper>
          <FaBluetooth />
        </IconWrapper>
        
        <IconWrapper>
          {getBatteryIcon()}
          <BatteryPercentage>{batteryLevel}%</BatteryPercentage>
        </IconWrapper>
      </RightSection>
    </StatusBarContainer>
  );
};

export default MobileStatusBar;
