import React, { useState } from 'react';
import styled from 'styled-components';
import { FaPlus } from 'react-icons/fa';
import StatusViewer from './StatusViewer';

const StatusContainer = styled.div`
  display: flex;
  padding: 15px 10px;
  overflow-x: auto;
  background-color: ${props => props.theme.colors.secondary};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  
  &::-webkit-scrollbar {
    display: none;
  }
  
  -ms-overflow-style: none;
  scrollbar-width: none;
`;

const StatusItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 15px;
  cursor: pointer;
`;

const StatusCircle = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  padding: 2px;
  background: ${props => props.$hasStatus 
    ? `linear-gradient(to right, ${props.theme.colors.primary}, #8e44ad)` 
    : props.theme.colors.border};
  margin-bottom: 5px;
`;

const StatusImage = styled.div`
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.background};
  background-image: ${props => props.$image ? `url(${props.$image})` : 'none'};
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.theme.colors.secondaryText};
  font-size: 24px;
  overflow: hidden;
`;

const StatusName = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.secondaryText};
  max-width: 70px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
`;

const MyStatusText = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.primary};
  font-weight: 500;
`;

// Mock data for statuses
const mockStatuses = [
  {
    id: 'my-status',
    name: 'My Status',
    image: null,
    hasStatus: false,
    isMyStatus: true,
    stories: []
  },
  {
    id: '1',
    name: 'John Doe',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
    hasStatus: true,
    stories: [
      {
        id: '1-1',
        type: 'image',
        content: 'https://source.unsplash.com/random/800x1200?nature',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        caption: 'Beautiful day!'
      }
    ]
  },
  {
    id: '2',
    name: 'Jane Smith',
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
    hasStatus: true,
    stories: [
      {
        id: '2-1',
        type: 'text',
        content: 'Just landed in Paris!',
        backgroundColor: '#3498db',
        timestamp: new Date(Date.now() - 7200000).toISOString()
      },
      {
        id: '2-2',
        type: 'image',
        content: 'https://source.unsplash.com/random/800x1200?paris',
        timestamp: new Date(Date.now() - 5400000).toISOString()
      }
    ]
  },
  {
    id: '3',
    name: 'Mike Johnson',
    image: 'https://randomuser.me/api/portraits/men/67.jpg',
    hasStatus: true,
    stories: [
      {
        id: '3-1',
        type: 'image',
        content: 'https://source.unsplash.com/random/800x1200?coffee',
        timestamp: new Date(Date.now() - 1800000).toISOString(),
        caption: 'Coffee time ☕'
      }
    ]
  },
  {
    id: '4',
    name: 'Sarah Williams',
    image: 'https://randomuser.me/api/portraits/women/63.jpg',
    hasStatus: true,
    stories: [
      {
        id: '4-1',
        type: 'text',
        content: 'New job, new beginnings! 🎉',
        backgroundColor: '#e74c3c',
        timestamp: new Date(Date.now() - 10800000).toISOString()
      }
    ]
  },
  {
    id: '5',
    name: 'David Brown',
    image: 'https://randomuser.me/api/portraits/men/22.jpg',
    hasStatus: true,
    stories: [
      {
        id: '5-1',
        type: 'image',
        content: 'https://source.unsplash.com/random/800x1200?concert',
        timestamp: new Date(Date.now() - 14400000).toISOString(),
        caption: 'Amazing concert last night!'
      }
    ]
  }
];

const StatusBar = ({ theme }) => {
  const [statuses] = useState(mockStatuses);
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [showStatusViewer, setShowStatusViewer] = useState(false);
  
  const handleStatusClick = (status) => {
    if (status.isMyStatus && !status.hasStatus) {
      // Handle adding a new status
      console.log('Add new status');
      return;
    }
    
    if (status.stories.length > 0) {
      setSelectedStatus(status);
      setShowStatusViewer(true);
    }
  };
  
  const closeStatusViewer = () => {
    setShowStatusViewer(false);
    setSelectedStatus(null);
  };
  
  return (
    <>
      <StatusContainer theme={theme}>
        {statuses.map((status) => (
          <StatusItem key={status.id} onClick={() => handleStatusClick(status)}>
            <StatusCircle $hasStatus={status.hasStatus} theme={theme}>
              <StatusImage 
                $image={status.image} 
                theme={theme}
              >
                {status.isMyStatus && !status.image && <FaPlus />}
              </StatusImage>
            </StatusCircle>
            {status.isMyStatus ? (
              <MyStatusText theme={theme}>
                {status.hasStatus ? 'My Status' : 'Add Status'}
              </MyStatusText>
            ) : (
              <StatusName theme={theme}>{status.name}</StatusName>
            )}
          </StatusItem>
        ))}
      </StatusContainer>
      
      {showStatusViewer && selectedStatus && (
        <StatusViewer 
          status={selectedStatus} 
          onClose={closeStatusViewer}
          theme={theme}
        />
      )}
    </>
  );
};

export default StatusBar;
