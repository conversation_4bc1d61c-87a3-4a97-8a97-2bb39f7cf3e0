import React, { useState } from 'react';
import styled from 'styled-components';
import { FaEnvelope, <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaEyeSlash } from 'react-icons/fa';
import Logo from '../../assets/logo';

const LoginContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
  background-color: ${props => props.theme.colors.background};
  padding: 20px;
`;

const LoginForm = styled.form`
  width: 100%;
  max-width: 400px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-top: 30px;
`;

const LogoContainer = styled.div`
  margin-bottom: 30px;
`;

const Title = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: 20px;
  text-align: center;
`;

const InputGroup = styled.div`
  position: relative;
  margin-bottom: 20px;
`;

const Input = styled.input`
  width: 100%;
  padding: 12px 15px 12px 45px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 5px;
  font-size: 16px;
  color: ${props => props.theme.colors.text};
  background-color: ${props => props.theme.colors.background};
  transition: border-color 0.3s;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }

  &::placeholder {
    color: ${props => props.theme.colors.secondaryText};
  }
`;

const InputIcon = styled.div`
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.secondaryText};
  font-size: 18px;
`;

const PasswordToggle = styled.div`
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.secondaryText};
  font-size: 18px;
  cursor: pointer;

  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const Button = styled.button`
  width: 100%;
  padding: 12px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 10px;

  &:hover {
    background-color: ${props => {
      const color = props.theme.colors.primary;
      // Darken the color by 10%
      return color.startsWith('#')
        ? '#' + color.substring(1).match(/.{2}/g).map(c => {
            const num = parseInt(c, 16);
            return Math.max(0, num - 25).toString(16).padStart(2, '0');
          }).join('')
        : color;
    }};
  }

  &:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
`;

const ForgotPassword = styled.div`
  text-align: right;
  margin-bottom: 20px;

  a {
    color: ${props => props.theme.colors.primary};
    text-decoration: none;
    font-size: 14px;

    &:hover {
      text-decoration: underline;
    }
  }
`;

const Divider = styled.div`
  display: flex;
  align-items: center;
  margin: 20px 0;

  &::before, &::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: ${props => props.theme.colors.border};
  }

  span {
    padding: 0 10px;
    color: ${props => props.theme.colors.secondaryText};
    font-size: 14px;
  }
`;

const SocialLogin = styled.div`
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
`;

const SocialButton = styled.button`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid ${props => props.theme.colors.border};
  background-color: ${props => props.theme.colors.background};
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }

  svg {
    font-size: 20px;
    color: ${props => props.color || props.theme.colors.text};
  }
`;

const SignupLink = styled.div`
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  color: ${props => props.theme.colors.secondaryText};

  a {
    color: ${props => props.theme.colors.primary};
    text-decoration: none;
    font-weight: 600;

    &:hover {
      text-decoration: underline;
    }
  }
`;

const ErrorMessage = styled.div`
  color: #e74c3c;
  font-size: 14px;
  margin-top: 5px;
  margin-bottom: 15px;
`;

const Login = ({ onLogin, onSignupClick, theme }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();

    // Simple validation
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    if (!email.includes('@')) {
      setError('Please enter a valid email address');
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }

    setError('');
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);

      // For demo purposes, accept any valid format
      onLogin({ email, name: email.split('@')[0] });
    }, 1500);
  };

  return (
    <LoginContainer theme={theme}>
      <LogoContainer theme={theme}>
        <Logo vertical large theme={theme} />
      </LogoContainer>

      <LoginForm onSubmit={handleSubmit} theme={theme}>
        <Title theme={theme}>Welcome Back</Title>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <InputGroup>
          <InputIcon theme={theme}>
            <FaEnvelope />
          </InputIcon>
          <Input
            type="email"
            placeholder="Email Address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            theme={theme}
          />
        </InputGroup>

        <InputGroup>
          <InputIcon theme={theme}>
            <FaLock />
          </InputIcon>
          <Input
            type={showPassword ? 'text' : 'password'}
            placeholder="Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            theme={theme}
          />
          <PasswordToggle
            onClick={() => setShowPassword(!showPassword)}
            theme={theme}
          >
            {showPassword ? <FaEyeSlash /> : <FaEye />}
          </PasswordToggle>
        </InputGroup>

        <ForgotPassword theme={theme}>
          <a href="#forgot-password">Forgot Password?</a>
        </ForgotPassword>

        <Button
          type="submit"
          disabled={isLoading}
          theme={theme}
        >
          {isLoading ? 'Logging in...' : 'Login'}
        </Button>

        <Divider theme={theme}>
          <span>or</span>
        </Divider>

        <SignupLink theme={theme}>
          Don't have an account? <a href="#signup" onClick={(e) => {
            e.preventDefault();
            onSignupClick();
          }}>Sign Up</a>
        </SignupLink>
      </LoginForm>
    </LoginContainer>
  );
};

export default Login;
