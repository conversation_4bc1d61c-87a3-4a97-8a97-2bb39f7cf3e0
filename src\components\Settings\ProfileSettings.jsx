import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import {
  FaTimes,
  FaCamera,
  FaEdit,
  FaCheck,
  FaUser,
  FaPhone,
  FaEnvelope,
  FaCalendar,
  FaMapMarkerAlt,
  FaGlobe,
  FaHeart,
  FaBriefcase,
  FaGraduationCap,
  FaImage,
  FaTrash,
  FaSave,
  FaEye,
  FaEyeSlash
} from 'react-icons/fa';
import storage from '../../utils/storage';
import mediaAccess from '../../utils/mediaAccess';
import MobileHaptics from '../Mobile/MobileHaptics';

const ProfileContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${props => props.theme.colors.background};
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
`;

const ProfileHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  min-height: 60px;
  position: sticky;
  top: 0;
  z-index: 10;
`;

const HeaderTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
`;

const HeaderButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const ProfileContent = styled.div`
  flex: 1;
  padding: 0;
`;

const AvatarSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 16px;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary}, ${props => props.theme.colors.primaryDark || props.theme.colors.primary});
  color: white;
`;

const AvatarContainer = styled.div`
  position: relative;
  margin-bottom: 16px;
`;

const Avatar = styled.div`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 48px;
  font-weight: 600;
  overflow: hidden;
  border: 4px solid rgba(255, 255, 255, 0.3);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const CameraButton = styled.button`
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  border: 3px solid white;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;

  &:hover {
    background-color: ${props => props.theme.colors.primaryDark || props.theme.colors.primary};
  }
`;

const AvatarActions = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 16px;
`;

const AvatarActionButton = styled.button`
  padding: 8px 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const FormSection = styled.div`
  padding: 24px 16px;
`;

const FormGroup = styled.div`
  margin-bottom: 24px;
`;

const FormLabel = styled.label`
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.primary};
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const FormInput = styled.input`
  width: 100%;
  padding: 16px;
  border: 2px solid ${props => props.theme.colors.border};
  border-radius: 12px;
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 16px;
  outline: none;
  transition: all 0.3s ease;

  &:focus {
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
  }

  &::placeholder {
    color: ${props => props.theme.colors.secondaryText};
  }
`;

const FormTextarea = styled.textarea`
  width: 100%;
  padding: 16px;
  border: 2px solid ${props => props.theme.colors.border};
  border-radius: 12px;
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 16px;
  outline: none;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
  transition: all 0.3s ease;

  &:focus {
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
  }

  &::placeholder {
    color: ${props => props.theme.colors.secondaryText};
  }
`;

const InputIcon = styled.div`
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.secondaryText};
  font-size: 18px;
`;

const InputContainer = styled.div`
  position: relative;

  ${FormInput}, ${FormTextarea} {
    padding-left: ${props => props.$hasIcon ? '52px' : '16px'};
  }
`;

const CharacterCount = styled.div`
  text-align: right;
  font-size: 12px;
  color: ${props => props.theme.colors.secondaryText};
  margin-top: 4px;
`;

const PrivacySection = styled.div`
  background-color: ${props => props.theme.colors.secondary};
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 16px;
`;

const PrivacyTitle = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PrivacyOption = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid ${props => props.theme.colors.border};

  &:last-child {
    border-bottom: none;
  }
`;

const PrivacyLabel = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.text};
`;

const PrivacyToggle = styled.div`
  width: 40px;
  height: 22px;
  border-radius: 11px;
  background-color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.border};
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;

  &::after {
    content: '';
    position: absolute;
    top: 2px;
    left: ${props => props.$active ? '20px' : '2px'};
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
`;

const SaveButton = styled.button`
  width: 100%;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 24px;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.primaryDark || props.theme.colors.primary};
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
  }

  &:disabled {
    background-color: ${props => props.theme.colors.border};
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const EnhancedProfileSettings = ({ theme, onClose }) => {
  const [profile, setProfile] = useState({
    name: '',
    username: '',
    bio: '',
    email: '',
    phone: '',
    birthday: '',
    location: '',
    website: '',
    occupation: '',
    education: '',
    relationship: '',
    avatar: null
  });

  const [privacy, setPrivacy] = useState({
    showEmail: true,
    showPhone: false,
    showBirthday: true,
    showLocation: false,
    showWebsite: true,
    showOccupation: true,
    showEducation: true,
    showRelationship: false
  });

  const [isEditing, setIsEditing] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const fileInputRef = useRef(null);

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = () => {
    const userData = storage.getSection('user');
    if (userData) {
      setProfile(prev => ({ ...prev, ...userData }));
    }

    const privacyData = storage.getSection('privacy');
    if (privacyData) {
      setPrivacy(prev => ({ ...prev, ...privacyData }));
    }
  };

  const handleInputChange = (field, value) => {
    setProfile(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
    MobileHaptics.light();
  };

  const handlePrivacyChange = (field, value) => {
    setPrivacy(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
    MobileHaptics.light();
  };

  const handleAvatarUpload = async () => {
    try {
      const files = await mediaAccess.pickImages(false);
      if (files.length > 0) {
        const file = files[0];
        const mediaData = await mediaAccess.saveMediaToStorage(file, 'image');
        setProfile(prev => ({ ...prev, avatar: mediaData.url }));
        setHasChanges(true);
        MobileHaptics.success();
      }
    } catch (error) {
      console.error('Error uploading avatar:', error);
    }
  };

  const handleCameraCapture = () => {
    // This would open the camera component
    console.log('Open camera for avatar');
    MobileHaptics.medium();
  };

  const handleRemoveAvatar = () => {
    setProfile(prev => ({ ...prev, avatar: null }));
    setHasChanges(true);
    MobileHaptics.medium();
  };

  const handleSave = () => {
    storage.updateSection('user', profile);
    storage.updateSection('privacy', privacy);
    setHasChanges(false);
    setIsEditing(false);
    MobileHaptics.success();
  };

  const getInitials = (name) => {
    return name?.split(' ').map(n => n[0]).join('').toUpperCase() || '?';
  };

  const generateUsername = (name) => {
    if (!name) return '';
    const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '');
    const randomNum = Math.floor(Math.random() * 1000);
    return `@${cleanName}${randomNum}`;
  };

  const handleNameChange = (value) => {
    handleInputChange('name', value);
    if (!profile.username || profile.username === generateUsername(profile.name)) {
      handleInputChange('username', generateUsername(value));
    }
  };

  return (
    <ProfileContainer theme={theme}>
      <ProfileHeader theme={theme}>
        <HeaderButton onClick={onClose}>
          <FaTimes />
        </HeaderButton>
        <HeaderTitle>Profile</HeaderTitle>
        <HeaderButton onClick={() => setIsEditing(!isEditing)}>
          {isEditing ? <FaCheck /> : <FaEdit />}
        </HeaderButton>
      </ProfileHeader>

      <ProfileContent>
        <AvatarSection theme={theme}>
          <AvatarContainer>
            <Avatar>
              {profile.avatar ? (
                <img src={profile.avatar} alt={profile.name} />
              ) : (
                getInitials(profile.name)
              )}
            </Avatar>
            {isEditing && (
              <CameraButton onClick={handleAvatarUpload} theme={theme}>
                <FaCamera />
              </CameraButton>
            )}
          </AvatarContainer>

          {isEditing && (
            <AvatarActions>
              <AvatarActionButton onClick={handleAvatarUpload}>
                <FaImage /> Gallery
              </AvatarActionButton>
              <AvatarActionButton onClick={handleCameraCapture}>
                <FaCamera /> Camera
              </AvatarActionButton>
              {profile.avatar && (
                <AvatarActionButton onClick={handleRemoveAvatar}>
                  <FaTrash /> Remove
                </AvatarActionButton>
              )}
            </AvatarActions>
          )}
        </AvatarSection>

        <FormSection>
          <FormGroup>
            <FormLabel theme={theme}>Display Name</FormLabel>
            <InputContainer $hasIcon>
              <InputIcon theme={theme}>
                <FaUser />
              </InputIcon>
              <FormInput
                type="text"
                placeholder="Enter your name"
                value={profile.name}
                onChange={(e) => handleNameChange(e.target.value)}
                disabled={!isEditing}
                theme={theme}
              />
            </InputContainer>
          </FormGroup>

          <FormGroup>
            <FormLabel theme={theme}>Username</FormLabel>
            <InputContainer $hasIcon>
              <InputIcon theme={theme}>@</InputIcon>
              <FormInput
                type="text"
                placeholder="@username"
                value={profile.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                disabled={!isEditing}
                theme={theme}
              />
            </InputContainer>
          </FormGroup>

          <FormGroup>
            <FormLabel theme={theme}>Bio</FormLabel>
            <InputContainer>
              <FormTextarea
                placeholder="Tell us about yourself..."
                value={profile.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                disabled={!isEditing}
                theme={theme}
                maxLength={150}
              />
              <CharacterCount theme={theme}>
                {profile.bio?.length || 0}/150
              </CharacterCount>
            </InputContainer>
          </FormGroup>

          <FormGroup>
            <FormLabel theme={theme}>Email</FormLabel>
            <InputContainer $hasIcon>
              <InputIcon theme={theme}>
                <FaEnvelope />
              </InputIcon>
              <FormInput
                type="email"
                placeholder="<EMAIL>"
                value={profile.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                disabled={!isEditing}
                theme={theme}
              />
            </InputContainer>
          </FormGroup>

          <FormGroup>
            <FormLabel theme={theme}>Phone</FormLabel>
            <InputContainer $hasIcon>
              <InputIcon theme={theme}>
                <FaPhone />
              </InputIcon>
              <FormInput
                type="tel"
                placeholder="****** 567 8900"
                value={profile.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                disabled={!isEditing}
                theme={theme}
              />
            </InputContainer>
          </FormGroup>

          <FormGroup>
            <FormLabel theme={theme}>Birthday</FormLabel>
            <InputContainer $hasIcon>
              <InputIcon theme={theme}>
                <FaCalendar />
              </InputIcon>
              <FormInput
                type="date"
                value={profile.birthday}
                onChange={(e) => handleInputChange('birthday', e.target.value)}
                disabled={!isEditing}
                theme={theme}
              />
            </InputContainer>
          </FormGroup>

          <FormGroup>
            <FormLabel theme={theme}>Location</FormLabel>
            <InputContainer $hasIcon>
              <InputIcon theme={theme}>
                <FaMapMarkerAlt />
              </InputIcon>
              <FormInput
                type="text"
                placeholder="City, Country"
                value={profile.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                disabled={!isEditing}
                theme={theme}
              />
            </InputContainer>
          </FormGroup>

          <FormGroup>
            <FormLabel theme={theme}>Website</FormLabel>
            <InputContainer $hasIcon>
              <InputIcon theme={theme}>
                <FaGlobe />
              </InputIcon>
              <FormInput
                type="url"
                placeholder="https://yourwebsite.com"
                value={profile.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                disabled={!isEditing}
                theme={theme}
              />
            </InputContainer>
          </FormGroup>

          <FormGroup>
            <FormLabel theme={theme}>Occupation</FormLabel>
            <InputContainer $hasIcon>
              <InputIcon theme={theme}>
                <FaBriefcase />
              </InputIcon>
              <FormInput
                type="text"
                placeholder="Your job title"
                value={profile.occupation}
                onChange={(e) => handleInputChange('occupation', e.target.value)}
                disabled={!isEditing}
                theme={theme}
              />
            </InputContainer>
          </FormGroup>

          <FormGroup>
            <FormLabel theme={theme}>Education</FormLabel>
            <InputContainer $hasIcon>
              <InputIcon theme={theme}>
                <FaGraduationCap />
              </InputIcon>
              <FormInput
                type="text"
                placeholder="School/University"
                value={profile.education}
                onChange={(e) => handleInputChange('education', e.target.value)}
                disabled={!isEditing}
                theme={theme}
              />
            </InputContainer>
          </FormGroup>

          <FormGroup>
            <FormLabel theme={theme}>Relationship Status</FormLabel>
            <InputContainer $hasIcon>
              <InputIcon theme={theme}>
                <FaHeart />
              </InputIcon>
              <FormInput
                type="text"
                placeholder="Single, Married, etc."
                value={profile.relationship}
                onChange={(e) => handleInputChange('relationship', e.target.value)}
                disabled={!isEditing}
                theme={theme}
              />
            </InputContainer>
          </FormGroup>

          {isEditing && (
            <PrivacySection theme={theme}>
              <PrivacyTitle theme={theme}>
                <FaEye /> Privacy Settings
              </PrivacyTitle>

              <PrivacyOption theme={theme}>
                <PrivacyLabel theme={theme}>Show Email</PrivacyLabel>
                <PrivacyToggle
                  $active={privacy.showEmail}
                  onClick={() => handlePrivacyChange('showEmail', !privacy.showEmail)}
                  theme={theme}
                />
              </PrivacyOption>

              <PrivacyOption theme={theme}>
                <PrivacyLabel theme={theme}>Show Phone</PrivacyLabel>
                <PrivacyToggle
                  $active={privacy.showPhone}
                  onClick={() => handlePrivacyChange('showPhone', !privacy.showPhone)}
                  theme={theme}
                />
              </PrivacyOption>

              <PrivacyOption theme={theme}>
                <PrivacyLabel theme={theme}>Show Birthday</PrivacyLabel>
                <PrivacyToggle
                  $active={privacy.showBirthday}
                  onClick={() => handlePrivacyChange('showBirthday', !privacy.showBirthday)}
                  theme={theme}
                />
              </PrivacyOption>

              <PrivacyOption theme={theme}>
                <PrivacyLabel theme={theme}>Show Location</PrivacyLabel>
                <PrivacyToggle
                  $active={privacy.showLocation}
                  onClick={() => handlePrivacyChange('showLocation', !privacy.showLocation)}
                  theme={theme}
                />
              </PrivacyOption>
            </PrivacySection>
          )}

          {hasChanges && (
            <SaveButton onClick={handleSave} theme={theme}>
              <FaSave /> Save Changes
            </SaveButton>
          )}
        </FormSection>
      </ProfileContent>
    </ProfileContainer>
  );
};

export default EnhancedProfileSettings;
