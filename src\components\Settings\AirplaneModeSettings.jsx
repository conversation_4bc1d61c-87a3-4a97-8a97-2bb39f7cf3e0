import React, { useState } from 'react';
import styled from 'styled-components';
import {
  FaPlane,
  FaToggleOn,
  FaToggle<PERSON>ff,
  FaWifi,
  FaBan,
  FaDownload,
  FaUpload,
  FaClock,
  FaInfoCircle,
  FaExclamationTriangle,
  FaBatteryFull,
  FaBellSlash
} from 'react-icons/fa';

const AirplaneModeContainer = styled.div`
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const SettingsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const SettingItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const SettingInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const IconWrapper = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary}20;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    color: ${props => props.color || props.theme.colors.primary};
    font-size: 18px;
  }
`;

const SettingText = styled.div`
  display: flex;
  flex-direction: column;
`;

const SettingName = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 0.95rem;
`;

const SettingDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 0.8rem;
  margin-top: 4px;
`;

const ToggleButton = styled.div`
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
  font-size: 1.5rem;
  cursor: pointer;
`;

const InfoBox = styled.div`
  margin-top: 20px;
  padding: 15px;
  background-color: ${props => props.theme.colors.primary}10;
  border-left: 4px solid ${props => props.theme.colors.primary};
  border-radius: 4px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
`;

const InfoIcon = styled.div`
  color: ${props => props.theme.colors.primary};
  font-size: 18px;
  margin-top: 2px;
`;

const InfoText = styled.div`
  color: ${props => props.theme.colors.text};
  font-size: 14px;
  line-height: 1.5;
`;

const WarningBox = styled.div`
  margin-top: 20px;
  padding: 15px;
  background-color: #FFF3CD;
  border-left: 4px solid #FFC107;
  border-radius: 4px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
`;

const WarningIcon = styled.div`
  color: #FFC107;
  font-size: 18px;
  margin-top: 2px;
`;

const WarningText = styled.div`
  color: #856404;
  font-size: 14px;
  line-height: 1.5;
`;

const TimeSelector = styled.div`
  display: flex;
  gap: 10px;
  align-items: center;
`;

const TimeInput = styled.input`
  padding: 8px;
  border-radius: 4px;
  border: 1px solid ${props => props.theme.colors.border};
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  width: 80px;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const TimeSeparator = styled.span`
  color: ${props => props.theme.colors.text};
`;

const BenefitsContainer = styled.div`
  margin-top: 20px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const BenefitsTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  margin-bottom: 12px;

  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const BenefitsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

const BenefitItem = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
`;

const BenefitIcon = styled.div`
  color: ${props => props.theme.colors.primary};
  font-size: 16px;
`;

const BenefitText = styled.div`
  color: ${props => props.theme.colors.text};
  font-size: 14px;
`;

const AirplaneModeSettings = ({ theme }) => {
  const [settings, setSettings] = useState({
    airplaneMode: false,
    allowWifi: true,
    allowDownloads: false,
    scheduleEnabled: false,
    startTime: '22:00',
    endTime: '07:00'
  });

  const toggleSetting = (setting) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  const handleTimeChange = (setting, value) => {
    setSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  return (
    <AirplaneModeContainer theme={theme}>
      <SectionTitle theme={theme}>Airplane Mode Settings</SectionTitle>

      <SettingsList>
        <SettingItem theme={theme} onClick={() => toggleSetting('airplaneMode')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#2196F3">
              <FaPlane />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Airplane Mode</SettingName>
              <SettingDescription theme={theme}>
                Disable all network connections
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton
            $active={settings.airplaneMode}
            theme={theme}
          >
            {settings.airplaneMode ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>

        {settings.airplaneMode && (
          <>
            <SettingItem theme={theme} onClick={() => toggleSetting('allowWifi')}>
              <SettingInfo>
                <IconWrapper theme={theme} color="#4CAF50">
                  {settings.allowWifi ? <FaWifi /> : <FaBan />}
                </IconWrapper>
                <SettingText>
                  <SettingName theme={theme}>Allow Wi-Fi</SettingName>
                  <SettingDescription theme={theme}>
                    Keep Wi-Fi enabled in Airplane Mode
                  </SettingDescription>
                </SettingText>
              </SettingInfo>
              <ToggleButton
                $active={settings.allowWifi}
                theme={theme}
              >
                {settings.allowWifi ? <FaToggleOn /> : <FaToggleOff />}
              </ToggleButton>
            </SettingItem>

            <SettingItem theme={theme} onClick={() => toggleSetting('allowDownloads')}>
              <SettingInfo>
                <IconWrapper theme={theme} color="#FF9800">
                  <FaDownload />
                </IconWrapper>
                <SettingText>
                  <SettingName theme={theme}>Allow downloads</SettingName>
                  <SettingDescription theme={theme}>
                    Allow media downloads in Airplane Mode
                  </SettingDescription>
                </SettingText>
              </SettingInfo>
              <ToggleButton
                $active={settings.allowDownloads}
                theme={theme}
              >
                {settings.allowDownloads ? <FaToggleOn /> : <FaToggleOff />}
              </ToggleButton>
            </SettingItem>
          </>
        )}

        <SettingItem theme={theme} onClick={() => toggleSetting('scheduleEnabled')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#9C27B0">
              <FaClock />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Schedule</SettingName>
              <SettingDescription theme={theme}>
                Set a specific time for Airplane Mode
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton
            $active={settings.scheduleEnabled}
            theme={theme}
          >
            {settings.scheduleEnabled ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>

        {settings.scheduleEnabled && (
          <SettingItem theme={theme}>
            <SettingInfo>
              <IconWrapper theme={theme} color="#E91E63">
                <FaPlane />
              </IconWrapper>
              <SettingText>
                <SettingName theme={theme}>Airplane hours</SettingName>
                <SettingDescription theme={theme}>
                  Set when Airplane Mode is active
                </SettingDescription>
              </SettingText>
            </SettingInfo>
            <TimeSelector>
              <TimeInput
                type="time"
                value={settings.startTime}
                onChange={(e) => handleTimeChange('startTime', e.target.value)}
                theme={theme}
              />
              <TimeSeparator theme={theme}>to</TimeSeparator>
              <TimeInput
                type="time"
                value={settings.endTime}
                onChange={(e) => handleTimeChange('endTime', e.target.value)}
                theme={theme}
              />
            </TimeSelector>
          </SettingItem>
        )}
      </SettingsList>

      <BenefitsContainer theme={theme}>
        <BenefitsTitle theme={theme}>
          <FaBatteryFull />
          Benefits of Airplane Mode
        </BenefitsTitle>

        <BenefitsList>
          <BenefitItem theme={theme}>
            <BenefitIcon theme={theme}>
              <FaBatteryFull />
            </BenefitIcon>
            <BenefitText theme={theme}>
              Extends battery life by reducing network usage
            </BenefitText>
          </BenefitItem>

          <BenefitItem theme={theme}>
            <BenefitIcon theme={theme}>
              <FaUpload />
            </BenefitIcon>
            <BenefitText theme={theme}>
              Prevents unwanted data usage and background syncing
            </BenefitText>
          </BenefitItem>

          <BenefitItem theme={theme}>
            <BenefitIcon theme={theme}>
              <FaBellSlash />
            </BenefitIcon>
            <BenefitText theme={theme}>
              Reduces distractions by blocking incoming notifications
            </BenefitText>
          </BenefitItem>
        </BenefitsList>
      </BenefitsContainer>

      <InfoBox theme={theme}>
        <InfoIcon theme={theme}>
          <FaInfoCircle />
        </InfoIcon>
        <InfoText theme={theme}>
          Airplane Mode disables all wireless connections including cellular data, Wi-Fi, and Bluetooth. You can still use your device for offline activities.
        </InfoText>
      </InfoBox>

      <WarningBox>
        <WarningIcon>
          <FaExclamationTriangle />
        </WarningIcon>
        <WarningText>
          When Airplane Mode is enabled, you won't be able to make or receive calls, send messages, or use online features until it's turned off.
        </WarningText>
      </WarningBox>
    </AirplaneModeContainer>
  );
};

export default AirplaneModeSettings;
