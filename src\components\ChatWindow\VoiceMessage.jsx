import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { FaPlay, FaPause, FaMicrophone } from 'react-icons/fa';

const VoiceMessageContainer = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  margin: 5px 0;
`;

const PlayButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.primary};
  font-size: 16px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 8px;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
`;

const WaveformContainer = styled.div`
  flex: 1;
  height: 32px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 16px;
  position: relative;
  overflow: hidden;
`;

const Waveform = styled.div`
  height: 100%;
  width: ${props => props.$progress}%;
  background: linear-gradient(
    to right,
    ${props => props.theme.colors.primary}22,
    ${props => props.theme.colors.primary}66
  );
  transition: width 0.1s linear;
`;

const WaveformBars = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  padding: 0 10px;
`;

const Bar = styled.div`
  width: 2px;
  height: ${props => props.$height}%;
  background-color: ${props => props.theme.colors.primary};
  margin: 0 1px;
  border-radius: 1px;
`;

const Duration = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.secondaryText};
  margin-left: 8px;
  min-width: 40px;
  text-align: right;
`;

const VoiceMessage = ({ duration, theme }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const intervalRef = useRef(null);
  
  // Generate random bars for the waveform
  const bars = Array.from({ length: 40 }, () => Math.floor(Math.random() * 70) + 10);
  
  useEffect(() => {
    if (isPlaying) {
      intervalRef.current = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(intervalRef.current);
            setIsPlaying(false);
            setCurrentTime(duration);
            return 100;
          }
          
          const newProgress = prev + (100 / (duration * 10));
          setCurrentTime(prev * duration / 100);
          return newProgress;
        });
      }, 100);
    } else {
      clearInterval(intervalRef.current);
    }
    
    return () => clearInterval(intervalRef.current);
  }, [isPlaying, duration]);
  
  const togglePlay = () => {
    if (progress >= 100) {
      setProgress(0);
      setCurrentTime(0);
    }
    setIsPlaying(!isPlaying);
  };
  
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };
  
  return (
    <VoiceMessageContainer>
      <PlayButton onClick={togglePlay} theme={theme}>
        {isPlaying ? <FaPause /> : <FaPlay />}
      </PlayButton>
      
      <WaveformContainer theme={theme}>
        <Waveform $progress={progress} theme={theme} />
        <WaveformBars>
          {bars.map((height, index) => (
            <Bar key={index} $height={height} theme={theme} />
          ))}
        </WaveformBars>
      </WaveformContainer>
      
      <Duration theme={theme}>
        {formatTime(isPlaying ? currentTime : duration)}
      </Duration>
    </VoiceMessageContainer>
  );
};

export default VoiceMessage;
