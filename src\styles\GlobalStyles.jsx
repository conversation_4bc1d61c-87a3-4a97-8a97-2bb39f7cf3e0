import { createGlobalStyle } from 'styled-components';

const GlobalStyles = createGlobalStyle`
  /* Import Google Fonts */
  @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    user-select: none; /* Prevent text selection for app-like feel */
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE/Edge */
  }

  body {
    font-family: var(--font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
    background-color: #f0f2f5;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    transition: font-family 0.3s ease;
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
    touch-action: manipulation; /* Optimize for touch */
  }

  #root {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* Responsive font sizes */
  html {
    font-size: 16px;

    @media (min-width: 1600px) {
      font-size: 18px;
    }

    @media (max-width: 768px) {
      font-size: 14px;
    }
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    input, button, textarea, select {
      font-size: 16px; /* Prevents zoom on focus in iOS */
    }

    /* Improve touch targets */
    button,
    [role="button"],
    input[type="submit"],
    input[type="reset"],
    input[type="button"] {
      min-height: 44px;
      min-width: 44px;
      cursor: pointer;
    }

    /* App-like feel */
    body {
      position: fixed; /* Prevent bounce/scroll on mobile */
      overscroll-behavior: none; /* Prevent pull-to-refresh */
      width: 100%;
      height: 100vh;
      height: 100dvh; /* Dynamic viewport height for mobile */
    }

    /* Allow text selection in input fields and text areas */
    input, textarea {
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
    }

    /* Disable long-press context menu for app-like feel */
    * {
      -webkit-touch-callout: none;
    }

    /* Improve scrolling on mobile */
    * {
      -webkit-overflow-scrolling: touch;
    }

    /* Better button interactions on mobile */
    button, [role="button"] {
      -webkit-tap-highlight-color: transparent;
      touch-action: manipulation;
    }
  }

  /* Desktop optimizations */
  @media (min-width: 769px) {
    /* Add subtle hover effects for desktop */
    button:hover,
    [role="button"]:hover {
      transform: translateY(-1px);
      transition: transform 0.2s ease;
    }
  }

  /* Classes to allow text selection in specific areas */
  .selectable-text {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
  }

  /* Message text should be selectable */
  .message-text {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
  }

  /* Add active state for buttons to provide feedback on touch/click */
  button:active,
  [role="button"]:active {
    opacity: 0.7;
    transform: scale(0.98);
    transition: all 0.1s ease;
  }

  /* Add app-like transitions */
  .app-transition {
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  /* Prevent blue highlight on Chrome for Android */
  input:focus,
  textarea:focus,
  select:focus,
  button:focus {
    outline: none;
  }
`;

export default GlobalStyles;
