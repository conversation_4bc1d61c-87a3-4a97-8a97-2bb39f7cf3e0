import { createGlobalStyle } from 'styled-components';

const GlobalStyles = createGlobalStyle`
  /* Import Google Fonts */
  @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    user-select: none; /* Prevent text selection for app-like feel */
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE/Edge */
  }

  body {
    font-family: var(--font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
    background-color: #f0f2f5;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    transition: font-family 0.3s ease;
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
    touch-action: manipulation; /* Optimize for touch */
  }

  #root {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* Responsive font sizes */
  html {
    font-size: 16px;

    @media (min-width: 1600px) {
      font-size: 18px;
    }

    @media (max-width: 768px) {
      font-size: 14px;
    }
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    input, button, textarea, select {
      font-size: 16px; /* Prevents zoom on focus in iOS */
    }

    /* Improve touch targets */
    button,
    [role="button"],
    input[type="submit"],
    input[type="reset"],
    input[type="button"] {
      min-height: 44px;
      min-width: 44px;
      cursor: pointer;
    }

    /* App-like feel */
    body {
      position: fixed; /* Prevent bounce/scroll on mobile */
      overscroll-behavior: none; /* Prevent pull-to-refresh */
      width: 100%;
      height: 100vh;
      height: 100dvh; /* Dynamic viewport height for mobile */
    }

    /* Allow text selection in input fields and text areas */
    input, textarea {
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
    }

    /* Disable long-press context menu for app-like feel */
    * {
      -webkit-touch-callout: none;
    }

    /* Improve scrolling on mobile */
    * {
      -webkit-overflow-scrolling: touch;
    }

    /* Better button interactions on mobile */
    button, [role="button"] {
      -webkit-tap-highlight-color: transparent;
      touch-action: manipulation;
    }

    /* Enhanced mobile viewport */
    html {
      height: 100%;
      height: 100dvh; /* Dynamic viewport height */
    }

    /* Prevent zoom on input focus */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="search"],
    textarea {
      font-size: 16px !important;
      transform: translateZ(0);
      -webkit-appearance: none;
      border-radius: 0;
    }

    /* Better scrolling performance */
    * {
      -webkit-overflow-scrolling: touch;
      scroll-behavior: smooth;
    }

    /* Optimize for mobile performance */
    * {
      -webkit-transform: translateZ(0);
      -moz-transform: translateZ(0);
      -ms-transform: translateZ(0);
      -o-transform: translateZ(0);
      transform: translateZ(0);
    }
  }

  /* Desktop optimizations */
  @media (min-width: 769px) {
    /* Add subtle hover effects for desktop */
    button:hover,
    [role="button"]:hover {
      transform: translateY(-1px);
      transition: transform 0.2s ease;
    }

    /* Enhanced desktop scrollbars */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      transition: background 0.2s ease;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 0, 0, 0.3);
    }

    /* Desktop-specific animations */
    .desktop-fade-in {
      animation: desktopFadeIn 0.3s ease-out;
    }

    @keyframes desktopFadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Enhanced focus styles for desktop */
    button:focus-visible,
    input:focus-visible,
    textarea:focus-visible {
      outline: 2px solid var(--primary-color, #25d366);
      outline-offset: 2px;
      border-radius: 4px;
    }

    /* Desktop grid layouts */
    .desktop-grid {
      display: grid;
      gap: 16px;
    }

    .desktop-grid-2 {
      grid-template-columns: repeat(2, 1fr);
    }

    .desktop-grid-3 {
      grid-template-columns: repeat(3, 1fr);
    }

    .desktop-grid-4 {
      grid-template-columns: repeat(4, 1fr);
    }

    /* Desktop card hover effects */
    .desktop-card {
      transition: all 0.2s ease;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .desktop-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    /* Desktop navigation enhancements */
    .desktop-nav-item {
      position: relative;
      overflow: hidden;
    }

    .desktop-nav-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.5s ease;
    }

    .desktop-nav-item:hover::before {
      left: 100%;
    }
  }

  /* Large desktop optimizations */
  @media (min-width: 1200px) {
    /* Larger spacing for big screens */
    .large-desktop-spacing {
      padding: 24px;
      gap: 20px;
    }

    /* Enhanced typography for large screens */
    .large-desktop-text {
      font-size: 1.1em;
      line-height: 1.6;
    }

    /* Multi-column layouts */
    .large-desktop-columns {
      column-count: 2;
      column-gap: 32px;
    }
  }

  /* Ultra-wide desktop optimizations */
  @media (min-width: 1600px) {
    /* Prevent content from stretching too wide */
    .ultra-wide-container {
      max-width: 1600px;
      margin: 0 auto;
    }

    /* Enhanced sidebar for ultra-wide */
    .ultra-wide-sidebar {
      width: 400px;
      min-width: 350px;
    }

    /* Three-column layout for ultra-wide */
    .ultra-wide-grid {
      display: grid;
      grid-template-columns: 350px 1fr 300px;
      gap: 24px;
    }
  }

  /* Classes to allow text selection in specific areas */
  .selectable-text {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
  }

  /* Message text should be selectable */
  .message-text {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
  }

  /* Add active state for buttons to provide feedback on touch/click */
  button:active,
  [role="button"]:active {
    opacity: 0.7;
    transform: scale(0.98);
    transition: all 0.1s ease;
  }

  /* Add app-like transitions */
  .app-transition {
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  /* Prevent blue highlight on Chrome for Android */
  input:focus,
  textarea:focus,
  select:focus,
  button:focus {
    outline: none;
  }
`;

export default GlobalStyles;
