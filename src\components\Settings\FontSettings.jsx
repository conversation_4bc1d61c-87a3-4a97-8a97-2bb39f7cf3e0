import React, { useState } from 'react';
import styled from 'styled-components';
import {
  FaFont,
  FaPlus,
  FaMinus,
  FaCheck,
  FaToggleOn,
  FaToggleOff,
  FaAlignLeft,
  FaAlignCenter,
  FaAlignRight,
  FaArrowLeft
} from 'react-icons/fa';

const FontSettingsContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${props => props.theme.colors.background};
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
`;

const SettingsHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  min-height: 60px;
  position: sticky;
  top: 0;
  z-index: 10;
`;

const HeaderTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
`;

const HeaderButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const SettingsContent = styled.div`
  flex: 1;
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const SettingsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const SettingItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const SettingInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const IconWrapper = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary}20;
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    color: ${props => props.color || props.theme.colors.primary};
    font-size: 18px;
  }
`;

const SettingText = styled.div`
  display: flex;
  flex-direction: column;
`;

const SettingName = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 0.95rem;
`;

const SettingDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 0.8rem;
  margin-top: 4px;
`;

const ToggleButton = styled.div`
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
  font-size: 1.5rem;
  cursor: pointer;
`;

const FontSizeControls = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const SizeButton = styled.button`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background-color: ${props => props.theme.colors.secondary};
  color: ${props => props.theme.colors.text};
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.primary}20;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const FontSizeValue = styled.div`
  min-width: 40px;
  text-align: center;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
`;

const FontSelector = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

const FontOption = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
  
  ${props => props.$selected && `
    background-color: ${props.theme.colors.primary}10;
    border-left: 3px solid ${props.theme.colors.primary};
  `}
`;

const FontName = styled.div`
  color: ${props => props.theme.colors.text};
  font-family: ${props => props.$fontFamily};
  font-size: 16px;
`;

const CheckIcon = styled.div`
  color: ${props => props.theme.colors.primary};
  display: ${props => props.$show ? 'block' : 'none'};
`;

const AlignmentOptions = styled.div`
  display: flex;
  gap: 10px;
`;

const AlignButton = styled.button`
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: none;
  background-color: ${props => props.$active 
    ? props.theme.colors.primary 
    : props.theme.colors.secondary};
  color: ${props => props.$active ? 'white' : props.theme.colors.text};
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.$active 
      ? props.theme.colors.primary 
      : props.theme.colors.primary}20;
  }
`;

const PreviewContainer = styled.div`
  margin-top: 20px;
  padding: 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const PreviewTitle = styled.div`
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  
  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const PreviewText = styled.div`
  font-family: ${props => props.$fontFamily};
  font-size: ${props => props.$fontSize}px;
  color: ${props => props.theme.colors.text};
  text-align: ${props => props.$textAlign};
  padding: 15px;
  background-color: ${props => props.$isSent 
    ? props.theme.colors.sentMessage 
    : props.theme.colors.receivedMessage};
  border-radius: 8px;
  margin: 10px 0;
  max-width: 80%;
  align-self: ${props => props.$isSent ? 'flex-end' : 'flex-start'};
`;

const PreviewMessages = styled.div`
  display: flex;
  flex-direction: column;
`;

const FontSettings = ({ theme, onClose }) => {
  const [settings, setSettings] = useState({
    fontSize: 14,
    fontFamily: 'Segoe UI',
    textAlign: 'left',
    enableCustomFonts: false
  });
  
  const [showFontSelector, setShowFontSelector] = useState(false);
  
  const fontOptions = [
    { name: 'Segoe UI', value: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif' },
    { name: 'Arial', value: 'Arial, sans-serif' },
    { name: 'Roboto', value: 'Roboto, sans-serif' },
    { name: 'Open Sans', value: 'Open Sans, sans-serif' },
    { name: 'Montserrat', value: 'Montserrat, sans-serif' },
    { name: 'Comic Sans MS', value: 'Comic Sans MS, cursive' },
    { name: 'Georgia', value: 'Georgia, serif' },
    { name: 'Courier New', value: 'Courier New, monospace' }
  ];
  
  const handleFontSizeChange = (increment) => {
    setSettings(prev => ({
      ...prev,
      fontSize: Math.max(10, Math.min(24, prev.fontSize + increment))
    }));
  };
  
  const handleFontFamilyChange = (fontFamily) => {
    setSettings(prev => ({
      ...prev,
      fontFamily
    }));
    setShowFontSelector(false);
  };
  
  const handleTextAlignChange = (textAlign) => {
    setSettings(prev => ({
      ...prev,
      textAlign
    }));
  };
  
  const toggleCustomFonts = () => {
    setSettings(prev => ({
      ...prev,
      enableCustomFonts: !prev.enableCustomFonts
    }));
  };
  
  return (
    <FontSettingsContainer theme={theme}>
      <SettingsHeader theme={theme}>
        <HeaderButton onClick={onClose}>
          <FaArrowLeft />
        </HeaderButton>
        <HeaderTitle>Fonts & Typography</HeaderTitle>
        <HeaderButton onClick={() => {}}>
          {/* Placeholder for future action */}
        </HeaderButton>
      </SettingsHeader>

      <SettingsContent>
        <SectionTitle theme={theme}>Font Settings</SectionTitle>

        <SettingsList>
        <SettingItem theme={theme}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#4CAF50">
              <FaFont />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Font Size</SettingName>
              <SettingDescription theme={theme}>
                Adjust the size of text in messages
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <FontSizeControls>
            <SizeButton 
              onClick={() => handleFontSizeChange(-1)}
              disabled={settings.fontSize <= 10}
              theme={theme}
            >
              <FaMinus />
            </SizeButton>
            <FontSizeValue theme={theme}>{settings.fontSize}px</FontSizeValue>
            <SizeButton 
              onClick={() => handleFontSizeChange(1)}
              disabled={settings.fontSize >= 24}
              theme={theme}
            >
              <FaPlus />
            </SizeButton>
          </FontSizeControls>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => setShowFontSelector(!showFontSelector)}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#2196F3">
              <FaFont />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Font Family</SettingName>
              <SettingDescription theme={theme}>
                Change the font used in the app
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <div style={{ fontFamily: settings.fontFamily, color: theme.colors.text }}>
            {settings.fontFamily.split(',')[0]}
          </div>
        </SettingItem>
        
        {showFontSelector && (
          <FontSelector>
            {fontOptions.map(font => (
              <FontOption 
                key={font.name} 
                onClick={() => handleFontFamilyChange(font.value)}
                $selected={settings.fontFamily === font.value}
                theme={theme}
              >
                <FontName $fontFamily={font.value} theme={theme}>
                  {font.name}
                </FontName>
                <CheckIcon 
                  $show={settings.fontFamily === font.value}
                  theme={theme}
                >
                  <FaCheck />
                </CheckIcon>
              </FontOption>
            ))}
          </FontSelector>
        )}
        
        <SettingItem theme={theme}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#FF9800">
              <FaAlignLeft />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Text Alignment</SettingName>
              <SettingDescription theme={theme}>
                Set how text is aligned in messages
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <AlignmentOptions>
            <AlignButton 
              onClick={() => handleTextAlignChange('left')}
              $active={settings.textAlign === 'left'}
              theme={theme}
            >
              <FaAlignLeft />
            </AlignButton>
            <AlignButton 
              onClick={() => handleTextAlignChange('center')}
              $active={settings.textAlign === 'center'}
              theme={theme}
            >
              <FaAlignCenter />
            </AlignButton>
            <AlignButton 
              onClick={() => handleTextAlignChange('right')}
              $active={settings.textAlign === 'right'}
              theme={theme}
            >
              <FaAlignRight />
            </AlignButton>
          </AlignmentOptions>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={toggleCustomFonts}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#9C27B0">
              <FaFont />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Custom Fonts</SettingName>
              <SettingDescription theme={theme}>
                Allow custom fonts from Google Fonts
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.enableCustomFonts} 
            theme={theme}
          >
            {settings.enableCustomFonts ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
      </SettingsList>
      
      <PreviewContainer theme={theme}>
        <PreviewTitle theme={theme}>
          <FaFont />
          Preview
        </PreviewTitle>
        
        <PreviewMessages>
          <PreviewText 
            $fontFamily={settings.fontFamily}
            $fontSize={settings.fontSize}
            $textAlign={settings.textAlign}
            $isSent={false}
            theme={theme}
          >
            Hello! How are you doing today?
          </PreviewText>
          
          <PreviewText 
            $fontFamily={settings.fontFamily}
            $fontSize={settings.fontSize}
            $textAlign={settings.textAlign}
            $isSent={true}
            theme={theme}
          >
            I'm doing great, thanks for asking! How about you?
          </PreviewText>
        </PreviewMessages>
      </PreviewContainer>
      </SettingsContent>
    </FontSettingsContainer>
  );
};

export default FontSettings;
