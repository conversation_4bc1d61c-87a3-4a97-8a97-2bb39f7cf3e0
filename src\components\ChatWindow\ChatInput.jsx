import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  FaSmile,
  FaPaperclip,
  FaMicrophone,
  FaCamera,
  FaImage,
  FaFile,
  FaLocationArrow,
  FaVideo,
  FaUser,
  FaPhoneAlt
} from 'react-icons/fa';
import { useTheme } from '../../context/ThemeContext';
import { usePrivacy } from '../../context/PrivacyContext';
import VoiceRecorder from './VoiceRecorder';
import EmojiPicker from './EmojiPicker';
import storage from '../../utils/storage';

const InputContainer = styled.div`
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: ${props => props.theme.colors.secondary};
`;

const IconWrapper = styled.div`
  color: ${props => props.theme.colors.icon};
  font-size: 1.5rem;
  margin: 0 10px;
  cursor: pointer;

  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const InputField = styled.input`
  flex: 1;
  border: none;
  border-radius: 21px;
  padding: 9px 12px;
  font-size: 15px;
  outline: none;
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  z-index: 1;
  position: relative;

  /* Fix for mobile visibility */
  -webkit-appearance: none;
  appearance: none;
  opacity: 1 !important;

  &::placeholder {
    color: ${props => props.theme.colors.secondaryText};
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    font-size: 16px; /* Prevents zoom on iOS */
  }
`;

const SendButton = styled.button`
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10px;
  cursor: pointer;

  &:disabled {
    background-color: #b3b3b3;
    cursor: default;
  }
`;

const AttachmentMenu = styled.div`
  position: absolute;
  bottom: 70px;
  left: 50px;
  display: ${props => props.$show ? 'grid' : 'none'};
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  z-index: 10;
  max-width: 320px;
  animation: fadeIn 0.2s ease-out;

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
`;

const AttachmentOption = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
`;

const AttachmentIcon = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: ${props => props.color};
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 1.2rem;
  margin-bottom: 5px;
`;

const AttachmentLabel = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.text};
`;

const TypingIndicator = styled.div`
  position: absolute;
  bottom: 70px;
  left: 20px;
  font-size: 12px;
  color: ${props => props.theme.colors.secondaryText};
  background-color: ${props => props.theme.colors.background};
  padding: 5px 10px;
  border-radius: 10px;
  opacity: ${props => props.$show ? 1 : 0};
  transition: opacity 0.3s;
`;

const ChatInput = ({ onSendMessage, chatId }) => {
  const [message, setMessage] = useState('');
  const [showAttachments, setShowAttachments] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [uploadingFile, setUploadingFile] = useState(false);
  const { currentTheme } = useTheme();
  const { privacySettings } = usePrivacy();
  const fileInputRef = React.useRef(null);
  const imageInputRef = React.useRef(null);
  const videoInputRef = React.useRef(null);

  const handleSend = () => {
    if (message.trim()) {
      onSendMessage(message);
      setMessage('');
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSend();
    }
  };

  const toggleAttachments = () => {
    setShowAttachments(!showAttachments);
    if (showEmojiPicker) setShowEmojiPicker(false);
  };

  const toggleEmojiPicker = () => {
    setShowEmojiPicker(!showEmojiPicker);
    if (showAttachments) setShowAttachments(false);
  };

  const handleEmojiSelect = (emoji) => {
    setMessage(prev => prev + emoji);
    // Focus the input field after selecting an emoji
    document.querySelector('input[type="text"]').focus();
  };

  const handleStartRecording = () => {
    setIsRecording(true);
  };

  const handleSendVoiceMessage = (voiceMessage) => {
    onSendMessage(`Voice message (${voiceMessage.duration} seconds)`, voiceMessage);
    setIsRecording(false);
  };

  const handleFileUpload = async (file, type = 'file') => {
    if (!file) return;

    setUploadingFile(true);

    try {
      // Create a unique ID for the media
      const mediaId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);

      // Convert file to base64 for storage
      const reader = new FileReader();
      reader.onload = (e) => {
        const mediaData = {
          id: mediaId,
          name: file.name,
          type: file.type,
          size: file.size,
          url: e.target.result,
          uploadedAt: Date.now()
        };

        // Save media to storage
        storage.saveMedia(mediaId, mediaData);

        // Send message with media
        const messageText = type === 'image' ? '📷 Photo' :
                           type === 'video' ? '🎥 Video' :
                           `📎 ${file.name}`;

        onSendMessage(messageText, null, mediaData);
        setUploadingFile(false);
      };

      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading file:', error);
      setUploadingFile(false);
    }
  };

  const handleImageUpload = () => {
    imageInputRef.current?.click();
  };

  const handleVideoUpload = () => {
    videoInputRef.current?.click();
  };

  const handleDocumentUpload = () => {
    fileInputRef.current?.click();
  };

  const handleCameraCapture = () => {
    // This would open the camera component
    console.log('Open camera');
  };

  const handleLocationShare = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const locationMessage = `📍 Location: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
          onSendMessage(locationMessage);
        },
        (error) => {
          console.error('Error getting location:', error);
        }
      );
    }
  };

  const handlePaste = async (e) => {
    const items = e.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile();
        if (file) {
          await handleFileUpload(file, 'image');
        }
      }
    }
  };

  useEffect(() => {
    if (message.trim() && !isTyping && !privacySettings.hideTypingIndicator) {
      setIsTyping(true);
    } else if (!message.trim() && isTyping) {
      setIsTyping(false);
    }
  }, [message, isTyping, privacySettings.hideTypingIndicator]);

  const attachmentOptions = [
    { icon: <FaImage />, label: 'Photos', color: '#1E88E5', action: handleImageUpload },
    { icon: <FaCamera />, label: 'Camera', color: '#E53935', action: handleCameraCapture },
    { icon: <FaVideo />, label: 'Video', color: '#FF9800', action: handleVideoUpload },
    { icon: <FaFile />, label: 'Document', color: '#8E24AA', action: handleDocumentUpload },
    { icon: <FaLocationArrow />, label: 'Location', color: '#43A047', action: handleLocationShare },
    { icon: <FaUser />, label: 'Contact', color: '#009688', action: () => console.log('Share contact') },
    { icon: <FaPhoneAlt />, label: 'Call', color: '#F44336', action: () => console.log('Start call') },
    { icon: <FaMicrophone />, label: 'Voice', color: '#795548', action: handleStartRecording },
  ];

  return (
    <InputContainer theme={currentTheme}>
      {/* Hidden file inputs */}
      <input
        ref={fileInputRef}
        type="file"
        accept="*/*"
        style={{ display: 'none' }}
        onChange={(e) => {
          const file = e.target.files[0];
          if (file) handleFileUpload(file, 'file');
        }}
      />
      <input
        ref={imageInputRef}
        type="file"
        accept="image/*"
        style={{ display: 'none' }}
        onChange={(e) => {
          const file = e.target.files[0];
          if (file) handleFileUpload(file, 'image');
        }}
      />
      <input
        ref={videoInputRef}
        type="file"
        accept="video/*"
        style={{ display: 'none' }}
        onChange={(e) => {
          const file = e.target.files[0];
          if (file) handleFileUpload(file, 'video');
        }}
      />

      {isRecording ? (
        <VoiceRecorder
          onSend={handleSendVoiceMessage}
          theme={currentTheme}
        />
      ) : (
        <>
          <IconWrapper theme={currentTheme} onClick={toggleEmojiPicker}>
            <FaSmile />
          </IconWrapper>
          <IconWrapper theme={currentTheme} onClick={toggleAttachments}>
            <FaPaperclip />
          </IconWrapper>

          <AttachmentMenu $show={showAttachments} theme={currentTheme}>
            {attachmentOptions.map((option, index) => (
              <AttachmentOption
                key={index}
                onClick={() => {
                  option.action();
                  setShowAttachments(false);
                }}
              >
                <AttachmentIcon color={option.color}>
                  {option.icon}
                </AttachmentIcon>
                <AttachmentLabel theme={currentTheme}>{option.label}</AttachmentLabel>
              </AttachmentOption>
            ))}
          </AttachmentMenu>

          <EmojiPicker
            show={showEmojiPicker}
            onEmojiSelect={handleEmojiSelect}
            theme={currentTheme}
          />

          <TypingIndicator $show={isTyping} theme={currentTheme}>
            Typing...
          </TypingIndicator>

          <InputField
            type="text"
            placeholder={uploadingFile ? "Uploading file..." : "Type a message"}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            onPaste={handlePaste}
            onFocus={() => {
              // Show mobile keyboard on mobile devices
              if (window.innerWidth <= 768) {
                window.showMobileKeyboard = true;
              }
            }}
            theme={currentTheme}
            className="selectable-text"
            autoComplete="off"
            disabled={uploadingFile}
          />
          {message.trim() ? (
            <SendButton onClick={handleSend} theme={currentTheme}>
              <span>➤</span>
            </SendButton>
          ) : (
            <IconWrapper theme={currentTheme} onClick={handleStartRecording}>
              <FaMicrophone />
            </IconWrapper>
          )}
        </>
      )}
    </InputContainer>
  );
};

export default ChatInput;
