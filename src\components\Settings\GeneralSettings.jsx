import React from 'react';
import styled from 'styled-components';
import { useTheme } from '../../context/ThemeContext';

const GeneralSettingsContainer = styled.div`
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const SettingItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const SettingLabel = styled.div`
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
`;

const SettingDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 0.8rem;
  margin-top: 4px;
`;

const ToggleSwitch = styled.label`
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  flex-shrink: 0;
`;

const ToggleInput = styled.input`
  opacity: 0;
  width: 0;
  height: 0;

  &:checked + span {
    background-color: ${props => props.theme.colors.primary};
  }

  &:checked + span:before {
    transform: translateX(26px);
  }
`;

const ToggleSlider = styled.span`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;

  &:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }
`;

const SelectWrapper = styled.div`
  position: relative;
`;

const Select = styled.select`
  appearance: none;
  padding: 8px 32px 8px 12px;
  border-radius: 4px;
  border: 1px solid ${props => props.theme.colors.border};
  background-color: ${props => props.theme.colors.secondary};
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const SelectArrow = styled.div`
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  pointer-events: none;
  color: ${props => props.theme.colors.icon};
`;

const Button = styled.button`
  padding: 8px 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;

  &:hover {
    opacity: 0.9;
  }
`;

const GeneralSettings = ({ theme }) => {
  const { currentTheme } = useTheme();
  const themeToUse = theme || currentTheme;
  const [settings, setSettings] = React.useState({
    notifications: true,
    sounds: true,
    enterToSend: true,
    mediaAutoDownload: 'wifi',
    fontSize: 'medium',
  });

  const handleToggleChange = (setting) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  const handleSelectChange = (setting, e) => {
    setSettings(prev => ({
      ...prev,
      [setting]: e.target.value
    }));
  };

  return (
    <GeneralSettingsContainer theme={themeToUse}>
      <SectionTitle theme={themeToUse}>General Settings</SectionTitle>

      <SettingItem theme={themeToUse}>
        <div>
          <SettingLabel theme={themeToUse}>Notifications</SettingLabel>
          <SettingDescription theme={themeToUse}>
            Receive notifications for new messages
          </SettingDescription>
        </div>
        <ToggleSwitch>
          <ToggleInput
            type="checkbox"
            checked={settings.notifications}
            onChange={() => handleToggleChange('notifications')}
            theme={themeToUse}
          />
          <ToggleSlider />
        </ToggleSwitch>
      </SettingItem>

      <SettingItem theme={themeToUse}>
        <div>
          <SettingLabel theme={themeToUse}>Message Sounds</SettingLabel>
          <SettingDescription theme={themeToUse}>
            Play sounds for incoming and outgoing messages
          </SettingDescription>
        </div>
        <ToggleSwitch>
          <ToggleInput
            type="checkbox"
            checked={settings.sounds}
            onChange={() => handleToggleChange('sounds')}
            theme={themeToUse}
          />
          <ToggleSlider />
        </ToggleSwitch>
      </SettingItem>

      <SettingItem theme={themeToUse}>
        <div>
          <SettingLabel theme={themeToUse}>Enter to Send</SettingLabel>
          <SettingDescription theme={themeToUse}>
            Press Enter key to send messages
          </SettingDescription>
        </div>
        <ToggleSwitch>
          <ToggleInput
            type="checkbox"
            checked={settings.enterToSend}
            onChange={() => handleToggleChange('enterToSend')}
            theme={themeToUse}
          />
          <ToggleSlider />
        </ToggleSwitch>
      </SettingItem>

      <SettingItem theme={themeToUse}>
        <div>
          <SettingLabel theme={themeToUse}>Media Auto-Download</SettingLabel>
          <SettingDescription theme={themeToUse}>
            Automatically download media files
          </SettingDescription>
        </div>
        <SelectWrapper>
          <Select
            value={settings.mediaAutoDownload}
            onChange={(e) => handleSelectChange('mediaAutoDownload', e)}
            theme={themeToUse}
          >
            <option value="always">Always</option>
            <option value="wifi">Wi-Fi only</option>
            <option value="never">Never</option>
          </Select>
          <SelectArrow theme={themeToUse}>▼</SelectArrow>
        </SelectWrapper>
      </SettingItem>

      <SettingItem theme={themeToUse}>
        <div>
          <SettingLabel theme={themeToUse}>Font Size</SettingLabel>
          <SettingDescription theme={themeToUse}>
            Change the size of text in chats
          </SettingDescription>
        </div>
        <SelectWrapper>
          <Select
            value={settings.fontSize}
            onChange={(e) => handleSelectChange('fontSize', e)}
            theme={themeToUse}
          >
            <option value="small">Small</option>
            <option value="medium">Medium</option>
            <option value="large">Large</option>
          </Select>
          <SelectArrow theme={themeToUse}>▼</SelectArrow>
        </SelectWrapper>
      </SettingItem>

      <SettingItem theme={themeToUse} style={{ justifyContent: 'flex-start', gap: '16px' }}>
        <Button theme={themeToUse}>Clear All Chats</Button>
        <Button theme={themeToUse}>Backup Chats</Button>
      </SettingItem>
    </GeneralSettingsContainer>
  );
};

export default GeneralSettings;
