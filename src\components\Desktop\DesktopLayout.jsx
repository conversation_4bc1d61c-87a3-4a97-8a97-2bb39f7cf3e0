import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaSearch,
  FaEllipsisV,
  FaBell,
  FaCog,
  FaUser,
  FaComments,
  FaPhone,
  FaVideo,
  FaUsers,
  FaStar,
  FaArchive,
  FaPlus,
  FaFilter,
  FaSort,
  FaExpand,
  FaCompress,
  FaMoon,
  FaSun,
  FaDesktop,
  FaMobile,
  FaTabletAlt
} from 'react-icons/fa';
import { useTheme } from '../../context/ThemeContext';

const DesktopContainer = styled.div`
  display: flex;
  height: 100vh;
  width: 100%;
  background-color: ${props => props.theme.colors.background};
  position: relative;
  overflow: hidden;

  /* Responsive breakpoints */
  @media (max-width: 1024px) {
    flex-direction: column;
  }

  @media (min-width: 1200px) {
    max-width: 95%;
    height: 95vh;
    margin: 2.5vh auto;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.15);
    border-radius: 16px;
    overflow: hidden;
  }

  @media (min-width: 1600px) {
    max-width: 1600px;
  }

  @media (min-width: 2000px) {
    max-width: 1800px;
  }
`;

const TopBar = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary}, ${props => props.theme.colors.primaryDark || props.theme.colors.primary});
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  @media (max-width: 1024px) {
    position: relative;
  }
`;

const AppTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
  font-size: 20px;
  font-weight: 600;
`;

const AppLogo = styled.div`
  width: 32px;
  height: 32px;
  background: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.theme.colors.primary};
  font-weight: bold;
  font-size: 16px;
`;

const TopBarActions = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const TopBarButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
`;

const NotificationBadge = styled.div`
  position: absolute;
  top: -2px;
  right: -2px;
  width: 18px;
  height: 18px;
  background: #ff4444;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  color: white;
  border: 2px solid white;
`;

const MainContent = styled.div`
  display: flex;
  flex: 1;
  margin-top: 60px;
  height: calc(100vh - 60px);

  @media (max-width: 1024px) {
    margin-top: 0;
    height: calc(100vh - 60px);
    flex-direction: column;
  }

  @media (min-width: 1200px) {
    height: calc(95vh - 60px);
  }
`;

const NavigationPanel = styled.div`
  width: 80px;
  background-color: ${props => props.theme.colors.secondary};
  border-right: 1px solid ${props => props.theme.colors.border};
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  gap: 16px;
  transition: width 0.3s ease;

  ${props => props.$expanded && `
    width: 240px;
    align-items: flex-start;
    padding: 20px;
  `}

  @media (max-width: 1024px) {
    width: 100%;
    height: 60px;
    flex-direction: row;
    justify-content: space-around;
    padding: 0 20px;
    border-right: none;
    border-bottom: 1px solid ${props => props.theme.colors.border};
  }
`;

const NavItem = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.text};
  background-color: ${props => props.$active ? props.theme.colors.primary + '20' : 'transparent'};
  width: ${props => props.$expanded ? '100%' : '48px'};
  height: 48px;
  justify-content: ${props => props.$expanded ? 'flex-start' : 'center'};
  position: relative;

  &:hover {
    background-color: ${props => props.theme.colors.primary}20;
    color: ${props => props.theme.colors.primary};
    transform: translateY(-1px);
  }

  @media (max-width: 1024px) {
    width: auto;
    flex-direction: column;
    gap: 4px;
    font-size: 12px;
  }
`;

const NavIcon = styled.div`
  font-size: 20px;
  min-width: 20px;

  @media (max-width: 1024px) {
    font-size: 18px;
  }
`;

const NavLabel = styled.div`
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  opacity: ${props => props.$expanded ? 1 : 0};
  transition: opacity 0.3s ease;

  @media (max-width: 1024px) {
    opacity: 1;
    font-size: 10px;
  }
`;

const NavBadge = styled.div`
  position: absolute;
  top: 8px;
  right: 8px;
  width: 16px;
  height: 16px;
  background: ${props => props.theme.colors.primary};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  color: white;
`;

const ContentArea = styled.div`
  flex: 1;
  display: flex;
  background-color: ${props => props.theme.colors.background};
  position: relative;
  overflow: hidden;
`;

const SidebarPanel = styled.div`
  width: ${props => props.$collapsed ? '0' : '350px'};
  min-width: ${props => props.$collapsed ? '0' : '300px'};
  max-width: 400px;
  background-color: ${props => props.theme.colors.background};
  border-right: 1px solid ${props => props.theme.colors.border};
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  overflow: hidden;

  @media (max-width: 1024px) {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
    border-right: none;
    border-bottom: 1px solid ${props => props.theme.colors.border};
  }
`;

const ChatPanel = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: ${props => props.theme.colors.chatBackground};
  position: relative;
  min-width: 0;

  @media (max-width: 1024px) {
    width: 100%;
  }
`;

const ResizeHandle = styled.div`
  width: 4px;
  background-color: transparent;
  cursor: col-resize;
  position: relative;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${props => props.theme.colors.primary}40;
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 40px;
    background-color: ${props => props.theme.colors.border};
    border-radius: 1px;
  }

  @media (max-width: 1024px) {
    display: none;
  }
`;

const ViewModeToggle = styled.div`
  display: flex;
  gap: 8px;
  margin-left: auto;

  @media (max-width: 1024px) {
    display: none;
  }
`;

const ViewModeButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  &.active {
    background: rgba(255, 255, 255, 0.3);
  }
`;

const DesktopLayout = ({ 
  children, 
  sidebar, 
  chatWindow, 
  activeTab, 
  onTabChange,
  onSettingsClick,
  onProfileClick,
  onNotificationsClick,
  unreadCount = 0,
  notificationCount = 0
}) => {
  const { currentTheme, isDarkMode, toggleDarkMode } = useTheme();
  const [navExpanded, setNavExpanded] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [viewMode, setViewMode] = useState('desktop'); // desktop, tablet, mobile
  const [isResizing, setIsResizing] = useState(false);

  const navigationItems = [
    { id: 'chats', icon: <FaComments />, label: 'Chats', badge: unreadCount },
    { id: 'calls', icon: <FaPhone />, label: 'Calls' },
    { id: 'groups', icon: <FaUsers />, label: 'Groups' },
    { id: 'starred', icon: <FaStar />, label: 'Starred' },
    { id: 'archived', icon: <FaArchive />, label: 'Archived' }
  ];

  const handleNavItemClick = (itemId) => {
    if (onTabChange) {
      onTabChange(itemId);
    }
  };

  const handleViewModeChange = (mode) => {
    setViewMode(mode);
    // Apply different layouts based on view mode
    if (mode === 'mobile') {
      setSidebarCollapsed(true);
      setNavExpanded(false);
    } else if (mode === 'tablet') {
      setSidebarCollapsed(false);
      setNavExpanded(false);
    } else {
      setSidebarCollapsed(false);
      setNavExpanded(true);
    }
  };

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width <= 768) {
        setViewMode('mobile');
        setSidebarCollapsed(true);
        setNavExpanded(false);
      } else if (width <= 1024) {
        setViewMode('tablet');
        setSidebarCollapsed(false);
        setNavExpanded(false);
      } else {
        setViewMode('desktop');
        setSidebarCollapsed(false);
        setNavExpanded(true);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Initial call

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <DesktopContainer theme={currentTheme}>
      <TopBar theme={currentTheme}>
        <AppTitle>
          <AppLogo theme={currentTheme}>GB</AppLogo>
          GBChat
        </AppTitle>
        
        <TopBarActions>
          <ViewModeToggle>
            <ViewModeButton 
              className={viewMode === 'desktop' ? 'active' : ''}
              onClick={() => handleViewModeChange('desktop')}
              title="Desktop View"
            >
              <FaDesktop />
            </ViewModeButton>
            <ViewModeButton 
              className={viewMode === 'tablet' ? 'active' : ''}
              onClick={() => handleViewModeChange('tablet')}
              title="Tablet View"
            >
              <FaTabletAlt />
            </ViewModeButton>
            <ViewModeButton 
              className={viewMode === 'mobile' ? 'active' : ''}
              onClick={() => handleViewModeChange('mobile')}
              title="Mobile View"
            >
              <FaMobile />
            </ViewModeButton>
          </ViewModeToggle>

          <TopBarButton onClick={toggleDarkMode} title={isDarkMode ? 'Light Mode' : 'Dark Mode'}>
            {isDarkMode ? <FaSun /> : <FaMoon />}
          </TopBarButton>

          <TopBarButton onClick={onNotificationsClick} title="Notifications">
            <FaBell />
            {notificationCount > 0 && (
              <NotificationBadge>{notificationCount > 99 ? '99+' : notificationCount}</NotificationBadge>
            )}
          </TopBarButton>

          <TopBarButton onClick={onProfileClick} title="Profile">
            <FaUser />
          </TopBarButton>

          <TopBarButton onClick={onSettingsClick} title="Settings">
            <FaCog />
          </TopBarButton>
        </TopBarActions>
      </TopBar>

      <MainContent>
        <NavigationPanel theme={currentTheme} $expanded={navExpanded}>
          {navigationItems.map(item => (
            <NavItem
              key={item.id}
              $active={activeTab === item.id}
              $expanded={navExpanded}
              onClick={() => handleNavItemClick(item.id)}
              theme={currentTheme}
            >
              <NavIcon>{item.icon}</NavIcon>
              <NavLabel $expanded={navExpanded}>{item.label}</NavLabel>
              {item.badge > 0 && <NavBadge theme={currentTheme}>{item.badge > 99 ? '99+' : item.badge}</NavBadge>}
            </NavItem>
          ))}
        </NavigationPanel>

        <ContentArea theme={currentTheme}>
          <SidebarPanel theme={currentTheme} $collapsed={sidebarCollapsed}>
            {sidebar}
          </SidebarPanel>
          
          {!sidebarCollapsed && <ResizeHandle theme={currentTheme} />}
          
          <ChatPanel theme={currentTheme}>
            {chatWindow}
          </ChatPanel>
        </ContentArea>
      </MainContent>
    </DesktopContainer>
  );
};

export default DesktopLayout;
