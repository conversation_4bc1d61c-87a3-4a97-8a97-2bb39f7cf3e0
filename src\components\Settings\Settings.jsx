import React, { useState } from 'react';
import styled from 'styled-components';
import {
  FaArrowLeft,
  FaPalette,
  FaLock,
  FaBell,
  FaWrench,
  FaShieldAlt,
  FaCog,
  FaUserCog,
  FaGhost,
  FaPlane,
  FaBellSlash,
  FaHome,
  FaFont,
  FaCommentAlt,
  FaMobile,
  FaDesktop,
  FaUser,
  FaAddressBook,
  FaFolder,
  FaDatabase,
  FaDownload,
  FaUpload
} from 'react-icons/fa';
import ThemeSettings from './ThemeSettings';
import PrivacySettings from './PrivacySettings';
import GeneralSettings from './GeneralSettings';
import NotificationSettings from './NotificationSettings';
import SecuritySettings from './SecuritySettings';
import AdvancedSettings from './AdvancedSettings';
import DNDSettings from './DNDSettings';
import GhostModeSettings from './GhostModeSettings';
import AirplaneModeSettings from './AirplaneModeSettings';
import FontSettings from './FontSettings';
import ChatStyleSettings from './ChatStyleSettings';
import EnhancedProfileSettings from './ProfileSettings';
import EnhancedPrivacySettings from './EnhancedPrivacySettings';
import EnhancedNotificationSettings from './EnhancedNotificationSettings';
import DataStorageSettings from './DataStorageSettings';

const SettingsContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: ${props => props.theme.colors.background};
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
  transform: ${props => props.$isOpen ? 'translateX(0)' : 'translateX(-100%)'};

  @media (min-width: 768px) {
    width: 400px;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  }
`;

const SettingsHeader = styled.div`
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
`;

const BackButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  margin-right: 20px;
`;

const SettingsTitle = styled.h2`
  margin: 0;
  font-size: 1.2rem;
`;

const SettingsContent = styled.div`
  flex: 1;
  overflow-y: auto;
`;

const SettingsMenu = styled.div`
  display: flex;
  flex-direction: column;
`;

const MenuItem = styled.div`
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  cursor: pointer;

  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const MenuIcon = styled.div`
  margin-right: 16px;
  font-size: 1.2rem;
  color: ${props => props.theme.colors.icon};
`;

const MenuText = styled.div`
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const Settings = ({ isOpen, onClose, theme }) => {
  const [activeSection, setActiveSection] = useState(null);

  // Check for global navigation request
  React.useEffect(() => {
    if (isOpen && window.openSettingsWithSection) {
      setActiveSection(window.openSettingsWithSection);
      window.openSettingsWithSection = null;
    }
  }, [isOpen]);

  const handleBackClick = () => {
    if (activeSection) {
      setActiveSection(null);
    } else {
      onClose();
    }
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'theme':
        return <ThemeSettings theme={theme} />;
      case 'privacy':
        return <EnhancedPrivacySettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'general':
        return <GeneralSettings theme={theme} />;
      case 'notifications':
        return <EnhancedNotificationSettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'security':
        return <SecuritySettings theme={theme} />;
      case 'advanced':
        return <AdvancedSettings theme={theme} />;
      case 'profile':
        return <EnhancedProfileSettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'dnd':
        return <DNDSettings theme={theme} />;
      case 'ghost':
        return <GhostModeSettings theme={theme} />;
      case 'airplane':
        return <AirplaneModeSettings theme={theme} />;
      case 'fonts':
        return <FontSettings theme={theme} />;
      case 'chatStyle':
        return <ChatStyleSettings theme={theme} />;
      case 'storage':
        return <DataStorageSettings theme={theme} />;
      default:
        return (
          <SettingsMenu theme={theme}>
            <MenuItem theme={theme} onClick={() => setActiveSection('profile')}>
              <MenuIcon theme={theme}>
                <FaUser />
              </MenuIcon>
              <MenuText theme={theme}>Profile Settings</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('theme')}>
              <MenuIcon theme={theme}>
                <FaPalette />
              </MenuIcon>
              <MenuText theme={theme}>Themes and Appearance</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('notifications')}>
              <MenuIcon theme={theme}>
                <FaBell />
              </MenuIcon>
              <MenuText theme={theme}>Notifications</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('dnd')}>
              <MenuIcon theme={theme}>
                <FaBellSlash />
              </MenuIcon>
              <MenuText theme={theme}>Do Not Disturb</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('privacy')}>
              <MenuIcon theme={theme}>
                <FaLock />
              </MenuIcon>
              <MenuText theme={theme}>Privacy</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('ghost')}>
              <MenuIcon theme={theme}>
                <FaGhost />
              </MenuIcon>
              <MenuText theme={theme}>Ghost Mode</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('airplane')}>
              <MenuIcon theme={theme}>
                <FaPlane />
              </MenuIcon>
              <MenuText theme={theme}>Airplane Mode</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('security')}>
              <MenuIcon theme={theme}>
                <FaShieldAlt />
              </MenuIcon>
              <MenuText theme={theme}>Security</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('general')}>
              <MenuIcon theme={theme}>
                <FaUserCog />
              </MenuIcon>
              <MenuText theme={theme}>Account</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('fonts')}>
              <MenuIcon theme={theme}>
                <FaFont />
              </MenuIcon>
              <MenuText theme={theme}>Fonts</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('chatStyle')}>
              <MenuIcon theme={theme}>
                <FaCommentAlt />
              </MenuIcon>
              <MenuText theme={theme}>Chat Style</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('advanced')}>
              <MenuIcon theme={theme}>
                <FaCog />
              </MenuIcon>
              <MenuText theme={theme}>Advanced</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => window.handleNavigation && window.handleNavigation('contacts')}>
              <MenuIcon theme={theme}>
                <FaAddressBook />
              </MenuIcon>
              <MenuText theme={theme}>Contacts</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => window.handleNavigation && window.handleNavigation('files')}>
              <MenuIcon theme={theme}>
                <FaFolder />
              </MenuIcon>
              <MenuText theme={theme}>File Manager</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('storage')}>
              <MenuIcon theme={theme}>
                <FaDatabase />
              </MenuIcon>
              <MenuText theme={theme}>Data & Storage</MenuText>
            </MenuItem>
          </SettingsMenu>
        );
    }
  };

  return (
    <SettingsContainer $isOpen={isOpen} theme={theme}>
      <SettingsHeader theme={theme}>
        <BackButton onClick={handleBackClick}>
          <FaArrowLeft />
        </BackButton>
        <SettingsTitle>
          {activeSection ?
            activeSection.charAt(0).toUpperCase() + activeSection.slice(1) + ' Settings'
            : 'Settings'}
        </SettingsTitle>
      </SettingsHeader>
      <SettingsContent>
        {renderContent()}
      </SettingsContent>
    </SettingsContainer>
  );
};

export default Settings;
