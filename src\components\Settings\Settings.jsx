import React, { useState } from 'react';
import styled from 'styled-components';
import {
  FaArrowLeft,
  FaPalette,
  FaLock,
  FaBell,
  FaWrench,
  FaShieldAlt,
  FaCog,
  FaUserCog,
  FaGhost,
  FaPlane,
  FaBellSlash,
  FaHome,
  FaFont,
  FaCommentAlt,
  FaMobile,
  FaDesktop,
  FaUser,
  FaAddressBook,
  FaFolder,
  FaDatabase,
  FaDownload,
  FaUpload,
  FaLanguage,
  FaQuestionCircle,
  FaInfoCircle,
  FaSignOutAlt,
  FaBackup,
  FaCloud
} from 'react-icons/fa';
import ThemeSettings from './ThemeSettings';
import PrivacySettings from './PrivacySettings';
import GeneralSettings from './GeneralSettings';
import NotificationSettings from './NotificationSettings';
import SecuritySettings from './SecuritySettings';
import AdvancedSettings from './AdvancedSettings';
import DNDSettings from './DNDSettings';
import GhostModeSettings from './GhostModeSettings';
import AirplaneModeSettings from './AirplaneModeSettings';
import FontSettings from './FontSettings';
import ChatStyleSettings from './ChatStyleSettings';
import EnhancedProfileSettings from './ProfileSettings';
import EnhancedPrivacySettings from './EnhancedPrivacySettings';
import EnhancedNotificationSettings from './EnhancedNotificationSettings';
import DataStorageSettings from './DataStorageSettings';
import AppearanceHub from './AppearanceHub';

const SettingsContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: ${props => props.theme.colors.background};
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
  transform: ${props => props.$isOpen ? 'translateX(0)' : 'translateX(-100%)'};

  @media (min-width: 768px) {
    width: 400px;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  }
`;

const SettingsHeader = styled.div`
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
`;

const BackButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  margin-right: 20px;
`;

const SettingsTitle = styled.h2`
  margin: 0;
  font-size: 1.2rem;
`;

const SettingsContent = styled.div`
  flex: 1;
  overflow-y: auto;
`;

const SettingsMenu = styled.div`
  display: flex;
  flex-direction: column;
`;

const MenuItem = styled.div`
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  cursor: pointer;

  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const MenuIcon = styled.div`
  margin-right: 16px;
  font-size: 1.2rem;
  color: ${props => props.theme.colors.icon};
`;

const MenuText = styled.div`
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const SectionHeader = styled.div`
  padding: 16px 16px 8px 16px;
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.primary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background-color: ${props => props.theme.colors.secondary};
`;

const Settings = ({ isOpen, onClose, theme }) => {
  const [activeSection, setActiveSection] = useState(null);

  // Check for global navigation request
  React.useEffect(() => {
    if (isOpen && window.openSettingsWithSection) {
      setActiveSection(window.openSettingsWithSection);
      window.openSettingsWithSection = null;
    }
  }, [isOpen]);

  const handleBackClick = () => {
    if (activeSection) {
      setActiveSection(null);
    } else {
      onClose();
    }
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'theme':
        return <AppearanceHub
          onClose={() => setActiveSection(null)}
          onOpenThemes={() => setActiveSection('themeDetails')}
          onOpenFonts={() => setActiveSection('fonts')}
          onOpenChatStyle={() => setActiveSection('chatStyle')}
        />;
      case 'themeDetails':
        return <ThemeSettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'privacy':
        return <EnhancedPrivacySettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'general':
        return <GeneralSettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'notifications':
        return <EnhancedNotificationSettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'security':
        return <SecuritySettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'advanced':
        return <AdvancedSettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'profile':
        return <EnhancedProfileSettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'dnd':
        return <DNDSettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'ghost':
        return <GhostModeSettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'airplane':
        return <AirplaneModeSettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'fonts':
        return <FontSettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'chatStyle':
        return <ChatStyleSettings theme={theme} onClose={() => setActiveSection(null)} />;
      case 'storage':
        return <DataStorageSettings theme={theme} />;
      case 'language':
        return <div style={{padding: '20px', color: theme.colors.text}}>Language settings coming soon...</div>;
      case 'backup':
        return <div style={{padding: '20px', color: theme.colors.text}}>Backup & Restore settings coming soon...</div>;
      case 'help':
        return <div style={{padding: '20px', color: theme.colors.text}}>Help & Support coming soon...</div>;
      case 'about':
        return <div style={{padding: '20px', color: theme.colors.text}}>About GBChat v1.0.0</div>;
      default:
        return (
          <SettingsMenu theme={theme}>
            <SectionHeader theme={theme}>Profile & Account</SectionHeader>
            <MenuItem theme={theme} onClick={() => setActiveSection('profile')}>
              <MenuIcon theme={theme}>
                <FaUser />
              </MenuIcon>
              <MenuText theme={theme}>Profile Settings</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('general')}>
              <MenuIcon theme={theme}>
                <FaUserCog />
              </MenuIcon>
              <MenuText theme={theme}>Account Settings</MenuText>
            </MenuItem>

            <SectionHeader theme={theme}>Privacy & Security</SectionHeader>
            <MenuItem theme={theme} onClick={() => setActiveSection('privacy')}>
              <MenuIcon theme={theme}>
                <FaLock />
              </MenuIcon>
              <MenuText theme={theme}>Privacy & Security</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('security')}>
              <MenuIcon theme={theme}>
                <FaShieldAlt />
              </MenuIcon>
              <MenuText theme={theme}>Security Settings</MenuText>
            </MenuItem>

            <SectionHeader theme={theme}>Notifications</SectionHeader>
            <MenuItem theme={theme} onClick={() => setActiveSection('notifications')}>
              <MenuIcon theme={theme}>
                <FaBell />
              </MenuIcon>
              <MenuText theme={theme}>Notifications</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('dnd')}>
              <MenuIcon theme={theme}>
                <FaBellSlash />
              </MenuIcon>
              <MenuText theme={theme}>Do Not Disturb</MenuText>
            </MenuItem>

            <SectionHeader theme={theme}>Appearance</SectionHeader>
            <MenuItem theme={theme} onClick={() => setActiveSection('theme')}>
              <MenuIcon theme={theme}>
                <FaPalette />
              </MenuIcon>
              <MenuText theme={theme}>Themes & Appearance</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('fonts')}>
              <MenuIcon theme={theme}>
                <FaFont />
              </MenuIcon>
              <MenuText theme={theme}>Fonts & Typography</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('chatStyle')}>
              <MenuIcon theme={theme}>
                <FaCommentAlt />
              </MenuIcon>
              <MenuText theme={theme}>Chat Wallpaper & Style</MenuText>
            </MenuItem>

            <SectionHeader theme={theme}>Data & Files</SectionHeader>
            <MenuItem theme={theme} onClick={() => setActiveSection('storage')}>
              <MenuIcon theme={theme}>
                <FaDatabase />
              </MenuIcon>
              <MenuText theme={theme}>Data & Storage</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => {
              if (window.handleNavigation) {
                window.handleNavigation('contacts');
              }
            }}>
              <MenuIcon theme={theme}>
                <FaAddressBook />
              </MenuIcon>
              <MenuText theme={theme}>Contacts Manager</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => {
              if (window.handleNavigation) {
                window.handleNavigation('files');
              }
            }}>
              <MenuIcon theme={theme}>
                <FaFolder />
              </MenuIcon>
              <MenuText theme={theme}>File Manager</MenuText>
            </MenuItem>

            <SectionHeader theme={theme}>Advanced Features</SectionHeader>
            <MenuItem theme={theme} onClick={() => setActiveSection('ghost')}>
              <MenuIcon theme={theme}>
                <FaGhost />
              </MenuIcon>
              <MenuText theme={theme}>Ghost Mode</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('airplane')}>
              <MenuIcon theme={theme}>
                <FaPlane />
              </MenuIcon>
              <MenuText theme={theme}>Airplane Mode</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('advanced')}>
              <MenuIcon theme={theme}>
                <FaCog />
              </MenuIcon>
              <MenuText theme={theme}>Advanced Settings</MenuText>
            </MenuItem>

            <SectionHeader theme={theme}>System & Support</SectionHeader>
            <MenuItem theme={theme} onClick={() => setActiveSection('language')}>
              <MenuIcon theme={theme}>
                <FaLanguage />
              </MenuIcon>
              <MenuText theme={theme}>Language</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('backup')}>
              <MenuIcon theme={theme}>
                <FaCloud />
              </MenuIcon>
              <MenuText theme={theme}>Backup & Restore</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('help')}>
              <MenuIcon theme={theme}>
                <FaQuestionCircle />
              </MenuIcon>
              <MenuText theme={theme}>Help & Support</MenuText>
            </MenuItem>

            <MenuItem theme={theme} onClick={() => setActiveSection('about')}>
              <MenuIcon theme={theme}>
                <FaInfoCircle />
              </MenuIcon>
              <MenuText theme={theme}>About GBChat</MenuText>
            </MenuItem>
          </SettingsMenu>
        );
    }
  };

  return (
    <SettingsContainer $isOpen={isOpen} theme={theme}>
      <SettingsHeader theme={theme}>
        <BackButton onClick={handleBackClick}>
          <FaArrowLeft />
        </BackButton>
        <SettingsTitle>
          {activeSection ?
            activeSection.charAt(0).toUpperCase() + activeSection.slice(1) + ' Settings'
            : 'Settings'}
        </SettingsTitle>
      </SettingsHeader>
      <SettingsContent>
        {renderContent()}
      </SettingsContent>
    </SettingsContainer>
  );
};

export default Settings;
