import React from 'react';
import styled from 'styled-components';
import { usePrivacy } from '../../context/PrivacyContext';
import { useTheme } from '../../context/ThemeContext';

const PrivacySettingsContainer = styled.div`
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const SettingItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const SettingLabel = styled.div`
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
`;

const SettingDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 0.8rem;
  margin-top: 4px;
`;

const ToggleSwitch = styled.label`
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  flex-shrink: 0;
`;

const ToggleInput = styled.input`
  opacity: 0;
  width: 0;
  height: 0;

  &:checked + span {
    background-color: ${props => props.theme.colors.primary};
  }

  &:checked + span:before {
    transform: translateX(26px);
  }
`;

const ToggleSlider = styled.span`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;

  &:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }
`;

const SelectWrapper = styled.div`
  position: relative;
`;

const Select = styled.select`
  appearance: none;
  padding: 8px 32px 8px 12px;
  border-radius: 4px;
  border: 1px solid ${props => props.theme.colors.border};
  background-color: ${props => props.theme.colors.secondary};
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const SelectArrow = styled.div`
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  pointer-events: none;
  color: ${props => props.theme.colors.icon};
`;

const PrivacySettings = ({ theme }) => {
  const { privacySettings, updatePrivacySetting } = usePrivacy();
  const { currentTheme } = useTheme();
  const themeToUse = theme || currentTheme;

  const handleToggleChange = (setting) => {
    updatePrivacySetting(setting, !privacySettings[setting]);
  };

  const handleSelectChange = (setting, e) => {
    updatePrivacySetting(setting, e.target.value);
  };

  return (
    <PrivacySettingsContainer theme={themeToUse}>
      <SectionTitle theme={themeToUse}>Privacy</SectionTitle>

      <SettingItem theme={themeToUse}>
        <div>
          <SettingLabel theme={themeToUse}>Hide Online Status</SettingLabel>
          <SettingDescription theme={themeToUse}>
            Others won't see when you're online
          </SettingDescription>
        </div>
        <ToggleSwitch>
          <ToggleInput
            type="checkbox"
            checked={privacySettings.hideOnlineStatus}
            onChange={() => handleToggleChange('hideOnlineStatus')}
            theme={themeToUse}
          />
          <ToggleSlider />
        </ToggleSwitch>
      </SettingItem>

      <SettingItem theme={themeToUse}>
        <div>
          <SettingLabel theme={themeToUse}>Hide Read Receipts</SettingLabel>
          <SettingDescription theme={themeToUse}>
            Others won't see when you've read their messages
          </SettingDescription>
        </div>
        <ToggleSwitch>
          <ToggleInput
            type="checkbox"
            checked={privacySettings.hideReadReceipts}
            onChange={() => handleToggleChange('hideReadReceipts')}
            theme={themeToUse}
          />
          <ToggleSlider />
        </ToggleSwitch>
      </SettingItem>

      <SettingItem theme={themeToUse}>
        <div>
          <SettingLabel theme={themeToUse}>Hide Typing Indicator</SettingLabel>
          <SettingDescription theme={themeToUse}>
            Others won't see when you're typing
          </SettingDescription>
        </div>
        <ToggleSwitch>
          <ToggleInput
            type="checkbox"
            checked={privacySettings.hideTypingIndicator}
            onChange={() => handleToggleChange('hideTypingIndicator')}
            theme={themeToUse}
          />
          <ToggleSlider />
        </ToggleSwitch>
      </SettingItem>

      <SettingItem theme={themeToUse}>
        <div>
          <SettingLabel theme={themeToUse}>Show Deleted Messages</SettingLabel>
          <SettingDescription theme={themeToUse}>
            See messages that others have deleted
          </SettingDescription>
        </div>
        <ToggleSwitch>
          <ToggleInput
            type="checkbox"
            checked={privacySettings.showDeletedMessages}
            onChange={() => handleToggleChange('showDeletedMessages')}
            theme={themeToUse}
          />
          <ToggleSlider />
        </ToggleSwitch>
      </SettingItem>

      <SettingItem theme={themeToUse}>
        <div>
          <SettingLabel theme={themeToUse}>Last Seen</SettingLabel>
          <SettingDescription theme={themeToUse}>
            Who can see when you were last online
          </SettingDescription>
        </div>
        <SelectWrapper>
          <Select
            value={privacySettings.lastSeenPrivacy}
            onChange={(e) => handleSelectChange('lastSeenPrivacy', e)}
            theme={themeToUse}
          >
            <option value="everyone">Everyone</option>
            <option value="contacts">My Contacts</option>
            <option value="nobody">Nobody</option>
          </Select>
          <SelectArrow theme={themeToUse}>▼</SelectArrow>
        </SelectWrapper>
      </SettingItem>

      <SettingItem theme={themeToUse}>
        <div>
          <SettingLabel theme={themeToUse}>Profile Photo</SettingLabel>
          <SettingDescription theme={themeToUse}>
            Who can see your profile photo
          </SettingDescription>
        </div>
        <SelectWrapper>
          <Select
            value={privacySettings.profilePhotoPrivacy}
            onChange={(e) => handleSelectChange('profilePhotoPrivacy', e)}
            theme={themeToUse}
          >
            <option value="everyone">Everyone</option>
            <option value="contacts">My Contacts</option>
            <option value="nobody">Nobody</option>
          </Select>
          <SelectArrow theme={themeToUse}>▼</SelectArrow>
        </SelectWrapper>
      </SettingItem>

      <SettingItem theme={themeToUse}>
        <div>
          <SettingLabel theme={themeToUse}>Status</SettingLabel>
          <SettingDescription theme={themeToUse}>
            Who can see your status updates
          </SettingDescription>
        </div>
        <SelectWrapper>
          <Select
            value={privacySettings.statusPrivacy}
            onChange={(e) => handleSelectChange('statusPrivacy', e)}
            theme={themeToUse}
          >
            <option value="everyone">Everyone</option>
            <option value="contacts">My Contacts</option>
            <option value="nobody">Nobody</option>
          </Select>
          <SelectArrow theme={themeToUse}>▼</SelectArrow>
        </SelectWrapper>
      </SettingItem>
    </PrivacySettingsContainer>
  );
};

export default PrivacySettings;
