import React, { useState } from 'react';
import styled from 'styled-components';
import {
  FaArrowLeft,
  FaCamera,
  FaUser,
  FaInfoCircle,
  FaPhone,
  FaEnvelope,
  FaCheck,
  FaEllipsisV,
  FaVideo,
  FaGhost,
  FaSmile,
  FaGrin,
  <PERSON><PERSON><PERSON><PERSON>,
  FaSadTear,
  FaAngry,
  FaHeart
} from 'react-icons/fa';
import { useAuth } from '../../context/AuthContext';

const ProfileContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: ${props => props.theme.colors.background};
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
  transform: ${props => props.$isOpen ? 'translateX(0)' : 'translateX(100%)'};
`;

const ProfileHeader = styled.div`
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
`;

const BackButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  margin-right: 20px;
`;

const HeaderTitle = styled.h2`
  margin: 0;
  font-size: 1.2rem;
`;

const ProfileContent = styled.div`
  flex: 1;
  overflow-y: auto;
`;

const ProfileImageSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 0;
  background-color: ${props => props.theme.colors.secondary};
`;

const ProfileImageWrapper = styled.div`
  position: relative;
  width: 150px;
  height: 150px;
  margin-bottom: 15px;
`;

const ProfileImage = styled.div`
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.border};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.theme.colors.secondaryText};
  font-size: 60px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const CameraButton = styled.div`
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
`;

const ProfileName = styled.div`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const ProfileInfoSection = styled.div`
  padding: 20px;
`;

const SectionTitle = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.primary};
  margin-bottom: 15px;
  font-weight: 500;
`;

const InfoItem = styled.div`
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid ${props => props.theme.colors.border};

  &:last-child {
    border-bottom: none;
  }
`;

const InfoIcon = styled.div`
  width: 40px;
  color: ${props => props.theme.colors.secondaryText};
  font-size: 18px;
`;

const InfoContent = styled.div`
  flex: 1;
`;

const InfoLabel = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.secondaryText};
  margin-bottom: 5px;
`;

const InfoValue = styled.div`
  font-size: 16px;
  color: ${props => props.theme.colors.text};
`;

const EditButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.primary};
  font-size: 14px;
  cursor: pointer;
  padding: 5px 10px;

  &:hover {
    text-decoration: underline;
  }
`;

const SaveButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: 5px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin: 30px auto;
  width: 90%;
  max-width: 300px;

  &:hover {
    opacity: 0.9;
  }
`;

const EditInput = styled.input`
  width: 100%;
  padding: 8px 0;
  border: none;
  border-bottom: 2px solid ${props => props.theme.colors.primary};
  background-color: transparent;
  color: ${props => props.theme.colors.text};
  font-size: 16px;
  outline: none;
`;

const MoreOptionsButton = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
  color: ${props => props.theme.colors.text};
`;

const GhostModeToggle = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background-color: ${props => props.theme.colors.secondary};
  color: ${props => props.theme.colors.text};
  border-radius: 8px;
  margin: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const GhostModeLabel = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;

  svg {
    color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
    font-size: 18px;
  }
`;

const ToggleSwitch = styled.div`
  position: relative;
  width: 50px;
  height: 24px;
  background-color: ${props => props.$active ? props.theme.colors.primary : '#ccc'};
  border-radius: 12px;
  cursor: pointer;
  transition: background-color 0.3s;

  &:after {
    content: '';
    position: absolute;
    top: 2px;
    left: ${props => props.$active ? '26px' : '2px'};
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: white;
    transition: left 0.3s;
  }
`;

const VideoSection = styled.div`
  padding: 20px;
  text-align: center;
`;

const EmojiPickerWrapper = styled.div`
  position: absolute;
  bottom: 70px;
  right: 20px;
  z-index: 1000;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 10px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
`;

const EmojiButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const ProfileSettings = ({ isOpen, onClose, theme, customContent, title = 'Profile' }) => {
  const { currentUser, login } = useAuth();

  const [editMode, setEditMode] = useState({
    name: false,
    about: false,
    phone: false,
    email: false
  });

  const [profileData, setProfileData] = useState({
    name: currentUser?.name || 'User Name',
    about: 'Available',
    phone: '****** 567 8900',
    email: currentUser?.email || '<EMAIL>',
    image: null
  });

  const [tempData, setTempData] = useState({...profileData});
  const [ghostMode, setGhostMode] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [videoFile, setVideoFile] = useState(null);

  const handleEdit = (field) => {
    setEditMode({...editMode, [field]: true});
    setTempData({...profileData});
  };

  const handleChange = (field, value) => {
    setTempData({...tempData, [field]: value});
  };

  const handleSave = (field) => {
    setProfileData({...profileData, [field]: tempData[field]});
    setEditMode({...editMode, [field]: false});
  };

  const handleCancel = (field) => {
    setEditMode({...editMode, [field]: false});
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        setProfileData({...profileData, image: event.target.result});
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSaveAll = () => {
    if (window.confirm('Are you sure you want to save all changes?')) {
      // Update user data in context
      login({
        ...currentUser,
        name: profileData.name,
        email: profileData.email
      });
      onClose();
    }
  };

  const handleGhostModeToggle = () => {
    setGhostMode(!ghostMode);
  };

  const handleVideoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setVideoFile(URL.createObjectURL(file));
    }
  };

  const handleEmojiSelect = (emoji) => {
    setTempData({ ...tempData, about: tempData.about + ' ' + emoji });
    setShowEmojiPicker(false);
  };

  // Common emojis
  const emojis = ['😊', '😂', '❤️', '👍', '🎉', '🔥', '✨', '🙌', '🤔', '😎'];

  return (
    <ProfileContainer $isOpen={isOpen} theme={theme}>
      <ProfileHeader theme={theme}>
        <BackButton onClick={onClose}>
          <FaArrowLeft />
        </BackButton>
        <HeaderTitle>{title}</HeaderTitle>
        <MoreOptionsButton theme={theme}>
          <FaEllipsisV />
        </MoreOptionsButton>
      </ProfileHeader>

      <ProfileContent>
        {customContent ? (
          customContent
        ) : (
          <>
            <ProfileImageSection theme={theme}>
          <ProfileImageWrapper>
            <ProfileImage theme={theme}>
              {profileData.image ? (
                <img src={profileData.image} alt="Profile" />
              ) : (
                <FaUser />
              )}
            </ProfileImage>
            <CameraButton theme={theme} title="Upload a new profile picture">
              <label htmlFor="profile-image" style={{ cursor: 'pointer' }}>
                <FaCamera />
              </label>
              <input
                id="profile-image"
                type="file"
                accept="image/*"
                style={{ display: 'none' }}
                onChange={handleImageChange}
              />
            </CameraButton>
          </ProfileImageWrapper>
          <ProfileName theme={theme}>{profileData.name}</ProfileName>
        </ProfileImageSection>

        <ProfileInfoSection>
          <SectionTitle theme={theme}>Personal Information</SectionTitle>

          <InfoItem theme={theme}>
            <InfoIcon theme={theme}>
              <FaUser />
            </InfoIcon>
            <InfoContent>
              <InfoLabel theme={theme}>Name</InfoLabel>
              {editMode.name ? (
                <EditInput
                  theme={theme}
                  value={tempData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  autoFocus
                />
              ) : (
                <InfoValue theme={theme}>{profileData.name}</InfoValue>
              )}
            </InfoContent>
            {editMode.name ? (
              <>
                <EditButton theme={theme} onClick={() => handleSave('name')}>Save</EditButton>
                <EditButton theme={theme} onClick={() => handleCancel('name')}>Cancel</EditButton>
              </>
            ) : (
              <EditButton theme={theme} onClick={() => handleEdit('name')}>Edit</EditButton>
            )}
          </InfoItem>

          <InfoItem theme={theme}>
            <InfoIcon theme={theme}>
              <FaInfoCircle />
            </InfoIcon>
            <InfoContent>
              <InfoLabel theme={theme}>About</InfoLabel>
              {editMode.about ? (
                <EditInput
                  theme={theme}
                  value={tempData.about}
                  onChange={(e) => handleChange('about', e.target.value)}
                  autoFocus
                />
              ) : (
                <InfoValue theme={theme}>{profileData.about}</InfoValue>
              )}
            </InfoContent>
            {editMode.about ? (
              <>
                <EditButton theme={theme} onClick={() => handleSave('about')}>Save</EditButton>
                <EditButton theme={theme} onClick={() => handleCancel('about')}>Cancel</EditButton>
              </>
            ) : (
              <EditButton theme={theme} onClick={() => handleEdit('about')}>Edit</EditButton>
            )}
          </InfoItem>

          <InfoItem theme={theme}>
            <InfoIcon theme={theme}>
              <FaPhone />
            </InfoIcon>
            <InfoContent>
              <InfoLabel theme={theme}>Phone</InfoLabel>
              {editMode.phone ? (
                <EditInput
                  theme={theme}
                  value={tempData.phone}
                  onChange={(e) => handleChange('phone', e.target.value)}
                  autoFocus
                />
              ) : (
                <InfoValue theme={theme}>{profileData.phone}</InfoValue>
              )}
            </InfoContent>
            {editMode.phone ? (
              <>
                <EditButton theme={theme} onClick={() => handleSave('phone')}>Save</EditButton>
                <EditButton theme={theme} onClick={() => handleCancel('phone')}>Cancel</EditButton>
              </>
            ) : (
              <EditButton theme={theme} onClick={() => handleEdit('phone')}>Edit</EditButton>
            )}
          </InfoItem>

          <InfoItem theme={theme}>
            <InfoIcon theme={theme}>
              <FaEnvelope />
            </InfoIcon>
            <InfoContent>
              <InfoLabel theme={theme}>Email</InfoLabel>
              {editMode.email ? (
                <EditInput
                  theme={theme}
                  value={tempData.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  autoFocus
                />
              ) : (
                <InfoValue theme={theme}>{profileData.email}</InfoValue>
              )}
            </InfoContent>
            {editMode.email ? (
              <>
                <EditButton theme={theme} onClick={() => handleSave('email')}>Save</EditButton>
                <EditButton theme={theme} onClick={() => handleCancel('email')}>Cancel</EditButton>
              </>
            ) : (
              <EditButton theme={theme} onClick={() => handleEdit('email')}>Edit</EditButton>
            )}
          </InfoItem>
        </ProfileInfoSection>

        <GhostModeToggle theme={theme}>
          <GhostModeLabel $active={ghostMode} theme={theme}>
            <FaGhost /> Ghost Mode
          </GhostModeLabel>
          <ToggleSwitch
            $active={ghostMode}
            theme={theme}
            onClick={handleGhostModeToggle}
          />
        </GhostModeToggle>

        <VideoSection theme={theme}>
          <label htmlFor="video-upload">
            <FaVideo /> Upload Video
          </label>
          <input
            id="video-upload"
            type="file"
            accept="video/*"
            style={{ display: 'none' }}
            onChange={handleVideoUpload}
          />
          {videoFile && <video src={videoFile} controls width="100%" />}
        </VideoSection>

        {showEmojiPicker && (
          <EmojiPickerWrapper theme={theme}>
            {emojis.map((emoji, index) => (
              <EmojiButton
                key={index}
                onClick={() => handleEmojiSelect(emoji)}
                theme={theme}
              >
                {emoji}
              </EmojiButton>
            ))}
          </EmojiPickerWrapper>
        )}

        <EditButton theme={theme} onClick={() => setShowEmojiPicker(!showEmojiPicker)}>
          <FaSmile /> Add Emoji
        </EditButton>

        <SaveButton theme={theme} onClick={handleSaveAll}>
          <FaCheck />
          Save Changes
        </SaveButton>
          </>
        )}
      </ProfileContent>
    </ProfileContainer>
  );
};

export default ProfileSettings;
