import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaTimes, 
  FaLock, 
  FaEye,
  FaEyeSlash,
  FaUserSecret,
  FaShieldAlt,
  FaClock,
  FaTrash,
  FaUserSlash,
  FaPhoneSlash,
  FaCommentSlash,
  FaImage,
  FaVideo,
  FaFileAlt,
  FaLocationArrow,
  FaFingerprint,
  FaKey,
  FaMobile,
  FaDesktop,
  FaHistory,
  FaExclamationTriangle,
  FaCheckCircle,
  FaTimesCircle,
  FaChevronRight
} from 'react-icons/fa';
import storage from '../../utils/storage';
import MobileHaptics from '../Mobile/MobileHaptics';

const PrivacyContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${props => props.theme.colors.background};
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
`;

const PrivacyHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  min-height: 60px;
  position: sticky;
  top: 0;
  z-index: 10;
`;

const HeaderTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
`;

const HeaderButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const PrivacyContent = styled.div`
  flex: 1;
  padding: 0;
`;

const PrivacySection = styled.div`
  margin-bottom: 8px;
  background-color: ${props => props.theme.colors.background};
`;

const SectionHeader = styled.div`
  padding: 16px 16px 8px 16px;
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.primary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const PrivacyItem = styled.div`
  display: flex;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
  
  &:last-child {
    border-bottom: none;
  }
`;

const PrivacyIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  margin-right: 16px;
`;

const PrivacyInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const PrivacyTitle = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 2px;
`;

const PrivacySubtitle = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.secondaryText};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const PrivacyAction = styled.div`
  display: flex;
  align-items: center;
  color: ${props => props.theme.colors.secondaryText};
`;

const Toggle = styled.div`
  width: 50px;
  height: 28px;
  border-radius: 14px;
  background-color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.border};
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &::after {
    content: '';
    position: absolute;
    top: 2px;
    left: ${props => props.$active ? '24px' : '2px'};
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
`;

const StatusBadge = styled.div`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background-color: ${props => {
    if (props.$status === 'active') return '#4caf50';
    if (props.$status === 'warning') return '#ff9800';
    if (props.$status === 'danger') return '#f44336';
    return props.theme.colors.border;
  }};
  color: white;
`;

const SecurityAlert = styled.div`
  margin: 16px;
  padding: 16px;
  border-radius: 12px;
  background-color: ${props => props.$type === 'warning' ? '#fff3cd' : '#d1ecf1'};
  border: 1px solid ${props => props.$type === 'warning' ? '#ffeaa7' : '#bee5eb'};
  display: flex;
  align-items: flex-start;
  gap: 12px;
`;

const AlertIcon = styled.div`
  color: ${props => props.$type === 'warning' ? '#856404' : '#0c5460'};
  font-size: 20px;
  margin-top: 2px;
`;

const AlertContent = styled.div`
  flex: 1;
`;

const AlertTitle = styled.div`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.$type === 'warning' ? '#856404' : '#0c5460'};
  margin-bottom: 4px;
`;

const AlertText = styled.div`
  font-size: 13px;
  color: ${props => props.$type === 'warning' ? '#856404' : '#0c5460'};
  line-height: 1.4;
`;

const EnhancedPrivacySettings = ({ theme, onClose }) => {
  const [privacySettings, setPrivacySettings] = useState({
    lastSeen: true,
    profilePhoto: true,
    about: true,
    status: true,
    readReceipts: true,
    groups: 'everyone',
    liveLocation: false,
    calls: 'everyone',
    blockedContacts: [],
    disappearingMessages: false,
    disappearingTime: 24,
    twoStepVerification: false,
    fingerprintLock: false,
    autoDownload: {
      photos: true,
      videos: false,
      documents: false
    },
    backupEncryption: true
  });

  const [securityStatus, setSecurityStatus] = useState({
    twoFactor: false,
    fingerprint: false,
    activeDevices: 1,
    lastBackup: null
  });

  useEffect(() => {
    loadPrivacySettings();
    loadSecurityStatus();
  }, []);

  const loadPrivacySettings = () => {
    const settings = storage.getSection('privacy') || {};
    setPrivacySettings(prev => ({ ...prev, ...settings }));
  };

  const loadSecurityStatus = () => {
    const security = storage.getSection('security') || {};
    setSecurityStatus(prev => ({ ...prev, ...security }));
  };

  const updatePrivacySetting = (key, value) => {
    const newSettings = { ...privacySettings, [key]: value };
    setPrivacySettings(newSettings);
    storage.updateSection('privacy', newSettings);
    MobileHaptics.light();
  };

  const updateNestedSetting = (parent, key, value) => {
    const newSettings = {
      ...privacySettings,
      [parent]: {
        ...privacySettings[parent],
        [key]: value
      }
    };
    setPrivacySettings(newSettings);
    storage.updateSection('privacy', newSettings);
    MobileHaptics.light();
  };

  const getSecurityScore = () => {
    let score = 0;
    if (privacySettings.twoStepVerification) score += 25;
    if (privacySettings.fingerprintLock) score += 25;
    if (privacySettings.backupEncryption) score += 25;
    if (privacySettings.readReceipts === false) score += 25;
    return score;
  };

  return (
    <PrivacyContainer theme={theme}>
      <PrivacyHeader theme={theme}>
        <HeaderButton onClick={onClose}>
          <FaTimes />
        </HeaderButton>
        <HeaderTitle>Privacy & Security</HeaderTitle>
        <HeaderButton>
          <FaShieldAlt />
        </HeaderButton>
      </PrivacyHeader>

      <PrivacyContent>
        <SecurityAlert $type={getSecurityScore() < 50 ? 'warning' : 'info'}>
          <AlertIcon $type={getSecurityScore() < 50 ? 'warning' : 'info'}>
            {getSecurityScore() < 50 ? <FaExclamationTriangle /> : <FaCheckCircle />}
          </AlertIcon>
          <AlertContent>
            <AlertTitle $type={getSecurityScore() < 50 ? 'warning' : 'info'}>
              Security Score: {getSecurityScore()}%
            </AlertTitle>
            <AlertText $type={getSecurityScore() < 50 ? 'warning' : 'info'}>
              {getSecurityScore() < 50 
                ? 'Your account security can be improved. Enable two-step verification and fingerprint lock.'
                : 'Your account has good security settings enabled.'
              }
            </AlertText>
          </AlertContent>
        </SecurityAlert>

        <PrivacySection theme={theme}>
          <SectionHeader theme={theme}>Who can see my personal info</SectionHeader>
          
          <PrivacyItem theme={theme}>
            <PrivacyIcon color="#2196f3" theme={theme}>
              <FaClock />
            </PrivacyIcon>
            <PrivacyInfo>
              <PrivacyTitle theme={theme}>Last Seen</PrivacyTitle>
              <PrivacySubtitle theme={theme}>
                {privacySettings.lastSeen ? 'Everyone' : 'Nobody'}
              </PrivacySubtitle>
            </PrivacyInfo>
            <PrivacyAction theme={theme}>
              <Toggle 
                $active={privacySettings.lastSeen}
                onClick={() => updatePrivacySetting('lastSeen', !privacySettings.lastSeen)}
                theme={theme}
              />
            </PrivacyAction>
          </PrivacyItem>

          <PrivacyItem theme={theme}>
            <PrivacyIcon color="#9c27b0" theme={theme}>
              <FaImage />
            </PrivacyIcon>
            <PrivacyInfo>
              <PrivacyTitle theme={theme}>Profile Photo</PrivacyTitle>
              <PrivacySubtitle theme={theme}>
                {privacySettings.profilePhoto ? 'Everyone' : 'My contacts'}
              </PrivacySubtitle>
            </PrivacyInfo>
            <PrivacyAction theme={theme}>
              <Toggle 
                $active={privacySettings.profilePhoto}
                onClick={() => updatePrivacySetting('profilePhoto', !privacySettings.profilePhoto)}
                theme={theme}
              />
            </PrivacyAction>
          </PrivacyItem>

          <PrivacyItem theme={theme}>
            <PrivacyIcon color="#4caf50" theme={theme}>
              <FaFileAlt />
            </PrivacyIcon>
            <PrivacyInfo>
              <PrivacyTitle theme={theme}>About</PrivacyTitle>
              <PrivacySubtitle theme={theme}>
                {privacySettings.about ? 'Everyone' : 'My contacts'}
              </PrivacySubtitle>
            </PrivacyInfo>
            <PrivacyAction theme={theme}>
              <Toggle 
                $active={privacySettings.about}
                onClick={() => updatePrivacySetting('about', !privacySettings.about)}
                theme={theme}
              />
            </PrivacyAction>
          </PrivacyItem>

          <PrivacyItem theme={theme}>
            <PrivacyIcon color="#ff9800" theme={theme}>
              <FaEye />
            </PrivacyIcon>
            <PrivacyInfo>
              <PrivacyTitle theme={theme}>Read Receipts</PrivacyTitle>
              <PrivacySubtitle theme={theme}>
                {privacySettings.readReceipts ? 'Enabled' : 'Disabled'}
              </PrivacySubtitle>
            </PrivacyInfo>
            <PrivacyAction theme={theme}>
              <Toggle 
                $active={privacySettings.readReceipts}
                onClick={() => updatePrivacySetting('readReceipts', !privacySettings.readReceipts)}
                theme={theme}
              />
            </PrivacyAction>
          </PrivacyItem>
        </PrivacySection>

        <PrivacySection theme={theme}>
          <SectionHeader theme={theme}>Security</SectionHeader>
          
          <PrivacyItem theme={theme}>
            <PrivacyIcon color="#f44336" theme={theme}>
              <FaKey />
            </PrivacyIcon>
            <PrivacyInfo>
              <PrivacyTitle theme={theme}>Two-Step Verification</PrivacyTitle>
              <PrivacySubtitle theme={theme}>
                {privacySettings.twoStepVerification ? 'Enabled' : 'Disabled'}
              </PrivacySubtitle>
            </PrivacyInfo>
            <PrivacyAction theme={theme}>
              <StatusBadge $status={privacySettings.twoStepVerification ? 'active' : 'danger'}>
                {privacySettings.twoStepVerification ? 'ON' : 'OFF'}
              </StatusBadge>
            </PrivacyAction>
          </PrivacyItem>

          <PrivacyItem theme={theme}>
            <PrivacyIcon color="#673ab7" theme={theme}>
              <FaFingerprint />
            </PrivacyIcon>
            <PrivacyInfo>
              <PrivacyTitle theme={theme}>Fingerprint Lock</PrivacyTitle>
              <PrivacySubtitle theme={theme}>
                {privacySettings.fingerprintLock ? 'Enabled' : 'Disabled'}
              </PrivacySubtitle>
            </PrivacyInfo>
            <PrivacyAction theme={theme}>
              <Toggle 
                $active={privacySettings.fingerprintLock}
                onClick={() => updatePrivacySetting('fingerprintLock', !privacySettings.fingerprintLock)}
                theme={theme}
              />
            </PrivacyAction>
          </PrivacyItem>

          <PrivacyItem theme={theme}>
            <PrivacyIcon color="#607d8b" theme={theme}>
              <FaDesktop />
            </PrivacyIcon>
            <PrivacyInfo>
              <PrivacyTitle theme={theme}>Active Sessions</PrivacyTitle>
              <PrivacySubtitle theme={theme}>
                {securityStatus.activeDevices} device(s) active
              </PrivacySubtitle>
            </PrivacyInfo>
            <PrivacyAction theme={theme}>
              <FaChevronRight />
            </PrivacyAction>
          </PrivacyItem>
        </PrivacySection>

        <PrivacySection theme={theme}>
          <SectionHeader theme={theme}>Auto-Download</SectionHeader>
          
          <PrivacyItem theme={theme}>
            <PrivacyIcon color="#4caf50" theme={theme}>
              <FaImage />
            </PrivacyIcon>
            <PrivacyInfo>
              <PrivacyTitle theme={theme}>Photos</PrivacyTitle>
              <PrivacySubtitle theme={theme}>
                {privacySettings.autoDownload?.photos ? 'Enabled' : 'Disabled'}
              </PrivacySubtitle>
            </PrivacyInfo>
            <PrivacyAction theme={theme}>
              <Toggle 
                $active={privacySettings.autoDownload?.photos}
                onClick={() => updateNestedSetting('autoDownload', 'photos', !privacySettings.autoDownload?.photos)}
                theme={theme}
              />
            </PrivacyAction>
          </PrivacyItem>

          <PrivacyItem theme={theme}>
            <PrivacyIcon color="#ff9800" theme={theme}>
              <FaVideo />
            </PrivacyIcon>
            <PrivacyInfo>
              <PrivacyTitle theme={theme}>Videos</PrivacyTitle>
              <PrivacySubtitle theme={theme}>
                {privacySettings.autoDownload?.videos ? 'Enabled' : 'Disabled'}
              </PrivacySubtitle>
            </PrivacyInfo>
            <PrivacyAction theme={theme}>
              <Toggle 
                $active={privacySettings.autoDownload?.videos}
                onClick={() => updateNestedSetting('autoDownload', 'videos', !privacySettings.autoDownload?.videos)}
                theme={theme}
              />
            </PrivacyAction>
          </PrivacyItem>

          <PrivacyItem theme={theme}>
            <PrivacyIcon color="#2196f3" theme={theme}>
              <FaFileAlt />
            </PrivacyIcon>
            <PrivacyInfo>
              <PrivacyTitle theme={theme}>Documents</PrivacyTitle>
              <PrivacySubtitle theme={theme}>
                {privacySettings.autoDownload?.documents ? 'Enabled' : 'Disabled'}
              </PrivacySubtitle>
            </PrivacyInfo>
            <PrivacyAction theme={theme}>
              <Toggle 
                $active={privacySettings.autoDownload?.documents}
                onClick={() => updateNestedSetting('autoDownload', 'documents', !privacySettings.autoDownload?.documents)}
                theme={theme}
              />
            </PrivacyAction>
          </PrivacyItem>
        </PrivacySection>
      </PrivacyContent>
    </PrivacyContainer>
  );
};

export default EnhancedPrivacySettings;
