import React, { useState } from 'react';
import styled from 'styled-components';
import {
  FaBellSlash,
  FaClock,
  FaCalendarAlt,
  FaToggleOn,
  FaToggleOff,
  FaMoon,
  FaSun,
  FaUserFriends,
  FaExclamationCircle,
  FaPhone
} from 'react-icons/fa';

const DNDSettingsContainer = styled.div`
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const SettingsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const SettingItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const SettingInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const IconWrapper = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary}20;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    color: ${props => props.color || props.theme.colors.primary};
    font-size: 18px;
  }
`;

const SettingText = styled.div`
  display: flex;
  flex-direction: column;
`;

const SettingName = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 0.95rem;
`;

const SettingDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 0.8rem;
  margin-top: 4px;
`;

const ToggleButton = styled.div`
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
  font-size: 1.5rem;
  cursor: pointer;
`;

const TimeSelector = styled.div`
  display: flex;
  gap: 10px;
  align-items: center;
`;

const TimeInput = styled.input`
  padding: 8px;
  border-radius: 4px;
  border: 1px solid ${props => props.theme.colors.border};
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  width: 80px;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const TimeSeparator = styled.span`
  color: ${props => props.theme.colors.text};
`;

const ExceptionsList = styled.div`
  margin-top: 20px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const ExceptionsTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  margin-bottom: 12px;

  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const ExceptionItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid ${props => props.theme.colors.border};

  &:last-child {
    border-bottom: none;
  }
`;

const ExceptionName = styled.div`
  color: ${props => props.theme.colors.text};
  font-size: 14px;
`;

const RemoveButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.danger || '#e74c3c'};
  cursor: pointer;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;

  &:hover {
    background-color: ${props => props.theme.colors.danger || '#e74c3c'}10;
  }
`;

const AddExceptionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 10px;
  margin-top: 10px;
  background-color: ${props => props.theme.colors.secondary};
  border: 1px dashed ${props => props.theme.colors.border};
  border-radius: 4px;
  color: ${props => props.theme.colors.text};
  cursor: pointer;

  &:hover {
    background-color: ${props => props.theme.colors.primary}10;
  }
`;

const DNDSettings = ({ theme }) => {
  const [settings, setSettings] = useState({
    dndEnabled: false,
    scheduleEnabled: false,
    startTime: '22:00',
    endTime: '07:00',
    weekendsOnly: false,
    allowPriorityNotifications: true,
    allowRepeatedCalls: true
  });

  const [exceptions] = useState([
    { id: 1, name: 'Family Group' },
    { id: 2, name: 'Work Colleagues' },
    { id: 3, name: 'John Smith' }
  ]);

  const toggleSetting = (setting) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  const handleTimeChange = (setting, value) => {
    setSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  return (
    <DNDSettingsContainer theme={theme}>
      <SectionTitle theme={theme}>Do Not Disturb</SectionTitle>

      <SettingsList>
        <SettingItem theme={theme} onClick={() => toggleSetting('dndEnabled')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#E91E63">
              <FaBellSlash />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Do Not Disturb</SettingName>
              <SettingDescription theme={theme}>
                Mute all notifications
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton
            $active={settings.dndEnabled}
            theme={theme}
          >
            {settings.dndEnabled ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>

        <SettingItem theme={theme} onClick={() => toggleSetting('scheduleEnabled')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#9C27B0">
              <FaClock />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Schedule</SettingName>
              <SettingDescription theme={theme}>
                Set a specific time for DND mode
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton
            $active={settings.scheduleEnabled}
            theme={theme}
          >
            {settings.scheduleEnabled ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>

        {settings.scheduleEnabled && (
          <SettingItem theme={theme}>
            <SettingInfo>
              <IconWrapper theme={theme} color="#FF9800">
                <FaMoon />
              </IconWrapper>
              <SettingText>
                <SettingName theme={theme}>Quiet hours</SettingName>
                <SettingDescription theme={theme}>
                  Set your do not disturb schedule
                </SettingDescription>
              </SettingText>
            </SettingInfo>
            <TimeSelector>
              <TimeInput
                type="time"
                value={settings.startTime}
                onChange={(e) => handleTimeChange('startTime', e.target.value)}
                theme={theme}
              />
              <TimeSeparator theme={theme}>to</TimeSeparator>
              <TimeInput
                type="time"
                value={settings.endTime}
                onChange={(e) => handleTimeChange('endTime', e.target.value)}
                theme={theme}
              />
            </TimeSelector>
          </SettingItem>
        )}

        <SettingItem theme={theme} onClick={() => toggleSetting('weekendsOnly')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#4CAF50">
              <FaCalendarAlt />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Weekends only</SettingName>
              <SettingDescription theme={theme}>
                Apply DND only on weekends
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton
            $active={settings.weekendsOnly}
            theme={theme}
          >
            {settings.weekendsOnly ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>

        <SettingItem theme={theme} onClick={() => toggleSetting('allowPriorityNotifications')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#2196F3">
              <FaExclamationCircle />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Priority notifications</SettingName>
              <SettingDescription theme={theme}>
                Allow notifications from priority contacts
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton
            $active={settings.allowPriorityNotifications}
            theme={theme}
          >
            {settings.allowPriorityNotifications ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>

        <SettingItem theme={theme} onClick={() => toggleSetting('allowRepeatedCalls')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#607D8B">
              <FaPhone />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Repeated calls</SettingName>
              <SettingDescription theme={theme}>
                Allow repeated calls within 3 minutes
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton
            $active={settings.allowRepeatedCalls}
            theme={theme}
          >
            {settings.allowRepeatedCalls ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
      </SettingsList>

      <ExceptionsList theme={theme}>
        <ExceptionsTitle theme={theme}>
          <FaUserFriends />
          Exceptions
        </ExceptionsTitle>

        {exceptions.map(exception => (
          <ExceptionItem key={exception.id} theme={theme}>
            <ExceptionName theme={theme}>{exception.name}</ExceptionName>
            <RemoveButton theme={theme}>Remove</RemoveButton>
          </ExceptionItem>
        ))}

        <AddExceptionButton theme={theme}>
          + Add Exception
        </AddExceptionButton>
      </ExceptionsList>
    </DNDSettingsContainer>
  );
};

export default DNDSettings;
