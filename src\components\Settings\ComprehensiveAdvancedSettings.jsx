import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  <PERSON>a<PERSON>ser, 
  FaCog, 
  FaBell, 
  FaLock, 
  FaDatabase,
  FaDownload,
  FaUpload,
  FaTrash,
  FaPalette,
  FaLanguage,
  FaShieldAlt,
  FaEye,
  FaEyeSlash,
  FaCamera,
  FaMicrophone,
  FaLocationArrow,
  FaWifi,
  FaBluetooth,
  FaVolumeUp,
  FaMobile,
  FaDesktop,
  FaTabletAlt,
  FaChevronRight,
  FaTimes,
  FaCheck,
  FaEdit,
  FaImage,
  FaAddressBook,
  FaFolder,
  FaFont,
  FaCommentAlt,
  FaGhost,
  FaPlane,
  FaBellSlash
} from 'react-icons/fa';
import storage from '../../utils/storage';
import { useTheme } from '../../context/ThemeContext';
import MobileHaptics from '../Mobile/MobileHaptics';

const SettingsContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${props => props.theme.colors.background};
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
`;

const SettingsHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  min-height: 60px;
  position: sticky;
  top: 0;
  z-index: 10;
`;

const HeaderTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
`;

const HeaderButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const SettingsContent = styled.div`
  flex: 1;
  padding: 0;
`;

const SettingsSection = styled.div`
  margin-bottom: 8px;
  background-color: ${props => props.theme.colors.background};
`;

const SectionHeader = styled.div`
  padding: 16px 16px 8px 16px;
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.primary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const SettingsItem = styled.div`
  display: flex;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
  
  &:last-child {
    border-bottom: none;
  }
`;

const SettingsIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  margin-right: 16px;
`;

const SettingsInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const SettingsTitle = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 2px;
`;

const SettingsSubtitle = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.secondaryText};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const SettingsAction = styled.div`
  display: flex;
  align-items: center;
  color: ${props => props.theme.colors.secondaryText};
`;

const ProfileSection = styled.div`
  padding: 24px 16px;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary}, ${props => props.theme.colors.primaryDark || props.theme.colors.primary});
  color: white;
  margin-bottom: 8px;
`;

const ProfileContainer = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 16px;
`;

const ProfileAvatar = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  font-weight: 600;
  margin-right: 20px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const ProfileInfo = styled.div`
  flex: 1;
`;

const ProfileName = styled.div`
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
  cursor: pointer;
  
  &:hover {
    opacity: 0.8;
  }
`;

const ProfileStatus = styled.div`
  font-size: 16px;
  opacity: 0.9;
  cursor: pointer;
  
  &:hover {
    opacity: 0.7;
  }
`;

const ProfileUsername = styled.div`
  font-size: 14px;
  opacity: 0.8;
  margin-top: 4px;
  cursor: pointer;
  
  &:hover {
    opacity: 0.6;
  }
`;

const ComprehensiveAdvancedSettings = ({ onClose, onOpenProfile, onOpenPrivacy, onOpenNotifications, onOpenContacts, onOpenFiles }) => {
  const { currentTheme, setTheme } = useTheme();
  const [settings, setSettings] = useState({});
  const [storageInfo, setStorageInfo] = useState({});
  const [user, setUser] = useState({
    name: 'John Doe',
    username: '@johndoe',
    bio: 'Hey there! I am using GBChat.',
    avatar: null,
    phone: '****** 567 8900'
  });

  useEffect(() => {
    loadSettings();
    loadStorageInfo();
    loadUserProfile();
  }, []);

  const loadSettings = () => {
    const savedSettings = storage.getSettings();
    setSettings(savedSettings);
  };

  const loadStorageInfo = () => {
    const breakdown = storage.getStorageBreakdown();
    setStorageInfo(breakdown);
  };

  const loadUserProfile = () => {
    const userData = storage.getSection('user');
    if (userData) {
      setUser(userData);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getInitials = (name) => {
    return name?.split(' ').map(n => n[0]).join('').toUpperCase() || '?';
  };

  const handleMenuClick = (action) => {
    MobileHaptics.light();
    switch (action) {
      case 'profile':
        if (onOpenProfile) onOpenProfile();
        break;
      case 'privacy':
        if (onOpenPrivacy) onOpenPrivacy();
        break;
      case 'notifications':
        if (onOpenNotifications) onOpenNotifications();
        break;
      case 'contacts':
        if (onOpenContacts) onOpenContacts();
        break;
      case 'files':
        if (onOpenFiles) onOpenFiles();
        break;
      default:
        console.log('Opening:', action);
    }
  };

  return (
    <SettingsContainer theme={currentTheme}>
      <SettingsHeader theme={currentTheme}>
        <HeaderButton onClick={onClose}>
          <FaTimes />
        </HeaderButton>
        <HeaderTitle>Settings</HeaderTitle>
        <HeaderButton onClick={() => {}}>
          <FaCheck />
        </HeaderButton>
      </SettingsHeader>

      <SettingsContent>
        <ProfileSection theme={currentTheme}>
          <ProfileContainer>
            <ProfileAvatar onClick={() => handleMenuClick('profile')}>
              {user.avatar ? (
                <img src={user.avatar} alt={user.name} />
              ) : (
                getInitials(user.name)
              )}
            </ProfileAvatar>
            <ProfileInfo>
              <ProfileName onClick={() => handleMenuClick('profile')}>{user.name}</ProfileName>
              <ProfileStatus onClick={() => handleMenuClick('profile')}>{user.bio}</ProfileStatus>
              <ProfileUsername onClick={() => handleMenuClick('profile')}>{user.username}</ProfileUsername>
            </ProfileInfo>
          </ProfileContainer>
        </ProfileSection>

        <SettingsSection theme={currentTheme}>
          <SectionHeader theme={currentTheme}>Account & Profile</SectionHeader>
          
          <SettingsItem theme={currentTheme} onClick={() => handleMenuClick('profile')}>
            <SettingsIcon color="#25d366" theme={currentTheme}>
              <FaUser />
            </SettingsIcon>
            <SettingsInfo>
              <SettingsTitle theme={currentTheme}>Profile</SettingsTitle>
              <SettingsSubtitle theme={currentTheme}>Name, photo, bio, username</SettingsSubtitle>
            </SettingsInfo>
            <SettingsAction theme={currentTheme}>
              <FaChevronRight />
            </SettingsAction>
          </SettingsItem>

          <SettingsItem theme={currentTheme} onClick={() => handleMenuClick('privacy')}>
            <SettingsIcon color="#ff6b6b" theme={currentTheme}>
              <FaLock />
            </SettingsIcon>
            <SettingsInfo>
              <SettingsTitle theme={currentTheme}>Privacy & Security</SettingsTitle>
              <SettingsSubtitle theme={currentTheme}>Block contacts, disappearing messages</SettingsSubtitle>
            </SettingsInfo>
            <SettingsAction theme={currentTheme}>
              <FaChevronRight />
            </SettingsAction>
          </SettingsItem>
        </SettingsSection>

        <SettingsSection theme={currentTheme}>
          <SectionHeader theme={currentTheme}>App Preferences</SectionHeader>
          
          <SettingsItem theme={currentTheme} onClick={() => handleMenuClick('notifications')}>
            <SettingsIcon color="#ffa726" theme={currentTheme}>
              <FaBell />
            </SettingsIcon>
            <SettingsInfo>
              <SettingsTitle theme={currentTheme}>Notifications</SettingsTitle>
              <SettingsSubtitle theme={currentTheme}>Message, group & call tones</SettingsSubtitle>
            </SettingsInfo>
            <SettingsAction theme={currentTheme}>
              <FaChevronRight />
            </SettingsAction>
          </SettingsItem>

          <SettingsItem theme={currentTheme} onClick={() => window.handleNavigation && window.handleNavigation('theme')}>
            <SettingsIcon color="#9c27b0" theme={currentTheme}>
              <FaPalette />
            </SettingsIcon>
            <SettingsInfo>
              <SettingsTitle theme={currentTheme}>Themes</SettingsTitle>
              <SettingsSubtitle theme={currentTheme}>Dark, light, auto</SettingsSubtitle>
            </SettingsInfo>
            <SettingsAction theme={currentTheme}>
              <FaChevronRight />
            </SettingsAction>
          </SettingsItem>

          <SettingsItem theme={currentTheme} onClick={() => window.handleNavigation && window.handleNavigation('fonts')}>
            <SettingsIcon color="#2196f3" theme={currentTheme}>
              <FaFont />
            </SettingsIcon>
            <SettingsInfo>
              <SettingsTitle theme={currentTheme}>Fonts</SettingsTitle>
              <SettingsSubtitle theme={currentTheme}>Font size and style</SettingsSubtitle>
            </SettingsInfo>
            <SettingsAction theme={currentTheme}>
              <FaChevronRight />
            </SettingsAction>
          </SettingsItem>

          <SettingsItem theme={currentTheme} onClick={() => window.handleNavigation && window.handleNavigation('chatStyle')}>
            <SettingsIcon color="#4caf50" theme={currentTheme}>
              <FaCommentAlt />
            </SettingsIcon>
            <SettingsInfo>
              <SettingsTitle theme={currentTheme}>Chat Style</SettingsTitle>
              <SettingsSubtitle theme={currentTheme}>Wallpaper and bubble style</SettingsSubtitle>
            </SettingsInfo>
            <SettingsAction theme={currentTheme}>
              <FaChevronRight />
            </SettingsAction>
          </SettingsItem>
        </SettingsSection>

        <SettingsSection theme={currentTheme}>
          <SectionHeader theme={currentTheme}>Data & Files</SectionHeader>
          
          <SettingsItem theme={currentTheme} onClick={() => handleMenuClick('contacts')}>
            <SettingsIcon color="#607d8b" theme={currentTheme}>
              <FaAddressBook />
            </SettingsIcon>
            <SettingsInfo>
              <SettingsTitle theme={currentTheme}>Contacts</SettingsTitle>
              <SettingsSubtitle theme={currentTheme}>Manage your contacts</SettingsSubtitle>
            </SettingsInfo>
            <SettingsAction theme={currentTheme}>
              <FaChevronRight />
            </SettingsAction>
          </SettingsItem>

          <SettingsItem theme={currentTheme} onClick={() => handleMenuClick('files')}>
            <SettingsIcon color="#795548" theme={currentTheme}>
              <FaFolder />
            </SettingsIcon>
            <SettingsInfo>
              <SettingsTitle theme={currentTheme}>File Manager</SettingsTitle>
              <SettingsSubtitle theme={currentTheme}>Browse and manage files</SettingsSubtitle>
            </SettingsInfo>
            <SettingsAction theme={currentTheme}>
              <FaChevronRight />
            </SettingsAction>
          </SettingsItem>

          <SettingsItem theme={currentTheme} onClick={() => window.handleNavigation && window.handleNavigation('storage')}>
            <SettingsIcon color="#ff5722" theme={currentTheme}>
              <FaDatabase />
            </SettingsIcon>
            <SettingsInfo>
              <SettingsTitle theme={currentTheme}>Data & Storage</SettingsTitle>
              <SettingsSubtitle theme={currentTheme}>{formatFileSize(storageInfo.total || 0)} used</SettingsSubtitle>
            </SettingsInfo>
            <SettingsAction theme={currentTheme}>
              <FaChevronRight />
            </SettingsAction>
          </SettingsItem>
        </SettingsSection>

        <SettingsSection theme={currentTheme}>
          <SectionHeader theme={currentTheme}>Advanced Features</SectionHeader>
          
          <SettingsItem theme={currentTheme} onClick={() => window.handleNavigation && window.handleNavigation('dnd')}>
            <SettingsIcon color="#ff9800" theme={currentTheme}>
              <FaBellSlash />
            </SettingsIcon>
            <SettingsInfo>
              <SettingsTitle theme={currentTheme}>Do Not Disturb</SettingsTitle>
              <SettingsSubtitle theme={currentTheme}>Quiet hours and exceptions</SettingsSubtitle>
            </SettingsInfo>
            <SettingsAction theme={currentTheme}>
              <FaChevronRight />
            </SettingsAction>
          </SettingsItem>

          <SettingsItem theme={currentTheme} onClick={() => window.handleNavigation && window.handleNavigation('ghost')}>
            <SettingsIcon color="#673ab7" theme={currentTheme}>
              <FaGhost />
            </SettingsIcon>
            <SettingsInfo>
              <SettingsTitle theme={currentTheme}>Ghost Mode</SettingsTitle>
              <SettingsSubtitle theme={currentTheme}>Hide online status</SettingsSubtitle>
            </SettingsInfo>
            <SettingsAction theme={currentTheme}>
              <FaChevronRight />
            </SettingsAction>
          </SettingsItem>

          <SettingsItem theme={currentTheme} onClick={() => window.handleNavigation && window.handleNavigation('airplane')}>
            <SettingsIcon color="#607d8b" theme={currentTheme}>
              <FaPlane />
            </SettingsIcon>
            <SettingsInfo>
              <SettingsTitle theme={currentTheme}>Airplane Mode</SettingsTitle>
              <SettingsSubtitle theme={currentTheme}>Offline mode settings</SettingsSubtitle>
            </SettingsInfo>
            <SettingsAction theme={currentTheme}>
              <FaChevronRight />
            </SettingsAction>
          </SettingsItem>
        </SettingsSection>
      </SettingsContent>
    </SettingsContainer>
  );
};

export default ComprehensiveAdvancedSettings;
