import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  FaG<PERSON>t, 
  FaToggle<PERSON>n, 
  Fa<PERSON><PERSON><PERSON><PERSON>ff, 
  FaEye, 
  FaEyeSlash, 
  FaCheck, 
  FaClock,
  FaUserFriends,
  FaInfoCircle,
  FaExclamationTriangle
} from 'react-icons/fa';

const GhostModeContainer = styled.div`
  padding: 16px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
`;

const SettingsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const SettingItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
`;

const SettingInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const IconWrapper = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.color || props.theme.colors.primary}20;
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    color: ${props => props.color || props.theme.colors.primary};
    font-size: 18px;
  }
`;

const SettingText = styled.div`
  display: flex;
  flex-direction: column;
`;

const SettingName = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  font-size: 0.95rem;
`;

const SettingDescription = styled.div`
  color: ${props => props.theme.colors.secondaryText};
  font-size: 0.8rem;
  margin-top: 4px;
`;

const ToggleButton = styled.div`
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.secondaryText};
  font-size: 1.5rem;
  cursor: pointer;
`;

const InfoBox = styled.div`
  margin-top: 20px;
  padding: 15px;
  background-color: ${props => props.theme.colors.primary}10;
  border-left: 4px solid ${props => props.theme.colors.primary};
  border-radius: 4px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
`;

const InfoIcon = styled.div`
  color: ${props => props.theme.colors.primary};
  font-size: 18px;
  margin-top: 2px;
`;

const InfoText = styled.div`
  color: ${props => props.theme.colors.text};
  font-size: 14px;
  line-height: 1.5;
`;

const WarningBox = styled.div`
  margin-top: 20px;
  padding: 15px;
  background-color: #FFF3CD;
  border-left: 4px solid #FFC107;
  border-radius: 4px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
`;

const WarningIcon = styled.div`
  color: #FFC107;
  font-size: 18px;
  margin-top: 2px;
`;

const WarningText = styled.div`
  color: #856404;
  font-size: 14px;
  line-height: 1.5;
`;

const ExceptionsList = styled.div`
  margin-top: 20px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const ExceptionsTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${props => props.theme.colors.text};
  font-weight: 500;
  margin-bottom: 12px;
  
  svg {
    color: ${props => props.theme.colors.primary};
  }
`;

const ExceptionItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  
  &:last-child {
    border-bottom: none;
  }
`;

const ExceptionName = styled.div`
  color: ${props => props.theme.colors.text};
  font-size: 14px;
`;

const RemoveButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.danger || '#e74c3c'};
  cursor: pointer;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  
  &:hover {
    background-color: ${props => props.theme.colors.danger || '#e74c3c'}10;
  }
`;

const AddExceptionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 10px;
  margin-top: 10px;
  background-color: ${props => props.theme.colors.secondary};
  border: 1px dashed ${props => props.theme.colors.border};
  border-radius: 4px;
  color: ${props => props.theme.colors.text};
  cursor: pointer;
  
  &:hover {
    background-color: ${props => props.theme.colors.primary}10;
  }
`;

const GhostModeSettings = ({ theme }) => {
  const [settings, setSettings] = useState({
    ghostModeEnabled: false,
    hideLastSeen: true,
    hideTypingIndicator: true,
    hideReadReceipts: true,
    hideOnlineStatus: true,
    scheduleEnabled: false,
    startTime: '22:00',
    endTime: '07:00'
  });
  
  const [exceptions] = useState([
    { id: 1, name: 'Family Group' },
    { id: 2, name: 'Best Friend' }
  ]);
  
  const toggleSetting = (setting) => {
    if (setting === 'ghostModeEnabled') {
      // If enabling ghost mode, enable all privacy settings
      if (!settings.ghostModeEnabled) {
        setSettings(prev => ({
          ...prev,
          ghostModeEnabled: true,
          hideLastSeen: true,
          hideTypingIndicator: true,
          hideReadReceipts: true,
          hideOnlineStatus: true
        }));
      } else {
        // Just disable ghost mode
        setSettings(prev => ({
          ...prev,
          ghostModeEnabled: false
        }));
      }
    } else {
      // Toggle individual setting
      setSettings(prev => ({
        ...prev,
        [setting]: !prev[setting]
      }));
      
      // If all privacy settings are enabled, enable ghost mode
      if (
        !settings.ghostModeEnabled && 
        setting !== 'scheduleEnabled' &&
        (
          (setting === 'hideLastSeen' && settings.hideTypingIndicator && settings.hideReadReceipts && settings.hideOnlineStatus) ||
          (setting === 'hideTypingIndicator' && settings.hideLastSeen && settings.hideReadReceipts && settings.hideOnlineStatus) ||
          (setting === 'hideReadReceipts' && settings.hideLastSeen && settings.hideTypingIndicator && settings.hideOnlineStatus) ||
          (setting === 'hideOnlineStatus' && settings.hideLastSeen && settings.hideTypingIndicator && settings.hideReadReceipts)
        )
      ) {
        setSettings(prev => ({
          ...prev,
          ghostModeEnabled: true
        }));
      }
      
      // If any privacy setting is disabled, disable ghost mode
      if (
        settings.ghostModeEnabled && 
        setting !== 'scheduleEnabled' &&
        (
          (setting === 'hideLastSeen' && settings.hideLastSeen) ||
          (setting === 'hideTypingIndicator' && settings.hideTypingIndicator) ||
          (setting === 'hideReadReceipts' && settings.hideReadReceipts) ||
          (setting === 'hideOnlineStatus' && settings.hideOnlineStatus)
        )
      ) {
        setSettings(prev => ({
          ...prev,
          ghostModeEnabled: false
        }));
      }
    }
  };
  
  const handleTimeChange = (setting, value) => {
    setSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };
  
  return (
    <GhostModeContainer theme={theme}>
      <SectionTitle theme={theme}>Ghost Mode Settings</SectionTitle>
      
      <SettingsList>
        <SettingItem theme={theme} onClick={() => toggleSetting('ghostModeEnabled')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#9C27B0">
              <FaGhost />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Ghost Mode</SettingName>
              <SettingDescription theme={theme}>
                Hide your activity from others
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.ghostModeEnabled} 
            theme={theme}
          >
            {settings.ghostModeEnabled ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('hideLastSeen')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#2196F3">
              <FaClock />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Hide last seen</SettingName>
              <SettingDescription theme={theme}>
                Don't show when you were last online
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.hideLastSeen} 
            theme={theme}
          >
            {settings.hideLastSeen ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('hideTypingIndicator')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#4CAF50">
              <FaEyeSlash />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Hide typing</SettingName>
              <SettingDescription theme={theme}>
                Don't show when you're typing
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.hideTypingIndicator} 
            theme={theme}
          >
            {settings.hideTypingIndicator ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('hideReadReceipts')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#FF9800">
              <FaCheck />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Hide read receipts</SettingName>
              <SettingDescription theme={theme}>
                Don't show when you've read messages
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.hideReadReceipts} 
            theme={theme}
          >
            {settings.hideReadReceipts ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('hideOnlineStatus')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#E91E63">
              <FaEye />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Hide online status</SettingName>
              <SettingDescription theme={theme}>
                Don't show when you're online
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.hideOnlineStatus} 
            theme={theme}
          >
            {settings.hideOnlineStatus ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        <SettingItem theme={theme} onClick={() => toggleSetting('scheduleEnabled')}>
          <SettingInfo>
            <IconWrapper theme={theme} color="#607D8B">
              <FaClock />
            </IconWrapper>
            <SettingText>
              <SettingName theme={theme}>Schedule Ghost Mode</SettingName>
              <SettingDescription theme={theme}>
                Enable Ghost Mode during specific hours
              </SettingDescription>
            </SettingText>
          </SettingInfo>
          <ToggleButton 
            $active={settings.scheduleEnabled} 
            theme={theme}
          >
            {settings.scheduleEnabled ? <FaToggleOn /> : <FaToggleOff />}
          </ToggleButton>
        </SettingItem>
        
        {settings.scheduleEnabled && (
          <SettingItem theme={theme}>
            <SettingInfo>
              <IconWrapper theme={theme} color="#9C27B0">
                <FaGhost />
              </IconWrapper>
              <SettingText>
                <SettingName theme={theme}>Ghost hours</SettingName>
                <SettingDescription theme={theme}>
                  Set when Ghost Mode is active
                </SettingDescription>
              </SettingText>
            </SettingInfo>
            <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
              <input 
                type="time" 
                value={settings.startTime}
                onChange={(e) => handleTimeChange('startTime', e.target.value)}
                style={{ 
                  padding: '8px', 
                  borderRadius: '4px',
                  border: `1px solid ${theme.colors.border}`,
                  backgroundColor: theme.colors.background,
                  color: theme.colors.text
                }}
              />
              <span style={{ color: theme.colors.text }}>to</span>
              <input 
                type="time" 
                value={settings.endTime}
                onChange={(e) => handleTimeChange('endTime', e.target.value)}
                style={{ 
                  padding: '8px', 
                  borderRadius: '4px',
                  border: `1px solid ${theme.colors.border}`,
                  backgroundColor: theme.colors.background,
                  color: theme.colors.text
                }}
              />
            </div>
          </SettingItem>
        )}
      </SettingsList>
      
      <ExceptionsList theme={theme}>
        <ExceptionsTitle theme={theme}>
          <FaUserFriends />
          Ghost Mode Exceptions
        </ExceptionsTitle>
        
        {exceptions.map(exception => (
          <ExceptionItem key={exception.id} theme={theme}>
            <ExceptionName theme={theme}>{exception.name}</ExceptionName>
            <RemoveButton theme={theme}>Remove</RemoveButton>
          </ExceptionItem>
        ))}
        
        <AddExceptionButton theme={theme}>
          + Add Exception
        </AddExceptionButton>
      </ExceptionsList>
      
      <InfoBox theme={theme}>
        <InfoIcon theme={theme}>
          <FaInfoCircle />
        </InfoIcon>
        <InfoText theme={theme}>
          Ghost Mode hides your activity from other users. When enabled, others won't see when you're online, when you've read their messages, or when you're typing.
        </InfoText>
      </InfoBox>
      
      <WarningBox>
        <WarningIcon>
          <FaExclamationTriangle />
        </WarningIcon>
        <WarningText>
          Note: When Ghost Mode is enabled, you also won't be able to see when others are online, typing, or have read your messages.
        </WarningText>
      </WarningBox>
    </GhostModeContainer>
  );
};

export default GhostModeSettings;
