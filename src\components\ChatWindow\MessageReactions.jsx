import React from 'react';
import styled from 'styled-components';
import { 
  FaThum<PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  FaSurprise, 
  FaSadTear, 
  FaAngry,
  FaPlus
} from 'react-icons/fa';

const ReactionsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin-top: 4px;
  gap: 4px;
`;

const ReactionBubble = styled.div`
  display: flex;
  align-items: center;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 12px;
  padding: 2px 6px;
  font-size: 12px;
  cursor: pointer;
  transition: transform 0.2s;
  
  &:hover {
    transform: scale(1.1);
  }
  
  svg {
    margin-right: 3px;
    font-size: 12px;
  }
`;

const ReactionCount = styled.span`
  font-weight: 500;
`;

const ReactionsSelector = styled.div`
  display: ${props => props.$show ? 'flex' : 'none'};
  background-color: ${props => props.theme.colors.background};
  border-radius: 24px;
  padding: 6px;
  position: absolute;
  bottom: 100%;
  left: ${props => props.$isSent ? 'auto' : '0'};
  right: ${props => props.$isSent ? '0' : 'auto'};
  margin-bottom: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 5;
`;

const ReactionButton = styled.button`
  background: none;
  border: none;
  font-size: 18px;
  padding: 6px;
  cursor: pointer;
  color: ${props => props.theme.colors.icon};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s, background-color 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
    transform: scale(1.2);
  }
`;

const AddReactionButton = styled.button`
  background: none;
  border: none;
  font-size: 14px;
  padding: 2px 6px;
  cursor: pointer;
  color: ${props => props.theme.colors.secondaryText};
  border-radius: 12px;
  display: flex;
  align-items: center;
  
  &:hover {
    background-color: ${props => props.theme.colors.secondary};
  }
  
  svg {
    margin-right: 3px;
  }
`;

const reactionIcons = {
  like: <FaThumbsUp />,
  love: <FaHeart style={{ color: '#e74c3c' }} />,
  laugh: <FaLaugh style={{ color: '#f1c40f' }} />,
  wow: <FaSurprise style={{ color: '#3498db' }} />,
  sad: <FaSadTear style={{ color: '#9b59b6' }} />,
  angry: <FaAngry style={{ color: '#e67e22' }} />
};

const MessageReactions = ({ 
  reactions = {}, 
  onAddReaction, 
  isSent, 
  theme,
  showSelector,
  setShowSelector
}) => {
  const handleReactionClick = (type) => {
    onAddReaction(type);
    setShowSelector(false);
  };
  
  const totalReactions = Object.values(reactions).reduce((sum, count) => sum + count, 0);
  
  return (
    <>
      <ReactionsSelector 
        $show={showSelector} 
        $isSent={isSent} 
        theme={theme}
      >
        {Object.keys(reactionIcons).map(type => (
          <ReactionButton 
            key={type} 
            onClick={() => handleReactionClick(type)}
            theme={theme}
          >
            {reactionIcons[type]}
          </ReactionButton>
        ))}
      </ReactionsSelector>
      
      {totalReactions > 0 && (
        <ReactionsContainer>
          {Object.entries(reactions).map(([type, count]) => 
            count > 0 && (
              <ReactionBubble 
                key={type} 
                onClick={() => handleReactionClick(type)}
                theme={theme}
              >
                {reactionIcons[type]}
                <ReactionCount>{count}</ReactionCount>
              </ReactionBubble>
            )
          )}
        </ReactionsContainer>
      )}
      
      {!showSelector && totalReactions === 0 && (
        <AddReactionButton 
          onClick={() => setShowSelector(true)}
          theme={theme}
        >
          <FaPlus />
          React
        </AddReactionButton>
      )}
    </>
  );
};

export default MessageReactions;
