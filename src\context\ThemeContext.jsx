import React, { createContext, useState, useContext, useEffect } from 'react';

// Define available themes
export const themes = {
  default: {
    name: 'Default',
    colors: {
      primary: '#00a884',
      secondary: '#f0f2f5',
      background: '#f8f9fa',
      chatBackground: '#e5ddd5',
      text: '#111b21',
      secondaryText: '#667781',
      sentMessage: '#dcf8c6',
      receivedMessage: '#ffffff',
      icon: '#54656f',
      border: '#dadada',
    }
  },
  dark: {
    name: 'Dark',
    colors: {
      primary: '#00a884',
      secondary: '#222e35',
      background: '#111b21',
      chatBackground: '#0b141a',
      text: '#e9edef',
      secondaryText: '#8696a0',
      sentMessage: '#005c4b',
      receivedMessage: '#202c33',
      icon: '#aebac1',
      border: '#222d34',
    }
  },
  blue: {
    name: 'Blue',
    colors: {
      primary: '#0088cc',
      secondary: '#f0f8ff',
      background: '#e6f2ff',
      chatBackground: '#d1e6ff',
      text: '#0d1b2a',
      secondaryText: '#415a77',
      sentMessage: '#bde0fe',
      receivedMessage: '#ffffff',
      icon: '#1b263b',
      border: '#c8d8e4',
    }
  },
  pink: {
    name: 'Pink',
    colors: {
      primary: '#ff69b4',
      secondary: '#fff0f5',
      background: '#ffeef8',
      chatBackground: '#ffe6f2',
      text: '#4a0635',
      secondaryText: '#9e4675',
      sentMessage: '#ffcce6',
      receivedMessage: '#ffffff',
      icon: '#6d213c',
      border: '#ffc8dd',
    }
  },
  green: {
    name: 'Green',
    colors: {
      primary: '#2e8b57',
      secondary: '#f0fff0',
      background: '#e6ffe6',
      chatBackground: '#d1ffd1',
      text: '#0d260d',
      secondaryText: '#3a5f3a',
      sentMessage: '#c1ffc1',
      receivedMessage: '#ffffff',
      icon: '#1b4d1b',
      border: '#c8e6c9',
    }
  },
  purple: {
    name: 'Purple',
    colors: {
      primary: '#9c27b0',
      secondary: '#f3e5f5',
      background: '#f8f0fc',
      chatBackground: '#ede0f4',
      text: '#2e0039',
      secondaryText: '#7b1fa2',
      sentMessage: '#e1bee7',
      receivedMessage: '#ffffff',
      icon: '#4a148c',
      border: '#d1c4e9',
    }
  },
  orange: {
    name: 'Orange',
    colors: {
      primary: '#ff9800',
      secondary: '#fff3e0',
      background: '#fff8e1',
      chatBackground: '#ffecb3',
      text: '#3e2723',
      secondaryText: '#8d6e63',
      sentMessage: '#ffe0b2',
      receivedMessage: '#ffffff',
      icon: '#e65100',
      border: '#ffe0b2',
    }
  },
  teal: {
    name: 'Teal',
    colors: {
      primary: '#009688',
      secondary: '#e0f2f1',
      background: '#e0f7fa',
      chatBackground: '#b2dfdb',
      text: '#004d40',
      secondaryText: '#00796b',
      sentMessage: '#b2dfdb',
      receivedMessage: '#ffffff',
      icon: '#00695c',
      border: '#b2dfdb',
    }
  },
  red: {
    name: 'Red',
    colors: {
      primary: '#f44336',
      secondary: '#ffebee',
      background: '#ffcdd2',
      chatBackground: '#ffebee',
      text: '#b71c1c',
      secondaryText: '#c62828',
      sentMessage: '#ffcdd2',
      receivedMessage: '#ffffff',
      icon: '#d32f2f',
      border: '#ef9a9a',
    }
  },
  midnight: {
    name: 'Midnight',
    colors: {
      primary: '#3f51b5',
      secondary: '#1a237e',
      background: '#121212',
      chatBackground: '#0a0a0a',
      text: '#e0e0e0',
      secondaryText: '#9e9e9e',
      sentMessage: '#303f9f',
      receivedMessage: '#212121',
      icon: '#7986cb',
      border: '#1a237e',
    }
  },
  nature: {
    name: 'Nature',
    colors: {
      primary: '#8bc34a',
      secondary: '#f1f8e9',
      background: '#f9fbe7',
      chatBackground: '#dcedc8',
      text: '#33691e',
      secondaryText: '#558b2f',
      sentMessage: '#dcedc8',
      receivedMessage: '#ffffff',
      icon: '#689f38',
      border: '#c5e1a5',
    }
  },
};

const ThemeContext = createContext();

export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    const savedDarkMode = localStorage.getItem('whatsapp-dark-mode');
    return savedDarkMode ? JSON.parse(savedDarkMode) : false;
  });

  const [currentTheme, setCurrentTheme] = useState(() => {
    const savedTheme = localStorage.getItem('whatsapp-theme');
    return savedTheme ? JSON.parse(savedTheme) : themes.default;
  });

  useEffect(() => {
    localStorage.setItem('whatsapp-theme', JSON.stringify(currentTheme));
  }, [currentTheme]);

  useEffect(() => {
    localStorage.setItem('whatsapp-dark-mode', JSON.stringify(isDarkMode));
    if (isDarkMode) {
      // If user has selected a light theme but toggles dark mode, switch to dark theme
      if (currentTheme.name !== 'Dark') {
        setCurrentTheme(themes.dark);
      }
    } else {
      // If switching to light mode and current theme is dark, switch to default
      if (currentTheme.name === 'Dark') {
        setCurrentTheme(themes.default);
      }
    }
  }, [isDarkMode]);

  const changeTheme = (themeName) => {
    setCurrentTheme(themes[themeName]);
    // Update dark mode state if switching to/from dark theme
    if (themeName === 'dark') {
      setIsDarkMode(true);
    } else if (isDarkMode) {
      setIsDarkMode(false);
    }
  };

  const toggleDarkMode = () => {
    setIsDarkMode(prev => !prev);
  };

  return (
    <ThemeContext.Provider value={{
      currentTheme,
      changeTheme,
      themes,
      isDarkMode,
      toggleDarkMode
    }}>
      {children}
    </ThemeContext.Provider>
  );
};
