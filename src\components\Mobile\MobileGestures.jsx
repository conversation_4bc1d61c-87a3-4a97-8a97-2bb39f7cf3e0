import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';

const GestureContainer = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
`;

const SwipeIndicator = styled.div`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60px;
  background-color: ${props => props.theme.colors.primary};
  border-radius: 2px;
  opacity: ${props => props.$show ? 0.7 : 0};
  transition: opacity 0.3s ease;
  z-index: 10;
  
  ${props => props.$direction === 'left' && `
    left: 10px;
    animation: ${props.$show ? 'slideLeft 0.5s ease-in-out' : 'none'};
  `}
  
  ${props => props.$direction === 'right' && `
    right: 10px;
    animation: ${props.$show ? 'slideRight 0.5s ease-in-out' : 'none'};
  `}
  
  @keyframes slideLeft {
    0% { transform: translateY(-50%) translateX(20px); opacity: 0; }
    50% { opacity: 0.7; }
    100% { transform: translateY(-50%) translateX(0); opacity: 0.7; }
  }
  
  @keyframes slideRight {
    0% { transform: translateY(-50%) translateX(-20px); opacity: 0; }
    50% { opacity: 0.7; }
    100% { transform: translateY(-50%) translateX(0); opacity: 0.7; }
  }
`;

const PullToRefreshIndicator = styled.div`
  position: absolute;
  top: ${props => props.$offset}px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  opacity: ${props => props.$show ? 1 : 0};
  transition: all 0.3s ease;
  z-index: 10;
  
  ${props => props.$loading && `
    animation: spin 1s linear infinite;
  `}
  
  @keyframes spin {
    from { transform: translateX(-50%) rotate(0deg); }
    to { transform: translateX(-50%) rotate(360deg); }
  }
`;

const MobileGestures = ({ 
  children, 
  theme, 
  onSwipeLeft, 
  onSwipeRight, 
  onPullToRefresh,
  enableSwipe = true,
  enablePullToRefresh = false 
}) => {
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const [swipeDirection, setSwipeDirection] = useState(null);
  const [showSwipeIndicator, setShowSwipeIndicator] = useState(false);
  const [pullOffset, setPullOffset] = useState(0);
  const [isPulling, setIsPulling] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const containerRef = useRef(null);

  const minSwipeDistance = 50;
  const maxPullDistance = 80;

  const onTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
    
    if (enablePullToRefresh && containerRef.current?.scrollTop === 0) {
      setIsPulling(true);
    }
  };

  const onTouchMove = (e) => {
    if (!touchStart) return;
    
    const currentTouch = e.targetTouches[0];
    setTouchEnd(currentTouch.clientX);
    
    if (isPulling && enablePullToRefresh) {
      const pullDistance = Math.max(0, currentTouch.clientY - touchStart);
      setPullOffset(Math.min(pullDistance * 0.5, maxPullDistance));
    }
    
    if (enableSwipe) {
      const distance = touchStart - currentTouch.clientX;
      const isLeftSwipe = distance > minSwipeDistance;
      const isRightSwipe = distance < -minSwipeDistance;
      
      if (isLeftSwipe && swipeDirection !== 'left') {
        setSwipeDirection('left');
        setShowSwipeIndicator(true);
      } else if (isRightSwipe && swipeDirection !== 'right') {
        setSwipeDirection('right');
        setShowSwipeIndicator(true);
      }
    }
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) {
      resetGestures();
      return;
    }
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;
    
    if (enableSwipe) {
      if (isLeftSwipe && onSwipeLeft) {
        onSwipeLeft();
      } else if (isRightSwipe && onSwipeRight) {
        onSwipeRight();
      }
    }
    
    if (isPulling && pullOffset > 50 && onPullToRefresh) {
      setIsRefreshing(true);
      onPullToRefresh().finally(() => {
        setIsRefreshing(false);
        resetGestures();
      });
    } else {
      resetGestures();
    }
  };

  const resetGestures = () => {
    setTouchStart(null);
    setTouchEnd(null);
    setSwipeDirection(null);
    setShowSwipeIndicator(false);
    setPullOffset(0);
    setIsPulling(false);
  };

  useEffect(() => {
    if (showSwipeIndicator) {
      const timer = setTimeout(() => {
        setShowSwipeIndicator(false);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [showSwipeIndicator]);

  return (
    <GestureContainer
      ref={containerRef}
      onTouchStart={onTouchStart}
      onTouchMove={onTouchMove}
      onTouchEnd={onTouchEnd}
      style={{ transform: `translateY(${pullOffset}px)` }}
    >
      {enableSwipe && (
        <>
          <SwipeIndicator
            theme={theme}
            $show={showSwipeIndicator && swipeDirection === 'left'}
            $direction="left"
          />
          <SwipeIndicator
            theme={theme}
            $show={showSwipeIndicator && swipeDirection === 'right'}
            $direction="right"
          />
        </>
      )}
      
      {enablePullToRefresh && (
        <PullToRefreshIndicator
          theme={theme}
          $show={pullOffset > 20}
          $offset={pullOffset - 40}
          $loading={isRefreshing}
        >
          {isRefreshing ? '⟳' : '↓'}
        </PullToRefreshIndicator>
      )}
      
      {children}
    </GestureContainer>
  );
};

export default MobileGestures;
